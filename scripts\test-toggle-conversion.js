// Test script for Toggle component conversion
// Run this in browser console to test all converted Toggle components

console.log('🧪 Testing Toggle Component Conversion...\n');

class ToggleConversionTester {
  constructor() {
    this.testResults = [];
    this.foundToggles = [];
  }

  async runAllTests() {
    console.log('🚀 Starting Toggle Conversion Tests...\n');
    
    try {
      await this.test1_FindAllToggles();
      await this.test2_TestToggleFunctionality();
      await this.test3_TestToggleStates();
      await this.test4_TestToggleAccessibility();
      await this.test5_TestToggleSizes();
      
      this.showResults();
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  async test1_FindAllToggles() {
    this.logTest('Find All Toggle Components');
    
    // Find all toggle switches in the DOM
    const toggles = document.querySelectorAll('[role="switch"]');
    this.foundToggles = Array.from(toggles);
    
    console.log(`Found ${toggles.length} toggle switches`);
    
    // Expected toggles by page
    const expectedToggles = {
      'settings': [
        'Real-time Sync',
        'Bandwidth Limit', 
        'Sync Completion',
        'Sync Errors',
        'System Notifications'
      ],
      'startup': [
        'Startup with Windows',
        'Start Minimized',
        'Minimize to Tray',
        'Close to Tray'
      ],
      'appearance': [
        // AppearanceSettingsPanel already uses Toggle
      ],
      'sync': [
        // SyncSettingsPanel already uses Toggle
      ],
      'create-task': [
        'Delete Extra Files',
        'Preserve Timestamps',
        'Enable Real-time Sync'
      ],
      'login': [
        'Remember Me'
      ]
    };
    
    this.assert(toggles.length > 0, 'Found toggle switches in DOM');
    
    // Log details of each toggle
    toggles.forEach((toggle, index) => {
      const label = this.getToggleLabel(toggle);
      const checked = toggle.getAttribute('aria-checked') === 'true';
      const disabled = toggle.hasAttribute('disabled') || toggle.getAttribute('aria-disabled') === 'true';
      
      console.log(`   ${index + 1}. ${label}: ${checked ? 'ON' : 'OFF'}${disabled ? ' (disabled)' : ''}`);
    });
    
    console.log('✅ Toggle discovery completed\n');
  }

  async test2_TestToggleFunctionality() {
    this.logTest('Test Toggle Functionality');
    
    if (this.foundToggles.length === 0) {
      this.assert(false, 'No toggles found to test');
      return;
    }
    
    let functionalToggles = 0;
    
    for (const toggle of this.foundToggles.slice(0, 3)) { // Test first 3 toggles
      const label = this.getToggleLabel(toggle);
      const originalState = toggle.getAttribute('aria-checked') === 'true';
      
      try {
        // Click the toggle
        toggle.click();
        
        // Wait for state change
        await this.delay(100);
        
        const newState = toggle.getAttribute('aria-checked') === 'true';
        const stateChanged = originalState !== newState;
        
        if (stateChanged) {
          functionalToggles++;
          console.log(`   ✅ ${label}: ${originalState ? 'ON' : 'OFF'} → ${newState ? 'ON' : 'OFF'}`);
          
          // Click back to restore original state
          toggle.click();
          await this.delay(100);
        } else {
          console.log(`   ❌ ${label}: No state change detected`);
        }
        
      } catch (error) {
        console.log(`   ❌ ${label}: Error testing - ${error.message}`);
      }
    }
    
    this.assert(functionalToggles > 0, `${functionalToggles} toggles are functional`);
    console.log('✅ Toggle functionality test completed\n');
  }

  async test3_TestToggleStates() {
    this.logTest('Test Toggle States');
    
    const stateTests = [
      { state: 'checked', selector: '[aria-checked="true"]' },
      { state: 'unchecked', selector: '[aria-checked="false"]' },
      { state: 'disabled', selector: '[disabled], [aria-disabled="true"]' }
    ];
    
    stateTests.forEach(test => {
      const elements = document.querySelectorAll(`[role="switch"]${test.selector}`);
      console.log(`   ${test.state}: ${elements.length} toggles`);
      this.assert(true, `Found ${elements.length} ${test.state} toggles`);
    });
    
    console.log('✅ Toggle states test completed\n');
  }

  async test4_TestToggleAccessibility() {
    this.logTest('Test Toggle Accessibility');
    
    let accessibleToggles = 0;
    
    this.foundToggles.forEach(toggle => {
      const label = this.getToggleLabel(toggle);
      const hasRole = toggle.getAttribute('role') === 'switch';
      const hasAriaChecked = toggle.hasAttribute('aria-checked');
      const hasLabel = !!label;
      const isKeyboardAccessible = toggle.tabIndex >= 0 || toggle.hasAttribute('tabindex');
      
      const accessibilityScore = [hasRole, hasAriaChecked, hasLabel, isKeyboardAccessible].filter(Boolean).length;
      
      if (accessibilityScore >= 3) {
        accessibleToggles++;
        console.log(`   ✅ ${label}: ${accessibilityScore}/4 accessibility features`);
      } else {
        console.log(`   ⚠️ ${label}: ${accessibilityScore}/4 accessibility features`);
      }
    });
    
    this.assert(accessibleToggles > 0, `${accessibleToggles} toggles are accessible`);
    console.log('✅ Toggle accessibility test completed\n');
  }

  async test5_TestToggleSizes() {
    this.logTest('Test Toggle Sizes');
    
    const sizeClasses = {
      'sm': ['w-8', 'h-4'],
      'md': ['w-11', 'h-6'], 
      'lg': ['w-14', 'h-7']
    };
    
    const sizeCounts = { sm: 0, md: 0, lg: 0, unknown: 0 };
    
    this.foundToggles.forEach(toggle => {
      const label = this.getToggleLabel(toggle);
      let detectedSize = 'unknown';
      
      Object.entries(sizeClasses).forEach(([size, classes]) => {
        if (classes.every(cls => toggle.classList.contains(cls))) {
          detectedSize = size;
        }
      });
      
      sizeCounts[detectedSize]++;
      console.log(`   ${label}: ${detectedSize} size`);
    });
    
    console.log(`   Size distribution: SM=${sizeCounts.sm}, MD=${sizeCounts.md}, LG=${sizeCounts.lg}, Unknown=${sizeCounts.unknown}`);
    
    this.assert(sizeCounts.unknown < this.foundToggles.length, 'Most toggles have recognizable sizes');
    console.log('✅ Toggle sizes test completed\n');
  }

  getToggleLabel(toggle) {
    // Try different methods to get toggle label
    const ariaLabel = toggle.getAttribute('aria-label');
    if (ariaLabel) return ariaLabel;
    
    const ariaLabelledBy = toggle.getAttribute('aria-labelledby');
    if (ariaLabelledBy) {
      const labelElement = document.getElementById(ariaLabelledBy);
      if (labelElement) return labelElement.textContent.trim();
    }
    
    // Look for nearby label text
    const parent = toggle.closest('.flex, .space-y-4, .toggle-container');
    if (parent) {
      const labelText = parent.querySelector('label, .text-sm, .font-medium');
      if (labelText) return labelText.textContent.trim();
    }
    
    return 'Unknown Toggle';
  }

  logTest(testName) {
    console.log(`🔸 Test: ${testName}`);
    console.log('─'.repeat(40));
  }

  assert(condition, message) {
    const result = {
      test: message,
      passed: condition,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    if (condition) {
      console.log(`✅ ${message}`);
    } else {
      console.log(`❌ ${message}`);
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  showResults() {
    console.log('\n📊 Test Results Summary');
    console.log('═'.repeat(50));
    
    const passed = this.testResults.filter(r => r.passed).length;
    const total = this.testResults.length;
    
    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${total - passed}`);
    console.log(`Success Rate: ${Math.round((passed / total) * 100)}%`);
    
    console.log('\n📋 Detailed Results:');
    this.testResults.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.test}`);
    });
    
    if (passed === total) {
      console.log('\n🎉 All tests passed! Toggle conversion is successful.');
    } else {
      console.log('\n⚠️ Some tests failed. Check the implementation.');
    }
    
    console.log('\n📈 Conversion Summary:');
    console.log(`   🔄 Converted ${this.foundToggles.length} checkboxes to Toggle components`);
    console.log('   ✅ Modern toggle switch design');
    console.log('   ♿ Improved accessibility');
    console.log('   🎨 Consistent UI across the app');
  }
}

// Manual testing functions
window.toggleConversionTester = {
  // Run full test suite
  runTests: async () => {
    const tester = new ToggleConversionTester();
    await tester.runAllTests();
  },

  // Find all toggles in current page
  findToggles: () => {
    const toggles = document.querySelectorAll('[role="switch"]');
    console.log(`🔍 Found ${toggles.length} toggle switches:`);
    
    toggles.forEach((toggle, index) => {
      const label = toggle.getAttribute('aria-label') || 
                   toggle.closest('.flex')?.querySelector('label, .text-sm')?.textContent?.trim() || 
                   'Unknown';
      const checked = toggle.getAttribute('aria-checked') === 'true';
      const disabled = toggle.hasAttribute('disabled');
      
      console.log(`   ${index + 1}. ${label}: ${checked ? 'ON' : 'OFF'}${disabled ? ' (disabled)' : ''}`);
    });
    
    return toggles;
  },

  // Test specific toggle by index
  testToggle: async (index) => {
    const toggles = document.querySelectorAll('[role="switch"]');
    if (index >= toggles.length) {
      console.log(`❌ Toggle index ${index} not found. Found ${toggles.length} toggles.`);
      return;
    }
    
    const toggle = toggles[index];
    const label = toggle.getAttribute('aria-label') || `Toggle ${index}`;
    const originalState = toggle.getAttribute('aria-checked') === 'true';
    
    console.log(`🧪 Testing ${label}...`);
    console.log(`   Original state: ${originalState ? 'ON' : 'OFF'}`);
    
    // Click toggle
    toggle.click();
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const newState = toggle.getAttribute('aria-checked') === 'true';
    console.log(`   New state: ${newState ? 'ON' : 'OFF'}`);
    console.log(`   State changed: ${originalState !== newState ? '✅ Yes' : '❌ No'}`);
    
    // Restore original state
    if (originalState !== newState) {
      toggle.click();
    }
  },

  // Check conversion status
  checkConversion: () => {
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    const toggles = document.querySelectorAll('[role="switch"]');
    
    console.log('🔄 Conversion Status:');
    console.log(`   📦 Remaining checkboxes: ${checkboxes.length}`);
    console.log(`   🎛️ Toggle switches: ${toggles.length}`);
    
    if (checkboxes.length > 0) {
      console.log('\n📋 Remaining checkboxes:');
      checkboxes.forEach((checkbox, index) => {
        const label = checkbox.labels?.[0]?.textContent || 
                     checkbox.closest('label')?.textContent ||
                     checkbox.getAttribute('aria-label') ||
                     'Unknown checkbox';
        console.log(`   ${index + 1}. ${label.trim()}`);
      });
    }
    
    return { checkboxes: checkboxes.length, toggles: toggles.length };
  }
};

// Auto-run tests
console.log('🎯 Toggle Conversion Tester loaded!');
console.log('📝 Available commands:');
console.log('   - window.toggleConversionTester.runTests()');
console.log('   - window.toggleConversionTester.findToggles()');
console.log('   - window.toggleConversionTester.testToggle(index)');
console.log('   - window.toggleConversionTester.checkConversion()');
console.log('');
