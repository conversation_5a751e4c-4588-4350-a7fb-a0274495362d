const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { getDatabase } = require('../database/init');

class FileVersioning {
  constructor() {
    this.versionsDir = path.join(process.cwd(), 'data', 'versions');
    this.maxVersions = 10; // Keep last 10 versions
    this.init();
  }

  async init() {
    try {
      await fs.mkdir(this.versionsDir, { recursive: true });
    } catch (error) {
      console.error('Failed to create versions directory:', error);
    }
  }

  // Generate file hash for version identification
  async generateFileHash(filePath) {
    try {
      const fileBuffer = await fs.readFile(filePath);
      return crypto.createHash('sha256').update(fileBuffer).digest('hex');
    } catch (error) {
      console.error('Failed to generate file hash:', error);
      return null;
    }
  }

  // Create a new version of a file
  async createVersion(taskId, filePath, relativePath, reason = 'sync') {
    try {
      const db = getDatabase();
      const fileHash = await this.generateFileHash(filePath);
      
      if (!fileHash) return null;

      // Check if this version already exists
      const existingVersion = await db.query(
        'SELECT id FROM file_versions WHERE file_hash = ? AND task_id = ?',
        [fileHash, taskId]
      );

      if (existingVersion.rows.length > 0) {
        console.log(`Version already exists for ${relativePath}`);
        return existingVersion.rows[0].id;
      }

      // Create version directory for this task
      const taskVersionDir = path.join(this.versionsDir, taskId.toString());
      await fs.mkdir(taskVersionDir, { recursive: true });

      // Generate unique version filename
      const timestamp = Date.now();
      const ext = path.extname(relativePath);
      const baseName = path.basename(relativePath, ext);
      const versionFileName = `${baseName}_${timestamp}_${fileHash.substring(0, 8)}${ext}`;
      const versionPath = path.join(taskVersionDir, versionFileName);

      // Copy file to versions directory
      await fs.copyFile(filePath, versionPath);

      // Get file stats
      const stats = await fs.stat(filePath);

      // Insert version record
      const result = await db.query(
        `INSERT INTO file_versions 
         (task_id, file_path, version_path, file_hash, file_size, reason, created_at)
         VALUES (?, ?, ?, ?, ?, ?, datetime('now'))`,
        [taskId, relativePath, versionPath, fileHash, stats.size, reason]
      );

      const versionId = result.insertId || result.rows[0]?.id;

      // Clean up old versions
      await this.cleanupOldVersions(taskId, relativePath);

      console.log(`✅ Created version for ${relativePath}: ${versionFileName}`);
      return versionId;

    } catch (error) {
      console.error('Failed to create file version:', error);
      return null;
    }
  }

  // Get all versions of a file
  async getFileVersions(taskId, relativePath) {
    try {
      const db = getDatabase();
      const result = await db.query(
        `SELECT * FROM file_versions 
         WHERE task_id = ? AND file_path = ? 
         ORDER BY created_at DESC`,
        [taskId, relativePath]
      );

      return result.rows.map(version => ({
        id: version.id,
        filePath: version.file_path,
        versionPath: version.version_path,
        fileHash: version.file_hash,
        fileSize: version.file_size,
        reason: version.reason,
        createdAt: version.created_at
      }));
    } catch (error) {
      console.error('Failed to get file versions:', error);
      return [];
    }
  }

  // Restore a specific version
  async restoreVersion(versionId, targetPath) {
    try {
      const db = getDatabase();
      const result = await db.query(
        'SELECT * FROM file_versions WHERE id = ?',
        [versionId]
      );

      if (result.rows.length === 0) {
        throw new Error('Version not found');
      }

      const version = result.rows[0];
      
      // Check if version file exists
      try {
        await fs.access(version.version_path);
      } catch (error) {
        throw new Error('Version file not found on disk');
      }

      // Create target directory if needed
      const targetDir = path.dirname(targetPath);
      await fs.mkdir(targetDir, { recursive: true });

      // Copy version file to target
      await fs.copyFile(version.version_path, targetPath);

      console.log(`✅ Restored version ${versionId} to ${targetPath}`);
      return true;

    } catch (error) {
      console.error('Failed to restore version:', error);
      return false;
    }
  }

  // Clean up old versions (keep only maxVersions)
  async cleanupOldVersions(taskId, relativePath) {
    try {
      const db = getDatabase();
      
      // Get all versions for this file
      const versions = await this.getFileVersions(taskId, relativePath);
      
      if (versions.length <= this.maxVersions) {
        return; // No cleanup needed
      }

      // Delete old versions
      const versionsToDelete = versions.slice(this.maxVersions);
      
      for (const version of versionsToDelete) {
        try {
          // Delete file from disk
          await fs.unlink(version.versionPath);
          
          // Delete from database
          await db.query('DELETE FROM file_versions WHERE id = ?', [version.id]);
          
          console.log(`🗑️ Cleaned up old version: ${version.id}`);
        } catch (error) {
          console.error(`Failed to delete version ${version.id}:`, error);
        }
      }

    } catch (error) {
      console.error('Failed to cleanup old versions:', error);
    }
  }

  // Get version statistics
  async getVersionStats(taskId) {
    try {
      const db = getDatabase();
      const result = await db.query(
        `SELECT 
           COUNT(*) as total_versions,
           COUNT(DISTINCT file_path) as unique_files,
           SUM(file_size) as total_size,
           MAX(created_at) as latest_version
         FROM file_versions 
         WHERE task_id = ?`,
        [taskId]
      );

      return result.rows[0] || {
        total_versions: 0,
        unique_files: 0,
        total_size: 0,
        latest_version: null
      };
    } catch (error) {
      console.error('Failed to get version stats:', error);
      return {
        total_versions: 0,
        unique_files: 0,
        total_size: 0,
        latest_version: null
      };
    }
  }

  // Delete all versions for a task
  async deleteTaskVersions(taskId) {
    try {
      const db = getDatabase();
      
      // Get all versions for this task
      const result = await db.query(
        'SELECT version_path FROM file_versions WHERE task_id = ?',
        [taskId]
      );

      // Delete version files from disk
      for (const version of result.rows) {
        try {
          await fs.unlink(version.version_path);
        } catch (error) {
          console.error(`Failed to delete version file ${version.version_path}:`, error);
        }
      }

      // Delete version directory if empty
      const taskVersionDir = path.join(this.versionsDir, taskId.toString());
      try {
        await fs.rmdir(taskVersionDir);
      } catch (error) {
        // Directory might not be empty or not exist
      }

      // Delete from database
      await db.query('DELETE FROM file_versions WHERE task_id = ?', [taskId]);

      console.log(`🗑️ Deleted all versions for task ${taskId}`);
      return true;

    } catch (error) {
      console.error('Failed to delete task versions:', error);
      return false;
    }
  }
}

module.exports = FileVersioning;
