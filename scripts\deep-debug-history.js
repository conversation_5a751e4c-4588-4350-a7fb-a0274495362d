// Deep debug script for history issues
// Run this to find exactly why history is not showing

const axios = require('axios');
const io = require('socket.io-client');

const baseURL = 'http://localhost:5002/api'; // Desktop API
const socketURL = 'http://localhost:5002';

class HistoryDeepDebugger {
  constructor() {
    this.token = null;
    this.socket = null;
    this.events = [];
  }

  async runDeepDebug() {
    console.log('🔍 Deep History Debug - Finding the Root Cause\n');
    
    try {
      await this.step1_Login();
      await this.step2_CheckDatabase();
      await this.step3_TestHistoryAPI();
      await this.step4_CheckRealtimeEngine();
      await this.step5_MonitorEvents();
      await this.step6_TestManualHistory();
      
      this.analyzeFindings();
    } catch (error) {
      console.error('❌ Deep debug failed:', error);
    }
  }

  async step1_Login() {
    console.log('1. 🔐 Authenticating...');
    
    try {
      const response = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      this.token = response.data.token;
      console.log('✅ Login successful');
    } catch (error) {
      console.log('❌ Login failed:', error.message);
      throw error;
    }
  }

  async step2_CheckDatabase() {
    console.log('\n2. 🗄️ Checking Database Structure...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };
    
    try {
      // Check if history table exists and has data
      const response = await axios.get(`${baseURL}/sync/history?limit=1`, { headers });
      console.log('✅ History API accessible');
      console.log(`📊 Response structure:`, Object.keys(response.data));
      
      if (response.data.history) {
        console.log(`📋 History array length: ${response.data.history.length}`);
        if (response.data.history.length > 0) {
          console.log(`📄 Sample entry:`, response.data.history[0]);
        }
      } else {
        console.log('❌ No history property in response');
      }
      
    } catch (error) {
      console.log('❌ Database check failed:', error.message);
      if (error.response) {
        console.log('Response status:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }
  }

  async step3_TestHistoryAPI() {
    console.log('\n3. 🔌 Testing History API Endpoints...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };
    
    const endpoints = [
      { url: `${baseURL}/sync/history`, desc: 'All history' },
      { url: `${baseURL}/sync/history?limit=10`, desc: 'Limited history' },
      { url: `${baseURL}/sync/history?status=completed`, desc: 'Completed only' },
      { url: `${baseURL}/sync/tasks`, desc: 'All tasks' }
    ];
    
    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(endpoint.url, { headers });
        console.log(`✅ ${endpoint.desc}: ${response.status}`);
        
        if (endpoint.url.includes('history')) {
          const count = response.data.history?.length || 0;
          console.log(`   📊 Entries: ${count}`);
          
          if (count > 0) {
            const entry = response.data.history[0];
            console.log(`   📄 Latest: Task ${entry.task_id}, Status: ${entry.status}, Files: ${entry.files_processed}`);
          }
        } else if (endpoint.url.includes('tasks')) {
          const count = response.data.tasks?.length || 0;
          console.log(`   📊 Tasks: ${count}`);
        }
        
      } catch (error) {
        console.log(`❌ ${endpoint.desc}: ${error.message}`);
      }
    }
  }

  async step4_CheckRealtimeEngine() {
    console.log('\n4. ⚡ Checking Real-time Engine...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };
    
    try {
      // Get tasks to find real-time enabled ones
      const tasksResponse = await axios.get(`${baseURL}/sync/tasks`, { headers });
      const tasks = tasksResponse.data.tasks || [];
      
      console.log(`📋 Total tasks: ${tasks.length}`);
      
      const realtimeTasks = tasks.filter(task => {
        const options = typeof task.options === 'string' ? JSON.parse(task.options) : task.options;
        return options?.enableRealtime;
      });
      
      console.log(`⚡ Real-time tasks: ${realtimeTasks.length}`);
      
      if (realtimeTasks.length > 0) {
        const task = realtimeTasks[0];
        console.log(`🎯 Testing with task: ${task.name} (ID: ${task.id})`);
        
        // Check task status
        console.log(`   Status: ${task.status}`);
        console.log(`   Last sync: ${task.last_sync || 'Never'}`);
        console.log(`   Files count: ${task.files_count || 0}`);
        
        // Try to get task-specific history
        const historyResponse = await axios.get(
          `${baseURL}/sync/history?taskId=${task.id}`, 
          { headers }
        );
        
        const taskHistory = historyResponse.data.history || [];
        console.log(`   📊 Task history entries: ${taskHistory.length}`);
        
        if (taskHistory.length > 0) {
          console.log(`   📄 Latest entry: ${taskHistory[0].status} at ${taskHistory[0].started_at}`);
        }
        
      } else {
        console.log('❌ No real-time tasks found');
        console.log('💡 Create a task with real-time enabled first');
      }
      
    } catch (error) {
      console.log('❌ Real-time engine check failed:', error.message);
    }
  }

  async step5_MonitorEvents() {
    console.log('\n5. 📡 Monitoring Socket Events...');
    
    return new Promise((resolve) => {
      this.socket = io(socketURL, {
        auth: { token: this.token },
        transports: ['websocket']
      });

      this.socket.on('connect', () => {
        console.log('✅ Socket connected:', this.socket.id);
        
        // Listen for all possible events
        const events = [
          'sync-started',
          'sync-progress', 
          'sync-completed',
          'sync-error',
          'realtime-sync-started',
          'realtime-sync-processing',
          'realtime-sync-completed',
          'realtime-sync-stopped',
          'file-change-detected'
        ];

        events.forEach(event => {
          this.socket.on(event, (data) => {
            const timestamp = new Date().toISOString();
            this.events.push({ event, data, timestamp });
            console.log(`📡 ${timestamp}: ${event}`, data);
          });
        });
        
        console.log('👂 Listening for events... (waiting 5 seconds)');
        
        setTimeout(() => {
          console.log(`📊 Total events captured: ${this.events.length}`);
          resolve();
        }, 5000);
      });

      this.socket.on('connect_error', (error) => {
        console.log('❌ Socket connection failed:', error);
        resolve();
      });
    });
  }

  async step6_TestManualHistory() {
    console.log('\n6. 🧪 Testing Manual History Creation...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };
    
    try {
      // Get a task to test with
      const tasksResponse = await axios.get(`${baseURL}/sync/tasks`, { headers });
      const tasks = tasksResponse.data.tasks || [];
      
      if (tasks.length === 0) {
        console.log('❌ No tasks found for testing');
        return;
      }
      
      const task = tasks[0];
      console.log(`🎯 Testing manual sync with task: ${task.name}`);
      
      // Try to start a regular sync
      try {
        const startResponse = await axios.post(
          `${baseURL}/sync/tasks/${task.id}/start`, 
          {}, 
          { headers }
        );
        
        console.log('✅ Manual sync started:', startResponse.data);
        
        // Wait a bit for completion
        await this.delay(3000);
        
        // Check if history was created
        const historyResponse = await axios.get(
          `${baseURL}/sync/history?taskId=${task.id}&limit=5`, 
          { headers }
        );
        
        const newHistory = historyResponse.data.history || [];
        console.log(`📊 History after manual sync: ${newHistory.length} entries`);
        
        if (newHistory.length > 0) {
          console.log('✅ Manual sync creates history - engine is working');
          console.log(`📄 Latest: ${newHistory[0].status} - ${newHistory[0].files_processed} files`);
        } else {
          console.log('❌ Manual sync did not create history - engine issue');
        }
        
      } catch (syncError) {
        console.log('❌ Manual sync failed:', syncError.message);
      }
      
    } catch (error) {
      console.log('❌ Manual history test failed:', error.message);
    }
  }

  analyzeFindings() {
    console.log('\n🔍 Analysis & Diagnosis');
    console.log('═'.repeat(50));
    
    console.log('\n📊 Event Summary:');
    if (this.events.length === 0) {
      console.log('❌ NO EVENTS CAPTURED');
      console.log('   This indicates:');
      console.log('   - Real-time sync is not running');
      console.log('   - Events are not being emitted');
      console.log('   - Socket connection issues');
    } else {
      const eventCounts = {};
      this.events.forEach(({ event }) => {
        eventCounts[event] = (eventCounts[event] || 0) + 1;
      });
      
      Object.entries(eventCounts).forEach(([event, count]) => {
        console.log(`   ${event}: ${count}`);
      });
    }
    
    console.log('\n💡 Likely Issues:');
    
    if (this.events.length === 0) {
      console.log('1. ❌ REAL-TIME SYNC NOT WORKING');
      console.log('   - Check if real-time sync is actually started');
      console.log('   - Verify file watcher is running');
      console.log('   - Check source/destination paths exist');
    }
    
    const completionEvents = this.events.filter(e => 
      e.event.includes('completed')
    );
    
    if (completionEvents.length === 0) {
      console.log('2. ❌ NO COMPLETION EVENTS');
      console.log('   - Real-time sync is not completing');
      console.log('   - Events are not being emitted properly');
      console.log('   - Check FileSyncEngine.js emit statements');
    }
    
    console.log('\n🔧 Immediate Actions:');
    console.log('1. Check server console for real-time sync logs');
    console.log('2. Verify source/destination folders exist');
    console.log('3. Test by creating a simple file in source');
    console.log('4. Check if file watcher is actually running');
    console.log('5. Restart the server to reload changes');
    
    console.log('\n🧪 Next Steps:');
    console.log('1. Create a new real-time task');
    console.log('2. Start real-time monitoring');
    console.log('3. Create a test file: echo "test" > source/test.txt');
    console.log('4. Watch server console for logs');
    console.log('5. Check if file appears in destination');
    
    if (this.socket) {
      this.socket.disconnect();
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export for manual use
module.exports = {
  runDeepDebug: async () => {
    const debugger = new HistoryDeepDebugger();
    await debugger.runDeepDebug();
  },
  
  quickCheck: async () => {
    console.log('🔍 Quick History Check\n');
    
    try {
      // Quick login and history check
      const loginResponse = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      const token = loginResponse.data.token;
      const headers = { 'Authorization': `Bearer ${token}` };
      
      // Check history
      const historyResponse = await axios.get(`${baseURL}/sync/history?limit=20`, { headers });
      const history = historyResponse.data.history || [];
      
      console.log(`📊 Total history entries: ${history.length}`);
      
      if (history.length === 0) {
        console.log('❌ NO HISTORY ENTRIES FOUND');
        console.log('\nPossible causes:');
        console.log('1. No syncs have been completed yet');
        console.log('2. Database connection issues');
        console.log('3. History creation logic not working');
        console.log('4. Real-time sync not emitting completion events');
      } else {
        console.log('✅ History entries found:');
        history.slice(0, 5).forEach((entry, index) => {
          console.log(`   ${index + 1}. Task ${entry.task_id}: ${entry.status} - ${entry.files_processed} files`);
        });
      }
      
      // Check tasks
      const tasksResponse = await axios.get(`${baseURL}/sync/tasks`, { headers });
      const tasks = tasksResponse.data.tasks || [];
      
      console.log(`\n📋 Total tasks: ${tasks.length}`);
      
      const realtimeTasks = tasks.filter(task => {
        const options = typeof task.options === 'string' ? JSON.parse(task.options) : task.options;
        return options?.enableRealtime;
      });
      
      console.log(`⚡ Real-time enabled tasks: ${realtimeTasks.length}`);
      
    } catch (error) {
      console.error('❌ Quick check failed:', error.message);
    }
  }
};

// Auto-run if called directly
if (require.main === module) {
  const debugger = new HistoryDeepDebugger();
  debugger.runDeepDebug();
}

console.log('🎯 Deep History Debugger loaded!');
console.log('📝 Run: node scripts/deep-debug-history.js');
console.log('📝 Or: require("./scripts/deep-debug-history").quickCheck()');
