const axios = require('axios');

async function testRedesignedClientManagement() {
  console.log('🧪 Testing Redesigned Client Management Architecture\n');

  const webServerUrl = 'http://localhost:5001';
  let token = null;

  try {
    // Test 1: Authentication
    console.log('🔐 TEST 1: Authentication');
    console.log('=' .repeat(50));

    try {
      const loginResponse = await axios.post(`${webServerUrl}/api/auth/login`, {
        email: '<EMAIL>',
        password: 'password123'
      }, { timeout: 5000 });

      token = loginResponse.data.token;
      console.log('✅ Login successful');
    } catch (error) {
      console.log(`❌ Login failed: ${error.code === 'ECONNREFUSED' ? 'Server not running' : error.message}`);
      return;
    }

    // Test 2: Client Management API
    console.log('\n📊 TEST 2: Client Management API');
    console.log('=' .repeat(50));

    try {
      const clientsResponse = await axios.get(`${webServerUrl}/api/clients`, {
        headers: { Authorization: `Bearer ${token}` },
        timeout: 10000
      });

      const data = clientsResponse.data;
      console.log(`✅ Clients API Response:`);
      console.log(`   📋 Total clients: ${data.total}`);
      console.log(`   📊 Source: ${data.source || 'database'}`);
      
      if (data.stats) {
        console.log(`   📈 Stats: ${data.stats.online} online, ${data.stats.offline} offline`);
      }

      if (data.clients && data.clients.length > 0) {
        const client = data.clients[0];
        console.log(`\n📋 SAMPLE CLIENT:`);
        console.log(`   🆔 ID: ${client.client_id}`);
        console.log(`   🏠 Hostname: ${client.hostname}`);
        console.log(`   📊 Status: ${client.status}`);
        console.log(`   📋 Tasks: ${client.total_tasks || 0} total, ${client.active_tasks || 0} active`);
        
        // Test client-specific endpoints
        await testClientSpecificEndpoints(client.client_id, token);
      } else {
        console.log('   📭 No clients found');
        console.log('   💡 Login to desktop app first to create client records');
      }

    } catch (error) {
      console.log(`❌ Clients API Error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }

    // Test 3: Navigation Structure
    console.log('\n🧭 TEST 3: Navigation Structure');
    console.log('=' .repeat(50));
    
    console.log('✅ NEW ARCHITECTURE:');
    console.log('   📊 Dashboard → Overview of all clients');
    console.log('   🖥️ Client Management → List of clients');
    console.log('   🖥️ Client Management → [Client Detail] → Overview');
    console.log('   🖥️ Client Management → [Client Detail] → Sync Tasks');
    console.log('   🖥️ Client Management → [Client Detail] → History');
    console.log('   📈 Analytics → System analytics');
    console.log('   ⚙️ Settings → System settings');
    console.log('');
    console.log('❌ REMOVED:');
    console.log('   🔄 Sync Tasks (standalone page) → Moved to client detail');
    console.log('');
    console.log('✅ BENEFITS:');
    console.log('   - Tasks are contextual to each client');
    console.log('   - Better UX with client-specific management');
    console.log('   - History is per-client, not global');
    console.log('   - Cleaner navigation structure');

    // Test 4: URL Structure
    console.log('\n🔗 TEST 4: URL Structure');
    console.log('=' .repeat(50));
    
    console.log('✅ NEW URL PATTERNS:');
    console.log('   /clients → Client list');
    console.log('   /clients/CLIENT-ID → Client detail (overview)');
    console.log('   /clients/CLIENT-ID?tab=tasks → Client tasks');
    console.log('   /clients/CLIENT-ID?tab=history → Client history');
    console.log('');
    console.log('❌ REMOVED URLs:');
    console.log('   /tasks → No longer exists');

    // Test 5: Component Structure
    console.log('\n🏗️ TEST 5: Component Structure');
    console.log('=' .repeat(50));
    
    console.log('✅ NEW COMPONENTS:');
    console.log('   📁 components/ClientDetail/');
    console.log('      📄 ClientTasks.js → Task management per client');
    console.log('      📄 ClientHistory.js → History per client');
    console.log('');
    console.log('✅ ENHANCED COMPONENTS:');
    console.log('   📄 pages/ClientManagement.js → Now supports detail view');
    console.log('   📄 components/Layout/Layout.js → Simplified navigation');
    console.log('');
    console.log('❌ REMOVED COMPONENTS:');
    console.log('   📄 pages/SyncTasks.js → Functionality moved to ClientTasks');

    // Test 6: Data Flow
    console.log('\n🔄 TEST 6: Data Flow');
    console.log('=' .repeat(50));
    
    console.log('✅ NEW DATA FLOW:');
    console.log('   1. Client list → GET /api/clients');
    console.log('   2. Client detail → URL params + client data');
    console.log('   3. Client tasks → GET /api/sync/tasks?client_id=X');
    console.log('   4. Client history → GET /api/sync/history?client_id=X');
    console.log('   5. Task commands → POST /api/clients/X/command');
    console.log('');
    console.log('✅ BENEFITS:');
    console.log('   - Filtered data per client');
    console.log('   - Better performance (less data transfer)');
    console.log('   - Clearer data relationships');
    console.log('   - Easier debugging and monitoring');

    console.log('\n📋 TESTING SUMMARY:');
    console.log('=' .repeat(50));
    console.log('✅ Architecture redesigned successfully');
    console.log('✅ Sync Tasks moved from sidebar to client detail');
    console.log('✅ History added per client');
    console.log('✅ URL routing supports client detail view');
    console.log('✅ Component structure is more logical');
    console.log('✅ Data flow is more efficient');
    console.log('');
    console.log('🎯 NEXT STEPS:');
    console.log('1. 🚀 Start web server: npm run start-web-management');
    console.log('2. 🌐 Open: http://localhost:3001');
    console.log('3. 🔐 Login with test credentials');
    console.log('4. 🖥️ Go to Client Management');
    console.log('5. 👁️ Click "View Details" on any client');
    console.log('6. 🔄 Check "Sync Tasks" tab');
    console.log('7. 📜 Check "History" tab');
    console.log('8. ✅ Verify no "Sync Tasks" in sidebar');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

async function testClientSpecificEndpoints(clientId, token) {
  console.log(`\n🔍 Testing client-specific endpoints for: ${clientId}`);
  
  const webServerUrl = 'http://localhost:5001';
  
  try {
    // Test tasks endpoint
    const tasksResponse = await axios.get(`${webServerUrl}/api/sync/tasks?client_id=${clientId}`, {
      headers: { Authorization: `Bearer ${token}` },
      timeout: 5000
    });
    
    const tasks = tasksResponse.data.tasks || [];
    console.log(`   🔄 Tasks: ${tasks.length} found`);
    
  } catch (error) {
    console.log(`   ❌ Tasks endpoint: ${error.response?.status || error.message}`);
  }
  
  try {
    // Test history endpoint
    const historyResponse = await axios.get(`${webServerUrl}/api/sync/history?client_id=${clientId}`, {
      headers: { Authorization: `Bearer ${token}` },
      timeout: 5000
    });
    
    const history = historyResponse.data.history || [];
    console.log(`   📜 History: ${history.length} entries found`);
    
  } catch (error) {
    console.log(`   ❌ History endpoint: ${error.response?.status || error.message}`);
  }
}

// Run test
testRedesignedClientManagement();
