const express = require('express');
const router = express.Router();
const authMiddleware = require('../middleware/auth');

// Get database sync status
router.get('/status', authMiddleware, (req, res) => {
  try {
    const dbSyncService = req.app.get('dbSyncService');
    const realTimeSync = req.app.get('realTimeDatabaseSync');
    const changeListener = req.app.get('changeListener');

    if (!dbSyncService) {
      return res.json({
        enabled: false,
        message: 'Database sync service not available'
      });
    }

    const status = dbSyncService.getStatus();

    res.json({
      enabled: true,
      ...status,
      realTimeSync: realTimeSync ? realTimeSync.getStatus() : null,
      changeListener: changeListener ? changeListener.getStatus() : null,
      environment: {
        enableDbSync: process.env.ENABLE_DB_SYNC === 'true',
        syncInterval: process.env.DB_SYNC_INTERVAL,
        syncDirection: process.env.DB_SYNC_DIRECTION
      }
    });
    
  } catch (error) {
    console.error('Get database sync status error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get database sync status'
    });
  }
});

// Update database sync configuration
router.put('/config', (req, res) => {
  try {
    const dbSyncService = req.app.get('dbSyncService');
    
    if (!dbSyncService) {
      return res.status(404).json({
        error: 'Service Not Available',
        message: 'Database sync service not available'
      });
    }
    
    const { enableAutoSync, syncIntervalMinutes, syncDirection, conflictResolution } = req.body;
    
    const newConfig = {};
    if (typeof enableAutoSync === 'boolean') newConfig.enableAutoSync = enableAutoSync;
    if (syncIntervalMinutes) newConfig.syncIntervalMinutes = parseInt(syncIntervalMinutes);
    if (syncDirection) newConfig.syncDirection = syncDirection;
    if (conflictResolution) newConfig.conflictResolution = conflictResolution;
    
    dbSyncService.updateConfig(newConfig);
    
    // Start or stop auto sync based on new config
    if (newConfig.enableAutoSync === true) {
      dbSyncService.startAutoSync();
    } else if (newConfig.enableAutoSync === false) {
      dbSyncService.stopAutoSync();
    }
    
    res.json({
      message: 'Database sync configuration updated',
      config: dbSyncService.getStatus().config
    });
    
  } catch (error) {
    console.error('Update database sync config error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update database sync configuration'
    });
  }
});

// Start manual sync
router.post('/sync', async (req, res) => {
  try {
    const dbSyncService = req.app.get('dbSyncService');
    
    if (!dbSyncService) {
      return res.status(404).json({
        error: 'Service Not Available',
        message: 'Database sync service not available'
      });
    }
    
    const { direction } = req.body;
    
    // Temporarily override sync direction if specified
    const originalDirection = dbSyncService.config.syncDirection;
    if (direction && ['sqlite-to-pg', 'pg-to-sqlite', 'bidirectional'].includes(direction)) {
      dbSyncService.config.syncDirection = direction;
    }
    
    const results = await dbSyncService.performSync();
    
    // Restore original direction
    dbSyncService.config.syncDirection = originalDirection;
    
    res.json({
      message: 'Manual database sync completed',
      results
    });
    
  } catch (error) {
    console.error('Manual database sync error:', error);
    res.status(500).json({
      error: 'Sync Failed',
      message: error.message || 'Failed to perform database sync'
    });
  }
});

// Start auto sync
router.post('/start', (req, res) => {
  try {
    const dbSyncService = req.app.get('dbSyncService');
    
    if (!dbSyncService) {
      return res.status(404).json({
        error: 'Service Not Available',
        message: 'Database sync service not available'
      });
    }
    
    if (dbSyncService.isRunning) {
      return res.json({
        message: 'Auto sync is already running'
      });
    }
    
    dbSyncService.startAutoSync();
    
    res.json({
      message: 'Auto database sync started',
      status: dbSyncService.getStatus()
    });
    
  } catch (error) {
    console.error('Start auto sync error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to start auto sync'
    });
  }
});

// Stop auto sync
router.post('/stop', (req, res) => {
  try {
    const dbSyncService = req.app.get('dbSyncService');
    
    if (!dbSyncService) {
      return res.status(404).json({
        error: 'Service Not Available',
        message: 'Database sync service not available'
      });
    }
    
    if (!dbSyncService.isRunning) {
      return res.json({
        message: 'Auto sync is not running'
      });
    }
    
    dbSyncService.stopAutoSync();
    
    res.json({
      message: 'Auto database sync stopped',
      status: dbSyncService.getStatus()
    });
    
  } catch (error) {
    console.error('Stop auto sync error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to stop auto sync'
    });
  }
});

// Test database connections
router.get('/test-connections', async (req, res) => {
  try {
    const dbSyncService = req.app.get('dbSyncService');
    
    if (!dbSyncService) {
      return res.status(404).json({
        error: 'Service Not Available',
        message: 'Database sync service not available'
      });
    }
    
    const connections = {
      sqlite: false,
      postgresql: false
    };
    
    // Test SQLite
    try {
      if (dbSyncService.sqlite) {
        const result = dbSyncService.sqlite.prepare('SELECT 1 as test').get();
        connections.sqlite = result.test === 1;
      }
    } catch (error) {
      console.error('SQLite test failed:', error);
    }
    
    // Test PostgreSQL
    try {
      if (dbSyncService.pgPool) {
        const result = await dbSyncService.pgPool.query('SELECT 1 as test');
        connections.postgresql = result.rows[0].test === 1;
      }
    } catch (error) {
      console.error('PostgreSQL test failed:', error);
    }
    
    res.json({
      message: 'Database connection test completed',
      connections,
      allConnected: connections.sqlite && connections.postgresql
    });
    
  } catch (error) {
    console.error('Test connections error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to test database connections'
    });
  }
});

// Get sync history/logs
router.get('/history', (req, res) => {
  try {
    const dbSyncService = req.app.get('dbSyncService');
    
    if (!dbSyncService) {
      return res.status(404).json({
        error: 'Service Not Available',
        message: 'Database sync service not available'
      });
    }
    
    // For now, return basic info
    // In the future, we could store sync history in database
    const status = dbSyncService.getStatus();
    
    res.json({
      message: 'Database sync history',
      history: [
        {
          timestamp: status.lastSyncTime,
          type: 'auto',
          direction: status.config.syncDirection,
          status: 'completed'
        }
      ].filter(h => h.timestamp) // Only include if we have sync history
    });
    
  } catch (error) {
    console.error('Get sync history error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get sync history'
    });
  }
});

// Compare data between databases
router.get('/compare', async (req, res) => {
  try {
    const dbSyncService = req.app.get('dbSyncService');

    if (!dbSyncService) {
      return res.status(404).json({
        error: 'Service Not Available',
        message: 'Database sync service not available'
      });
    }

    const comparison = await dbSyncService.compareData();
    res.json({
      message: 'Data comparison completed',
      comparison
    });

  } catch (error) {
    console.error('Error comparing data:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
});

// Perform consistency check
router.post('/consistency-check', async (req, res) => {
  try {
    const dbSyncService = req.app.get('dbSyncService');

    if (!dbSyncService) {
      return res.status(404).json({
        error: 'Service Not Available',
        message: 'Database sync service not available'
      });
    }

    const result = await dbSyncService.performConsistencyCheck();
    res.json({
      message: 'Consistency check completed',
      result
    });

  } catch (error) {
    console.error('Error performing consistency check:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
});

module.exports = router;
