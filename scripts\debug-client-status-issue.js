const axios = require('axios');

async function debugClientStatusIssue() {
  console.log('🔍 DEBUGGING CLIENT STATUS ISSUE WITH TOKEN REUSE\n');

  const desktopServerUrl = 'http://localhost:5000';
  const webServerUrl = 'http://localhost:5001';
  const credentials = {
    email: '<EMAIL>',
    password: 'password123'
  };

  // Test Desktop Server
  await testClientStatusFlow('Desktop', desktopServerUrl, credentials);
  
  // Test Web Server  
  await testClientStatusFlow('Web', webServerUrl, credentials);
}

async function testClientStatusFlow(serverName, serverUrl, credentials) {
  console.log(`🖥️ TESTING ${serverName.toUpperCase()} SERVER CLIENT STATUS`);
  console.log('=' .repeat(60));

  try {
    // Step 1: First Login
    console.log('🔐 STEP 1: First Login');
    const login1 = await axios.post(`${serverUrl}/api/auth/login`, credentials, { timeout: 5000 });
    const { token: token1, tokenReused: reused1, sessionInfo: session1 } = login1.data;
    
    console.log(`✅ First login successful`);
    console.log(`   🔑 Token: ${token1.substring(0, 30)}...`);
    console.log(`   🔄 Token reused: ${reused1}`);
    console.log(`   📊 Sessions: ${session1?.totalSessions || 'unknown'}`);

    // Step 2: Register Client (simulate desktop app registration)
    console.log('\n📋 STEP 2: Client Registration');
    try {
      const clientData = {
        clientId: `TEST-CLIENT-${Date.now()}`,
        userId: login1.data.user.id,
        hostname: `test-${serverName.toLowerCase()}-host`,
        platform: 'win32',
        arch: 'x64',
        version: '1.0.0',
        nodeVersion: process.version,
        totalMemory: 8589934592,
        cpuCount: 8
      };

      const registerResponse = await axios.post(`${serverUrl}/api/clients/register`, clientData, {
        headers: { Authorization: `Bearer ${token1}` },
        timeout: 10000
      });

      console.log(`✅ Client registration successful`);
      console.log(`   🆔 Client ID: ${clientData.clientId}`);
      console.log(`   🏠 Hostname: ${clientData.hostname}`);
      console.log(`   📊 Status: ${registerResponse.data.status}`);

      // Step 3: Check Client Status
      console.log('\n📊 STEP 3: Check Client Status');
      const clientsResponse = await axios.get(`${serverUrl}/api/clients`, {
        headers: { Authorization: `Bearer ${token1}` },
        timeout: 10000
      });

      const clients = clientsResponse.data.clients || [];
      const testClient = clients.find(c => c.client_id === clientData.clientId);

      if (testClient) {
        console.log(`✅ Client found in database`);
        console.log(`   📊 Status: ${testClient.status}`);
        console.log(`   👁️ Last seen: ${testClient.last_seen}`);
        console.log(`   🏠 Hostname: ${testClient.hostname}`);
      } else {
        console.log(`❌ Client NOT found in database`);
      }

      // Step 4: Second Login (Token Reuse)
      console.log('\n🔐 STEP 4: Second Login (Token Reuse Test)');
      const login2 = await axios.post(`${serverUrl}/api/auth/login`, credentials, { timeout: 5000 });
      const { token: token2, tokenReused: reused2, sessionInfo: session2 } = login2.data;
      
      console.log(`✅ Second login successful`);
      console.log(`   🔑 Token: ${token2.substring(0, 30)}...`);
      console.log(`   🔄 Token reused: ${reused2}`);
      console.log(`   📊 Sessions: ${session2?.totalSessions || 'unknown'}`);
      console.log(`   🔍 Same token: ${token1 === token2 ? 'YES' : 'NO'}`);

      // Step 5: Register Client Again (simulate app restart)
      console.log('\n📋 STEP 5: Client Re-registration (App Restart Simulation)');
      const reregisterResponse = await axios.post(`${serverUrl}/api/clients/register`, {
        ...clientData,
        metadata: {
          ...clientData.metadata,
          restartTime: new Date(),
          tokenReused: reused2
        }
      }, {
        headers: { Authorization: `Bearer ${token2}` },
        timeout: 10000
      });

      console.log(`✅ Client re-registration successful`);
      console.log(`   📊 Status: ${reregisterResponse.data.status}`);

      // Step 6: Check Client Status After Re-registration
      console.log('\n📊 STEP 6: Check Client Status After Re-registration');
      const clients2Response = await axios.get(`${serverUrl}/api/clients`, {
        headers: { Authorization: `Bearer ${token2}` },
        timeout: 10000
      });

      const clients2 = clients2Response.data.clients || [];
      const testClient2 = clients2.find(c => c.client_id === clientData.clientId);

      if (testClient2) {
        console.log(`✅ Client found after re-registration`);
        console.log(`   📊 Status: ${testClient2.status}`);
        console.log(`   👁️ Last seen: ${testClient2.last_seen}`);
        console.log(`   🔄 Status correct: ${testClient2.status === 'online' ? 'YES' : 'NO'}`);
        
        if (testClient2.status !== 'online') {
          console.log(`❌ ISSUE DETECTED: Client status should be 'online' but is '${testClient2.status}'`);
        }
      } else {
        console.log(`❌ CRITICAL ISSUE: Client NOT found after re-registration`);
      }

      // Step 7: Logout and Check Status
      console.log('\n🚪 STEP 7: Logout and Check Status');
      await axios.post(`${serverUrl}/api/auth/logout`, {}, {
        headers: { Authorization: `Bearer ${token2}` },
        timeout: 5000
      });
      console.log(`✅ Logout successful`);

      // Wait a moment for status update
      await new Promise(resolve => setTimeout(resolve, 2000));

      const clients3Response = await axios.get(`${serverUrl}/api/clients`, {
        headers: { Authorization: `Bearer ${token1}` }, // Use old token to check
        timeout: 10000
      });

      const clients3 = clients3Response.data.clients || [];
      const testClient3 = clients3.find(c => c.client_id === clientData.clientId);

      if (testClient3) {
        console.log(`✅ Client found after logout`);
        console.log(`   📊 Status: ${testClient3.status}`);
        console.log(`   🔄 Status correct: ${testClient3.status === 'offline' ? 'YES' : 'NO'}`);
        
        if (testClient3.status !== 'offline') {
          console.log(`⚠️ POTENTIAL ISSUE: Client status should be 'offline' but is '${testClient3.status}'`);
        }
      }

      // Cleanup: Remove test client
      console.log('\n🧹 CLEANUP: Removing test client');
      try {
        await axios.delete(`${serverUrl}/api/clients/${clientData.clientId}`, {
          headers: { Authorization: `Bearer ${token1}` },
          timeout: 5000
        });
        console.log(`✅ Test client removed`);
      } catch (cleanupError) {
        console.log(`⚠️ Cleanup failed: ${cleanupError.message}`);
      }

    } catch (clientError) {
      console.log(`❌ Client operations failed: ${clientError.response?.status} - ${clientError.response?.data?.message || clientError.message}`);
    }

  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log(`❌ ${serverName} server not running on ${serverUrl}`);
    } else {
      console.log(`❌ ${serverName} server test failed: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }
  }

  console.log(''); // Empty line for spacing
}

async function analyzeIssue() {
  console.log('📊 CLIENT STATUS ISSUE ANALYSIS');
  console.log('=' .repeat(60));
  
  console.log('🔍 POTENTIAL ROOT CAUSES:');
  console.log('   1. Token reuse prevents client re-registration');
  console.log('   2. Client status not updated when token is reused');
  console.log('   3. Socket connection tracking confused by reused tokens');
  console.log('   4. Database sync issues between desktop and web');
  console.log('   5. ClientStateManager not handling token reuse properly');
  console.log('');
  
  console.log('✅ EXPECTED BEHAVIOR:');
  console.log('   1. Login with new token → Client registers as online');
  console.log('   2. Login with reused token → Client still registers as online');
  console.log('   3. App restart → Client re-registers and shows online');
  console.log('   4. Logout → Client status changes to offline');
  console.log('   5. Socket disconnect → Client status changes to offline');
  console.log('');
  
  console.log('🔧 FIXES IMPLEMENTED:');
  console.log('   ✅ Track token reuse in auth response');
  console.log('   ✅ Force client status update regardless of token reuse');
  console.log('   ✅ Enhanced logging for debugging');
  console.log('   ✅ ClientStateManager force update flag');
  console.log('   ✅ Better client registration metadata');
  console.log('');
  
  console.log('🧪 TESTING RECOMMENDATIONS:');
  console.log('   1. Test login → register → check status');
  console.log('   2. Test login again (token reuse) → register → check status');
  console.log('   3. Test logout → check status');
  console.log('   4. Test socket disconnect → check status');
  console.log('   5. Monitor server logs for debugging info');
}

// Run debug
debugClientStatusIssue()
  .then(() => {
    analyzeIssue();
    console.log('\n🎉 CLIENT STATUS DEBUGGING COMPLETED!');
    console.log('💡 Check the results above to identify status tracking issues');
  })
  .catch(error => {
    console.error('❌ Debug failed:', error);
  });
