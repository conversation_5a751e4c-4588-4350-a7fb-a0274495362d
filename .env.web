# Database configuration
DB_TYPE=postgresql
DB_HOST=*************
DB_PORT=5432
DB_NAME=syncmasterpro
DB_USER=pi
DB_PASSWORD=ubuntu

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key-here

# Server configuration
PORT=5001
NODE_ENV=development

# Client URL for CORS
CLIENT_URL=http://localhost:5000

# File sync settings
MAX_SYNC_TASKS=10
SYNC_TIMEOUT=300000
FILE_WATCH_DEBOUNCE=1000
ENABLE_REAL_TIME_SYNC=true

# Database sync settings
ENABLE_DB_SYNC=false
DB_SYNC_INTERVAL=30
DB_SYNC_DIRECTION=bidirectional
PG_HOST=*************
PG_USER=pi
PG_PASSWORD=ubuntu
PG_DATABASE=syncmasterpro
PG_PORT=5432

# Upload settings
UPLOAD_DIR=uploads
MAX_FILE_SIZE=100MB

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Security
BCRYPT_ROUNDS=12
SESSION_TIMEOUT=24h

# Features
ENABLE_ANALYTICS=true
ENABLE_AUDIT_LOG=true
ENABLE_CLOUD_STORAGE=false
