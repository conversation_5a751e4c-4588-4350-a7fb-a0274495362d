// Test script to verify clean startup
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

async function testCleanStartup() {
  console.log('🔄 Testing Clean Startup Process\n');
  
  try {
    // 1. Clean old database to test fresh startup
    console.log('1. 🧹 Cleaning old database...');
    
    const dbPath = path.join(process.cwd(), 'data', 'syncmasterpro.db');
    if (fs.existsSync(dbPath)) {
      fs.unlinkSync(dbPath);
      console.log('✅ Old database removed');
    } else {
      console.log('ℹ️ No old database found');
    }
    
    // 2. Test server startup
    console.log('\n2. 🚀 Testing server startup...');
    
    const serverProcess = spawn('npm', ['run', 'server-desktop'], {
      stdio: 'pipe',
      shell: true,
      env: { ...process.env, NODE_ENV: 'test' }
    });
    
    let serverOutput = '';
    let serverErrors = '';
    let startupComplete = false;
    let hasErrors = false;
    
    serverProcess.stdout.on('data', (data) => {
      const output = data.toString();
      serverOutput += output;
      console.log('📤 Server:', output.trim());
      
      // Check for successful startup
      if (output.includes('Server running on port')) {
        startupComplete = true;
      }
      
      // Check for auto-resume completion
      if (output.includes('Task auto-resume completed')) {
        console.log('✅ Auto-resume completed successfully');
      }
    });
    
    serverProcess.stderr.on('data', (data) => {
      const error = data.toString();
      serverErrors += error;
      
      // Filter out known warnings and migration info
      if (!error.includes('DeprecationWarning') &&
          !error.includes('GPU process exited') &&
          !error.includes('Migration error') &&
          !error.includes('duplicate column') &&
          !error.includes('SQL:') &&
          !error.includes('Params:') &&
          !error.includes('SqliteError: duplicate column')) {
        console.log('❌ Server Error:', error.trim());
        hasErrors = true;
      } else {
        console.log('⚠️ Server Warning:', error.trim());
      }
    });
    
    // Wait for startup
    await new Promise((resolve) => {
      const checkStartup = setInterval(() => {
        if (startupComplete) {
          clearInterval(checkStartup);
          resolve();
        }
      }, 1000);
      
      // Timeout after 30 seconds
      setTimeout(() => {
        clearInterval(checkStartup);
        resolve();
      }, 30000);
    });
    
    // 3. Test API endpoints
    if (startupComplete) {
      console.log('\n3. 🔗 Testing API endpoints...');
      
      try {
        const axios = require('axios');
        
        // Test health check (if exists)
        try {
          const healthResponse = await axios.get('http://localhost:5002/api/auth/verify', {
            timeout: 5000,
            validateStatus: () => true // Accept any status
          });
          console.log(`✅ API responding: ${healthResponse.status}`);
        } catch (error) {
          console.log('⚠️ API not responding (expected without auth)');
        }
        
        // Test database connection
        try {
          const dbTestResponse = await axios.post('http://localhost:5002/api/auth/login', {
            email: '<EMAIL>',
            password: 'wrongpassword'
          }, {
            timeout: 5000,
            validateStatus: () => true
          });
          console.log(`✅ Database connection: ${dbTestResponse.status === 401 ? 'Working' : 'Unknown'}`);
        } catch (error) {
          console.log('❌ Database connection test failed');
        }
        
      } catch (error) {
        console.log('⚠️ Could not test API endpoints:', error.message);
      }
    }
    
    // 4. Test graceful shutdown
    console.log('\n4. 🛑 Testing graceful shutdown...');
    
    serverProcess.kill('SIGINT');
    
    await new Promise((resolve) => {
      serverProcess.on('exit', (code, signal) => {
        console.log(`✅ Server exited with code: ${code}, signal: ${signal}`);
        resolve();
      });
      
      // Force kill after 10 seconds
      setTimeout(() => {
        serverProcess.kill('SIGKILL');
        console.log('⚠️ Server force killed (timeout)');
        resolve();
      }, 10000);
    });
    
    // 5. Analyze results
    console.log('\n📊 STARTUP TEST SUMMARY:');
    console.log(`   Server startup: ${startupComplete ? '✅ SUCCESS' : '❌ FAILED'}`);
    console.log(`   Critical errors: ${hasErrors ? '❌ FOUND' : '✅ NONE'}`);
    console.log(`   Database creation: ${serverOutput.includes('Database initialized') ? '✅ SUCCESS' : '❌ FAILED'}`);
    console.log(`   Auto-resume: ${serverOutput.includes('auto-resume') ? '✅ WORKING' : '⚠️ NOT TESTED'}`);
    console.log(`   Graceful shutdown: ${serverOutput.includes('gracefully') ? '✅ WORKING' : '⚠️ UNKNOWN'}`);
    
    // 6. Known issues summary
    console.log('\n🔍 KNOWN ISSUES ANALYSIS:');
    
    const knownIssues = {
      'GPU process errors': serverErrors.includes('GPU process exited'),
      'Deprecation warnings': serverErrors.includes('DeprecationWarning'),
      'Migration warnings': serverErrors.includes('duplicate column'),
      'Critical errors': hasErrors
    };
    
    Object.entries(knownIssues).forEach(([issue, found]) => {
      const status = found ? '⚠️ PRESENT' : '✅ ABSENT';
      const impact = issue === 'Critical errors' ? '(BLOCKS FUNCTIONALITY)' : '(COSMETIC ONLY)';
      console.log(`   ${issue}: ${status} ${impact}`);
    });
    
    // 7. Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    
    if (knownIssues['GPU process errors']) {
      console.log('   🔧 GPU errors: Add --disable-gpu flag to Electron for headless environments');
    }
    
    if (knownIssues['Deprecation warnings']) {
      console.log('   📦 Deprecation: Update dependencies to latest versions');
    }
    
    if (knownIssues['Migration warnings']) {
      console.log('   🗄️ Migration: Consider database reset for clean development');
    }
    
    if (!knownIssues['Critical errors'] && startupComplete) {
      console.log('   🎉 Overall: Startup process is working correctly!');
    }
    
    console.log('\n🎯 CONCLUSION:');
    if (startupComplete && !hasErrors) {
      console.log('✅ Startup process is HEALTHY with only cosmetic warnings');
      console.log('🚀 Application is ready for use');
    } else if (startupComplete && hasErrors) {
      console.log('⚠️ Startup process has ISSUES that need attention');
      console.log('🔧 Check error logs for details');
    } else {
      console.log('❌ Startup process FAILED');
      console.log('🆘 Application cannot start properly');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Export for manual use
module.exports = { testCleanStartup };

// Auto-run if called directly
if (require.main === module) {
  testCleanStartup();
}

console.log('🔄 Startup Clean Tester loaded!');
console.log('📝 Run: node scripts/test-startup-clean.js');
