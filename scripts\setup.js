const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up SyncMasterPro...\n');

// Create necessary directories
const directories = [
  'data',
  'logs',
  'uploads',
  'temp'
];

directories.forEach(dir => {
  const dirPath = path.join(__dirname, '..', dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✅ Created directory: ${dir}`);
  } else {
    console.log(`📁 Directory already exists: ${dir}`);
  }
});

// Create .env file if it doesn't exist
const envPath = path.join(__dirname, '..', '.env');
const envExamplePath = path.join(__dirname, '..', '.env.example');

if (!fs.existsSync(envPath) && fs.existsSync(envExamplePath)) {
  fs.copyFileSync(envExamplePath, envPath);
  console.log('✅ Created .env file from .env.example');
  console.log('⚠️  Please update .env with your configuration');
} else if (fs.existsSync(envPath)) {
  console.log('📄 .env file already exists');
} else {
  console.log('❌ .env.example not found');
}

// Create gitignore if it doesn't exist
const gitignorePath = path.join(__dirname, '..', '.gitignore');
if (!fs.existsSync(gitignorePath)) {
  const gitignoreContent = `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
data/
*.db
*.sqlite

# Logs
logs/
*.log

# Temporary files
temp/
uploads/
*.tmp
*.temp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo

# Electron
out/
`;

  fs.writeFileSync(gitignorePath, gitignoreContent);
  console.log('✅ Created .gitignore file');
}

console.log('\n🎉 Setup complete!');
console.log('\nNext steps:');
console.log('1. Update .env with your configuration');
console.log('2. Run "npm start" to start development');
console.log('3. Run "npm run build" to build for production');
console.log('\nFor more information, see README.md');
