import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import FolderPicker from '../common/FolderPicker';
import Toggle from '../UI/Toggle';

const CreateTaskModal = ({ onClose, onCreate, initialData = null, isEditing = false }) => {
  const { t } = useLanguage();

  // Debug logging
  console.log('🔍 CreateTaskModal props:', { initialData, isEditing });
  const [formData, setFormData] = useState(() => {
    const defaultData = {
      name: '',
      sourcePath: '',
      destinationPath: '',
      syncType: 'bidirectional',
      filters: '',
      deleteExtraFiles: false,
      preserveTimestamps: true,
      enableRealtime: false,
      schedule: ''
    };

    if (initialData) {
      return {
        ...defaultData,
        name: initialData.name || '',
        sourcePath: initialData.sourcePath || '',
        destinationPath: initialData.destinationPath || '',
        syncType: initialData.syncType || 'bidirectional',
        schedule: initialData.schedule || '',
        // Handle filters - convert array to string for input field
        filters: Array.isArray(initialData.filters)
          ? initialData.filters.join(', ')
          : initialData.filters || '',
        // Extract options from nested object
        deleteExtraFiles: initialData.options?.deleteExtraFiles ?? defaultData.deleteExtraFiles,
        preserveTimestamps: initialData.options?.preserveTimestamps ?? defaultData.preserveTimestamps,
        enableRealtime: initialData.options?.enableRealtime ?? defaultData.enableRealtime
      };
    }

    return defaultData;
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Folder picker states
  const [showSourcePicker, setShowSourcePicker] = useState(false);
  const [showDestinationPicker, setShowDestinationPicker] = useState(false);

  // Handle URL parameters for pre-filling paths
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const sourcePath = urlParams.get('sourcePath');
    const destinationPath = urlParams.get('destinationPath');

    if (sourcePath || destinationPath) {
      setFormData(prev => ({
        ...prev,
        ...(sourcePath && { sourcePath: decodeURIComponent(sourcePath) }),
        ...(destinationPath && { destinationPath: decodeURIComponent(destinationPath) })
      }));
    }
  }, []);

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = t('taskNameRequired');
    }

    if (!formData.sourcePath.trim()) {
      newErrors.sourcePath = t('sourcePathRequired');
    }

    if (!formData.destinationPath.trim()) {
      newErrors.destinationPath = t('destinationPathRequired');
    }

    if (formData.sourcePath === formData.destinationPath) {
      newErrors.destinationPath = t('pathsMustBeDifferent');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Format data properly for API
      const taskData = {
        name: formData.name.trim(),
        sourcePath: formData.sourcePath.trim(),
        destinationPath: formData.destinationPath.trim(),
        syncType: formData.syncType,
        schedule: formData.schedule || null,
        // Handle filters - convert string to array
        filters: typeof formData.filters === 'string'
          ? formData.filters.split(',').map(f => f.trim()).filter(f => f)
          : Array.isArray(formData.filters)
            ? formData.filters
            : [],
        // Handle options object
        options: {
          deleteExtraFiles: formData.deleteExtraFiles,
          preserveTimestamps: formData.preserveTimestamps,
          enableRealtime: formData.enableRealtime
        }
      };

      console.log('📋 Formatted task data:', taskData);
      const result = await onCreate(taskData);

      if (result.success) {
        onClose();
      } else {
        setErrors({ submit: result.error || t('failedToCreateTask') });
      }
    } catch (error) {
      console.error('Error creating/updating task:', error);
      setErrors({ submit: error.message || t('failedToCreateTask') });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle folder selection from web-based picker
  const handleFolderSelect = (path, type) => {
    if (type === 'source') {
      handleChange('sourcePath', path);
      setShowSourcePicker(false);
    } else if (type === 'destination') {
      handleChange('destinationPath', path);
      setShowDestinationPicker(false);
    }
  };

  // Handle native folder picker
  const handleNativeFolderPicker = async (type) => {
    if (window.electronAPI) {
      try {
        const result = await window.electronAPI.selectFolder();
        if (!result.canceled && result.filePaths.length > 0) {
          if (type === 'source') {
            handleChange('sourcePath', result.filePaths[0]);
          } else if (type === 'destination') {
            handleChange('destinationPath', result.filePaths[0]);
          }
        }
      } catch (error) {
        console.error('Error selecting folder:', error);
      }
    }
  };

  const syncTypeOptions = [
    {
      value: 'bidirectional',
      label: t('bidirectional'),
      description: t('bidirectionalDescription'),
      icon: '🔄'
    },
    {
      value: 'source-to-destination',
      label: t('sourceToDestination'),
      description: t('sourceToDestinationDescription'),
      icon: '➡️'
    },
    {
      value: 'destination-to-source',
      label: t('destinationToSource'),
      description: t('destinationToSourceDescription'),
      icon: '⬅️'
    },
    {
      value: 'mirror',
      label: t('mirror'),
      description: t('mirrorDescription'),
      icon: '🪞'
    },
    {
      value: 'incremental',
      label: t('incremental'),
      description: t('incrementalDescription'),
      icon: '📈'
    },
    {
      value: 'differential',
      label: 'Differential',
      description: 'Sync changes since last full sync',
      icon: '📊'
    },
    {
      value: 'today-only',
      label: t('todayOnly'),
      description: t('todayOnlyDescription'),
      icon: '📅'
    },
    {
      value: 'scheduled',
      label: 'Scheduled',
      description: 'Sync at specific times',
      icon: '⏰'
    }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? t('editTask') : t('createNewTask')}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Task Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('taskName')} *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                errors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              }`}
              placeholder={t('enterTaskName')}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.name}</p>
            )}
          </div>

          {/* Source Path */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('sourcePath')} *
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                value={formData.sourcePath}
                onChange={(e) => handleChange('sourcePath', e.target.value)}
                className={`flex-1 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                  errors.sourcePath ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                }`}
                placeholder={t('selectSourceFolder')}
              />

              {/* Native Windows Folder Picker (Desktop only) */}
              {window.electronAPI && (
                <button
                  type="button"
                  onClick={() => handleNativeFolderPicker('source')}
                  className="px-4 py-2 bg-blue-100 dark:bg-blue-900 hover:bg-blue-200 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 rounded-md border border-blue-300 dark:border-blue-600 flex items-center space-x-1"
                  title="Open Windows folder picker"
                >
                  <span>🗂️</span>
                  <span>{t('nativePicker')}</span>
                </button>
              )}

              {/* Web-based Folder Picker (Always available) */}
              <button
                type="button"
                onClick={() => setShowSourcePicker(true)}
                className="px-4 py-2 bg-blue-50 dark:bg-blue-800 hover:bg-blue-100 dark:hover:bg-blue-700 text-blue-600 dark:text-blue-400 rounded-md border border-blue-200 dark:border-blue-500 flex items-center space-x-1"
                title="Open web folder picker"
              >
                <span>📁</span>
                <span>{t('webPicker')}</span>
              </button>
            </div>
            {errors.sourcePath && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.sourcePath}</p>
            )}
          </div>

          {/* Destination Path */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('destinationPath')} *
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                value={formData.destinationPath}
                onChange={(e) => handleChange('destinationPath', e.target.value)}
                className={`flex-1 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                  errors.destinationPath ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                }`}
                placeholder={t('selectDestinationFolder')}
              />

              {/* Native Windows Folder Picker (Desktop only) */}
              {window.electronAPI && (
                <button
                  type="button"
                  onClick={() => handleNativeFolderPicker('destination')}
                  className="px-4 py-2 bg-green-100 dark:bg-green-900 hover:bg-green-200 dark:hover:bg-green-800 text-green-700 dark:text-green-300 rounded-md border border-green-300 dark:border-green-600 flex items-center space-x-1"
                  title="Open Windows folder picker"
                >
                  <span>🗂️</span>
                  <span>{t('nativePicker')}</span>
                </button>
              )}

              {/* Web-based Folder Picker (Always available) */}
              <button
                type="button"
                onClick={() => setShowDestinationPicker(true)}
                className="px-4 py-2 bg-green-50 dark:bg-green-800 hover:bg-green-100 dark:hover:bg-green-700 text-green-600 dark:text-green-400 rounded-md border border-green-200 dark:border-green-500 flex items-center space-x-1"
                title="Open web folder picker"
              >
                <span>📁</span>
                <span>{t('webPicker')}</span>
              </button>
            </div>
            {errors.destinationPath && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.destinationPath}</p>
            )}
          </div>

          {/* Sync Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('syncType')}
            </label>
            <select
              value={formData.syncType}
              onChange={(e) => handleChange('syncType', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              {syncTypeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.icon} {option.label} - {option.description}
                </option>
              ))}
            </select>
          </div>

          {/* Schedule (only for scheduled sync) */}
          {formData.syncType === 'scheduled' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Schedule
              </label>
              <select
                value={formData.schedule}
                onChange={(e) => handleChange('schedule', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="">Select schedule...</option>
                <option value="every-5-minutes">Every 5 minutes</option>
                <option value="every-15-minutes">Every 15 minutes</option>
                <option value="every-30-minutes">Every 30 minutes</option>
                <option value="hourly">Every hour</option>
                <option value="daily">Daily at midnight</option>
                <option value="weekly">Weekly (Sunday)</option>
                <option value="monthly">Monthly (1st day)</option>
              </select>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">When to automatically run this sync task</p>
            </div>
          )}

          {/* File Filters */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('fileFilters')} ({t('optional')})
            </label>
            <input
              type="text"
              value={formData.filters}
              onChange={(e) => handleChange('filters', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder={t('fileFiltersPlaceholder')}
            />
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {t('fileFiltersHelp')}
            </p>
          </div>

          {/* Advanced Options */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {t('advancedOptions')}
            </h3>

            <Toggle
              label={t('deleteExtraFiles')}
              checked={formData.deleteExtraFiles}
              onChange={(checked) => handleChange('deleteExtraFiles', checked)}
            />

            <Toggle
              label={t('preserveTimestamps')}
              checked={formData.preserveTimestamps}
              onChange={(checked) => handleChange('preserveTimestamps', checked)}
            />

            {/* Real-time Sync Toggle */}
            <Toggle
              label={`⚡ ${t('enableRealtime')}`}
              description={t('enableRealtimeDescription')}
              checked={formData.enableRealtime}
              onChange={(checked) => handleChange('enableRealtime', checked)}
            />
          </div>

          {/* Submit Error */}
          {errors.submit && (
            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
              <p className="text-sm text-red-600 dark:text-red-400">{errors.submit}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {t('cancel')}
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? t('saving') : (isEditing ? t('updateTask') : t('createTask'))}
            </button>
          </div>
        </form>

        {/* Source Folder Picker Modal */}
        <FolderPicker
          isOpen={showSourcePicker}
          onClose={() => setShowSourcePicker(false)}
          onSelect={(path) => handleFolderSelect(path, 'source')}
          title={t('selectSourceFolder')}
        />

        {/* Destination Folder Picker Modal */}
        <FolderPicker
          isOpen={showDestinationPicker}
          onClose={() => setShowDestinationPicker(false)}
          onSelect={(path) => handleFolderSelect(path, 'destination')}
          title={t('selectDestinationFolder')}
        />
      </div>
    </div>
  );
};

// Icon component
const XIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
  </svg>
);



export default CreateTaskModal;
