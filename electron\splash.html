<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SyncMasterPro</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: white;
        }
        
        .splash-container {
            text-align: center;
            animation: fadeIn 1s ease-in;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: #667eea;
            font-weight: bold;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
        }
        
        .app-name {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .app-tagline {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 30px;
        }
        
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .loading-dot {
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            margin: 0 4px;
            animation: loading 1.4s infinite ease-in-out both;
        }
        
        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }
        .loading-dot:nth-child(3) { animation-delay: 0s; }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes loading {
            0%, 80%, 100% {
                transform: scale(0);
            }
            40% {
                transform: scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="splash-container">
        <div class="logo">S</div>
        <div class="app-name">SyncMasterPro</div>
        <div class="app-tagline">Professional File & Folder Synchronization</div>
        <div class="loading">
            <div class="loading-dot"></div>
            <div class="loading-dot"></div>
            <div class="loading-dot"></div>
        </div>
    </div>
</body>
</html>
