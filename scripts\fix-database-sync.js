#!/usr/bin/env node

/**
 * Fix database synchronization with authentication
 */

const axios = require('axios');

console.log('🔧 Fixing Database Synchronization');
console.log('==================================');

async function login() {
  console.log('\n1. 🔐 Logging in...');
  
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Login successful');
      return response.data.token;
    } else {
      console.log('❌ Login failed:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return null;
  }
}

async function checkSyncServiceStatus(token) {
  console.log('\n2. 📊 Checking sync service status...');

  try {
    const response = await axios.get('http://localhost:5001/api/database-sync/status', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Status Response:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Sync service accessible');
      console.log('📊 Service Status:', JSON.stringify(response.data, null, 2));
      return response.data;
    } else {
      console.log('❌ Sync service status failed:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Sync service error:', error.message);
    return null;
  }
}

async function configureSync(token) {
  console.log('\n3. 🔧 Configuring sync service...');

  try {
    const response = await axios.post('http://localhost:5001/api/database-sync/config', {
      enableAutoSync: true,
      syncDirection: 'sqlite-to-pg', // Desktop is primary source
      syncIntervalMinutes: 5, // Sync every 5 minutes
      conflictResolution: 'sqlite_wins' // Desktop data wins
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Config Response:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Sync service configured');
      console.log('📊 New Config:', JSON.stringify(response.data, null, 2));
      return true;
    } else {
      console.log('❌ Configure sync failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Configure sync error:', error.message);
    return false;
  }
}

async function triggerManualSync(token) {
  console.log('\n4. 🔄 Triggering manual sync (SQLite → PostgreSQL)...');

  try {
    const response = await axios.post('http://localhost:5001/api/database-sync/sync', {
      direction: 'sqlite-to-pg'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000, // 1 minute timeout
      validateStatus: () => true
    });
    
    console.log('📝 Sync Response:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Manual sync completed');
      console.log('📊 Sync Results:', JSON.stringify(response.data, null, 2));
      return response.data;
    } else {
      console.log('❌ Manual sync failed:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Manual sync error:', error.message);
    return null;
  }
}

async function startAutoSync(token) {
  console.log('\n5. 🚀 Starting auto sync...');

  try {
    const response = await axios.post('http://localhost:5001/api/database-sync/start', {}, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Start Response:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Auto sync started');
      console.log('📊 Status:', JSON.stringify(response.data, null, 2));
      return true;
    } else {
      console.log('❌ Start auto sync failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Start auto sync error:', error.message);
    return false;
  }
}

async function verifyDataSync() {
  console.log('\n6. 🔍 Verifying data synchronization...');
  
  const Database = require('better-sqlite3');
  const { Pool } = require('pg');
  const path = require('path');
  
  // Connect to SQLite (Desktop)
  const sqlitePath = path.join(__dirname, '../data/syncmasterpro.db');
  const sqlite = new Database(sqlitePath);
  
  // Connect to PostgreSQL (Web)
  const pgPool = new Pool({
    host: '*************',
    port: 5432,
    database: 'syncmasterpro',
    user: 'pi',
    password: 'ubuntu'
  });
  
  try {
    // Check Users
    const sqliteUsers = sqlite.prepare('SELECT COUNT(*) as count FROM users').get();
    const pgUsers = await pgPool.query('SELECT COUNT(*) as count FROM users');
    
    console.log(`👥 Users: SQLite(${sqliteUsers.count}) vs PostgreSQL(${pgUsers.rows[0].count})`);
    
    // Check Sync Tasks
    const sqliteTasks = sqlite.prepare('SELECT COUNT(*) as count FROM sync_tasks').get();
    const pgTasks = await pgPool.query('SELECT COUNT(*) as count FROM sync_tasks');
    
    console.log(`🔄 Tasks: SQLite(${sqliteTasks.count}) vs PostgreSQL(${pgTasks.rows[0].count})`);
    
    // Check if data is in sync
    const isInSync = (
      sqliteUsers.count === parseInt(pgUsers.rows[0].count) &&
      sqliteTasks.count === parseInt(pgTasks.rows[0].count)
    );
    
    if (isInSync) {
      console.log('✅ Databases are in sync!');
    } else {
      console.log('⚠️ Databases are NOT in sync!');
    }
    
    return isInSync;
    
  } finally {
    sqlite.close();
    await pgPool.end();
  }
}

async function main() {
  try {
    console.log('🚀 Starting database sync fix...\n');
    
    // Login to get token
    const token = await login();
    if (!token) {
      console.log('❌ Cannot proceed without authentication');
      return;
    }
    
    // Check current status
    const initialStatus = await checkSyncServiceStatus(token);
    
    // Configure sync service
    const configSuccess = await configureSync(token);
    
    // Trigger manual sync
    const syncResults = await triggerManualSync(token);
    
    // Start auto sync
    const autoSyncStarted = await startAutoSync(token);
    
    // Verify data sync
    const dataInSync = await verifyDataSync();
    
    console.log('\n📊 Summary:');
    console.log('===========');
    console.log('- Authentication:', token ? '✅' : '❌');
    console.log('- Service Status:', initialStatus ? '✅' : '❌');
    console.log('- Configuration:', configSuccess ? '✅' : '❌');
    console.log('- Manual Sync:', syncResults ? '✅' : '❌');
    console.log('- Auto Sync Started:', autoSyncStarted ? '✅' : '❌');
    console.log('- Data In Sync:', dataInSync ? '✅' : '❌');
    
    if (autoSyncStarted && dataInSync) {
      console.log('\n🎉 SUCCESS! Database synchronization is working!');
      console.log('');
      console.log('🔍 Configuration:');
      console.log('   - Direction: SQLite → PostgreSQL (Desktop is primary)');
      console.log('   - Interval: Every 5 minutes');
      console.log('   - Conflict Resolution: Desktop data wins');
      console.log('   - Auto Sync: Enabled');
      console.log('');
      console.log('📋 Desktop data will automatically sync to web database');
      console.log('🎯 Web manager will show real desktop data');
      console.log('💡 Any changes in desktop app will appear in web manager within 5 minutes');
    } else {
      console.log('\n❌ Database synchronization still has issues');
      console.log('💡 Check web server logs for more details');
      
      if (!dataInSync) {
        console.log('💡 Try running the manual sync again');
      }
    }
    
  } catch (error) {
    console.error('\n❌ Fix failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
