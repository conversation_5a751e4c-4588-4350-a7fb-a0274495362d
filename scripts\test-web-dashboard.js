// Test script for Web Dashboard Client Management
const axios = require('axios');

const webBaseURL = 'http://localhost:5001/api';
const desktopBaseURL = 'http://localhost:5002/api';

async function testWebDashboard() {
  console.log('🌐 Testing Web Dashboard Client Management\n');
  
  try {
    // 1. Login to web server
    console.log('1. 🔐 Logging into web server...');
    const webLoginResponse = await axios.post(`${webBaseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const webToken = webLoginResponse.data.token;
    const webHeaders = { 'Authorization': `Bearer ${webToken}` };
    
    console.log('✅ Web login successful');
    
    // 2. Check if any desktop clients are registered
    console.log('\n2. 📋 Checking registered desktop clients...');
    
    const clientsResponse = await axios.get(`${webBaseURL}/clients`, { headers: webHeaders });
    console.log(`✅ Found ${clientsResponse.data.total} registered clients`);
    
    if (clientsResponse.data.clients.length > 0) {
      console.log('📊 Registered clients:');
      clientsResponse.data.clients.forEach(client => {
        console.log(`   - ${client.hostname} (${client.client_id})`);
        console.log(`     Status: ${client.status}, Last seen: ${client.last_seen}`);
      });
    } else {
      console.log('💡 No clients registered yet. Start desktop app to register a client.');
    }
    
    // 3. Test client registration API (simulate desktop registration)
    console.log('\n3. 🖥️ Testing client registration API...');
    
    const testClientId = `test-client-${Date.now()}`;
    const registrationResponse = await axios.post(`${webBaseURL}/clients/register`, {
      clientId: testClientId,
      hostname: 'test-hostname',
      platform: 'win32',
      arch: 'x64',
      version: '1.0.0',
      nodeVersion: process.version,
      totalMemory: 8589934592,
      cpuCount: 8
    }, { headers: webHeaders });
    
    console.log('✅ Test client registered successfully');
    console.log(`📋 Client ID: ${testClientId}`);
    
    // 4. Get updated client list
    console.log('\n4. 📋 Getting updated client list...');
    
    const updatedClientsResponse = await axios.get(`${webBaseURL}/clients`, { headers: webHeaders });
    console.log(`✅ Now have ${updatedClientsResponse.data.total} registered clients`);
    
    // 5. Get specific client details
    console.log('\n5. 🔍 Getting client details...');
    
    const clientDetailsResponse = await axios.get(`${webBaseURL}/clients/${testClientId}`, { headers: webHeaders });
    console.log('✅ Client details retrieved');
    console.log(`📊 Client: ${clientDetailsResponse.data.client.hostname}`);
    console.log(`📊 Tasks: ${clientDetailsResponse.data.client.tasks.length}`);
    console.log(`📊 Recent history: ${clientDetailsResponse.data.client.recentHistory.length}`);
    
    // 6. Test sending command to client
    console.log('\n6. 🎛️ Testing remote command...');
    
    const commandResponse = await axios.post(`${webBaseURL}/clients/${testClientId}/command`, {
      type: 'get-status',
      data: { requestId: 'test-123' }
    }, { headers: webHeaders });
    
    console.log('✅ Command sent successfully');
    console.log(`📨 Command ID: ${commandResponse.data.commandId}`);
    
    // 7. Get client status
    console.log('\n7. 📊 Getting client status...');
    
    const statusResponse = await axios.get(`${webBaseURL}/clients/${testClientId}/status`, { headers: webHeaders });
    console.log('✅ Client status retrieved');
    console.log(`📊 Status: ${statusResponse.data.status.status}`);
    console.log(`📊 Connected: ${statusResponse.data.status.isConnected}`);
    
    // 8. Clean up test client
    console.log('\n8. 🗑️ Cleaning up test client...');
    
    const deleteResponse = await axios.delete(`${webBaseURL}/clients/${testClientId}`, { headers: webHeaders });
    console.log('✅ Test client removed successfully');
    
    // 9. Summary
    console.log('\n📊 WEB DASHBOARD TEST SUMMARY:');
    console.log('   Web Server Login: ✅ SUCCESS');
    console.log('   Client List API: ✅ SUCCESS');
    console.log('   Client Registration: ✅ SUCCESS');
    console.log('   Client Details: ✅ SUCCESS');
    console.log('   Remote Commands: ✅ SUCCESS');
    console.log('   Client Status: ✅ SUCCESS');
    console.log('   Client Removal: ✅ SUCCESS');
    
    console.log('\n🎯 PHASE 2 STATUS: ✅ WEB DASHBOARD API READY');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

async function testClientServerIntegration() {
  console.log('\n🔄 Testing Client-Server Integration\n');
  
  try {
    // 1. Login to both servers
    console.log('1. 🔐 Logging into both servers...');
    
    const webLoginResponse = await axios.post(`${webBaseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const desktopLoginResponse = await axios.post(`${desktopBaseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const webHeaders = { 'Authorization': `Bearer ${webLoginResponse.data.token}` };
    const desktopHeaders = { 'Authorization': `Bearer ${desktopLoginResponse.data.token}` };
    
    console.log('✅ Both servers logged in successfully');
    
    // 2. Check web server for desktop clients
    console.log('\n2. 🌐 Checking web server for desktop clients...');
    
    const webClientsResponse = await axios.get(`${webBaseURL}/clients`, { headers: webHeaders });
    console.log(`📊 Web server sees ${webClientsResponse.data.total} desktop clients`);
    
    if (webClientsResponse.data.clients.length > 0) {
      const client = webClientsResponse.data.clients[0];
      console.log(`📋 First client: ${client.hostname} (${client.status})`);
      
      // 3. Send command to desktop client
      console.log('\n3. 🎛️ Sending command to desktop client...');
      
      const commandResponse = await axios.post(`${webBaseURL}/clients/${client.client_id}/command`, {
        type: 'get-tasks',
        data: { userId: webLoginResponse.data.user.id }
      }, { headers: webHeaders });
      
      console.log('✅ Command sent to desktop client');
      console.log(`📨 Command ID: ${commandResponse.data.commandId}`);
      
      // 4. Check desktop tasks
      console.log('\n4. 🖥️ Checking desktop tasks...');
      
      const desktopTasksResponse = await axios.get(`${desktopBaseURL}/sync/tasks`, { headers: desktopHeaders });
      console.log(`📋 Desktop has ${desktopTasksResponse.data.tasks.length} sync tasks`);
      
      console.log('\n✅ CLIENT-SERVER INTEGRATION WORKING');
      
    } else {
      console.log('💡 No desktop clients connected to web server');
      console.log('💡 Make sure desktop app is running and logged in');
    }
    
  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

async function testRealTimeFeatures() {
  console.log('\n⚡ Testing Real-time Features\n');
  
  console.log('💡 Real-time features require Socket.IO connection');
  console.log('💡 To test real-time features:');
  console.log('   1. Start web server: npm run start-web');
  console.log('   2. Start desktop app: npm run start-desktop');
  console.log('   3. Login to desktop app');
  console.log('   4. Check web server logs for client registration');
  console.log('   5. Create/start sync tasks in desktop');
  console.log('   6. Check web server logs for client events');
  
  console.log('\n📊 Expected real-time events:');
  console.log('   - client-register: Desktop connects to web');
  console.log('   - client-status: Regular status updates');
  console.log('   - client-event: Sync events from desktop');
  console.log('   - client-connected/disconnected: Connection status');
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Export for manual use
module.exports = { testWebDashboard, testClientServerIntegration, testRealTimeFeatures };

// Auto-run if called directly
if (require.main === module) {
  testWebDashboard().then(() => {
    console.log('\n' + '='.repeat(60));
    return testClientServerIntegration();
  }).then(() => {
    console.log('\n' + '='.repeat(60));
    return testRealTimeFeatures();
  });
}

console.log('🌐 Web Dashboard Tester loaded!');
console.log('📝 Run: node scripts/test-web-dashboard.js');
