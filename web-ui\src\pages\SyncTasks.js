import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

const SyncTasks = () => {
  const { token } = useAuth();
  const [tasks, setTasks] = useState([]);
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedClient, setSelectedClient] = useState('all');

  useEffect(() => {
    loadData();
    // Refresh every 30 seconds
    const interval = setInterval(loadData, 30000);
    return () => clearInterval(interval);
  }, [token]);

  const loadData = async () => {
    if (!token) {
      setError('Authentication required');
      setLoading(false);
      return;
    }

    try {
      setError(null);
      console.log('🔄 Loading sync tasks data...');

      const response = await fetch('http://localhost:5001/api/clients', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to load data: ${response.status}`);
      }

      const data = await response.json();
      const clientsList = data.clients || [];
      
      setClients(clientsList);
      
      // Load real sync tasks from API
      console.log('📋 Loading real sync tasks from API...');

      const tasksResponse = await fetch('http://localhost:5001/api/sync/tasks', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!tasksResponse.ok) {
        throw new Error(`Failed to load sync tasks: ${tasksResponse.status}`);
      }

      const tasksData = await tasksResponse.json();
      const realTasks = tasksData.tasks || [];

      // Transform real tasks for display
      const transformedTasks = realTasks.map(task => {
        // Find the actual client for this task
        const taskClient = clientsList.find(c => c.client_id === task.client_id);

        return {
          id: task.id,
          clientId: task.client_id || (clientsList[0]?.client_id), // Use actual client_id
          clientName: taskClient?.hostname || 'Unknown Client',
          name: task.name,
          source: task.source_path || task.sourcePath,
          destination: task.destination_path || task.destinationPath,
          type: task.sync_type || 'bidirectional',
          status: task.status || 'idle',
          lastSync: task.updated_at || task.created_at,
          filesCount: task.files_count || 0,
          totalSize: task.total_size || 0,
          schedule: task.schedule,
          filters: task.filters
        };
      });

      setTasks(transformedTasks);
      console.log(`📋 Loaded ${transformedTasks.length} real sync tasks from API`);
      console.log('🔍 Task client IDs:', transformedTasks.map(t => ({
        id: t.id,
        clientId: t.clientId,
        clientName: t.clientName
      })));
    } catch (error) {
      console.error('❌ Failed to load sync tasks:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const sendCommand = async (clientId, command, taskId = null) => {
    try {
      console.log(`🔧 Sending command ${command} to client ${clientId}${taskId ? ` for task ${taskId}` : ''}`);
      
      const response = await fetch(`http://localhost:5001/api/clients/${clientId}/command`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: command,
          data: {
            taskId,
            timestamp: new Date().toISOString()
          }
        })
      });

      if (response.ok) {
        console.log(`✅ Command ${command} sent successfully`);
        // Refresh data after command
        setTimeout(loadData, 1000);
      } else {
        console.error(`❌ Failed to send command: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Error sending command:', error);
    }
  };

  const filteredTasks = selectedClient === 'all' 
    ? tasks 
    : tasks.filter(task => task.clientId === selectedClient);

  const getStatusColor = (status) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800';
      case 'stopped': return 'bg-gray-100 text-gray-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'bidirectional': return 'bg-blue-100 text-blue-800';
      case 'mirror': return 'bg-purple-100 text-purple-800';
      case 'backup': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Sync Tasks</h1>
          <p className="text-gray-600">Manage synchronization tasks across all desktop clients</p>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading sync tasks...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Sync Tasks</h1>
          <p className="text-gray-600">Manage synchronization tasks across all desktop clients</p>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading sync tasks</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
              <button
                onClick={loadData}
                className="mt-2 text-sm bg-red-100 text-red-800 px-3 py-1 rounded hover:bg-red-200"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Sync Tasks</h1>
          <p className="text-gray-600">Manage synchronization tasks across all desktop clients</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={loadData}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium text-gray-700">Filter by Client:</label>
          <select
            value={selectedClient}
            onChange={(e) => setSelectedClient(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Clients ({tasks.length} tasks)</option>
            {clients.map(client => (
              <option key={client.id} value={client.id}>
                {client.hostname} ({tasks.filter(t => t.clientId === client.id).length} tasks)
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Tasks List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            Sync Tasks ({filteredTasks.length})
          </h2>
        </div>
        <div className="p-6">
          {filteredTasks.length === 0 ? (
            <div className="text-center py-8">
              <svg className="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Sync Tasks Found</h3>
              <p className="text-gray-500">No synchronization tasks are configured on the selected client(s).</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredTasks.map((task) => (
                <div key={task.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <h3 className="text-lg font-medium text-gray-900">{task.name}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                        {task.status}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(task.type)}`}>
                        {task.type}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      {task.status === 'running' ? (
                        <button
                          onClick={() => sendCommand(task.clientId, 'stop-sync', task.id)}
                          className="px-3 py-1 bg-red-100 text-red-700 rounded-md text-sm hover:bg-red-200"
                        >
                          Stop
                        </button>
                      ) : (
                        <button
                          onClick={() => sendCommand(task.clientId, 'start-sync', task.id)}
                          className="px-3 py-1 bg-green-100 text-green-700 rounded-md text-sm hover:bg-green-200"
                        >
                          Start
                        </button>
                      )}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                    <div>
                      <p><span className="font-medium">Client:</span> {task.clientName}</p>
                      <p><span className="font-medium">Source:</span> {task.source}</p>
                      <p><span className="font-medium">Destination:</span> {task.destination}</p>
                    </div>
                    <div>
                      <p><span className="font-medium">Files:</span> {task.filesCount.toLocaleString()}</p>
                      <p><span className="font-medium">Size:</span> {(task.totalSize / 1024).toFixed(1)} MB</p>
                      <p><span className="font-medium">Last Sync:</span> {new Date(task.lastSync).toLocaleString()}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SyncTasks;
