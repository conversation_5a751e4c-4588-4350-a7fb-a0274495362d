import React, { createContext, useContext, useState, useEffect } from 'react';
import { useNotification } from './NotificationContext';
import { useLanguage } from './LanguageContext';

const SettingsContext = createContext();

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

export const SettingsProvider = ({ children }) => {
  const { addNotification } = useNotification();
  const { t } = useLanguage();
  
  // Check if running in Electron
  const isElectron = window.electronAPI !== undefined;
  
  // Default settings
  const defaultSettings = {
    // General settings
    language: 'en',
    
    // Appearance settings
    theme: 'system',
    
    // Startup settings (Electron only)
    startupWithWindows: false,
    minimizeToTray: true,
    closeToTray: true,
    startMinimized: false,
    
    // Sync settings
    autoSync: true,
    syncInterval: 300, // 5 minutes
    maxRetries: 3,
    
    // Notification settings
    showNotifications: true,
    soundEnabled: true,
    
    // Security settings
    sessionTimeout: 3600, // 1 hour
    requirePasswordChange: false
  };
  
  const [settings, setSettings] = useState(defaultSettings);
  const [originalSettings, setOriginalSettings] = useState(defaultSettings);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Load settings on mount
  useEffect(() => {
    loadSettings();
  }, []);

  // Check for unsaved changes
  useEffect(() => {
    const hasChanges = JSON.stringify(settings) !== JSON.stringify(originalSettings);
    setHasUnsavedChanges(hasChanges);
  }, [settings, originalSettings]);

  const loadSettings = async () => {
    try {
      setLoading(true);
      
      let loadedSettings = { ...defaultSettings };
      
      // Load from localStorage first
      const localSettings = localStorage.getItem('syncmasterpro-settings');
      if (localSettings) {
        loadedSettings = { ...loadedSettings, ...JSON.parse(localSettings) };
      }
      
      // Load Electron-specific settings if available
      if (isElectron && window.electronAPI?.startup?.getSettings) {
        try {
          const startupSettings = await window.electronAPI.startup.getSettings();
          loadedSettings = { ...loadedSettings, ...startupSettings };
        } catch (error) {
          // Silently handle missing Electron handlers - this is expected during development
          console.debug('Electron startup settings not available - using localStorage fallback');
        }
      }
      
      setSettings(loadedSettings);
      setOriginalSettings(loadedSettings);
      
    } catch (error) {
      console.error('Error loading settings:', error);
      addNotification(t('errorLoadingSettings'), 'error');
    } finally {
      setLoading(false);
    }
  };

  const updateSetting = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const updateMultipleSettings = (newSettings) => {
    setSettings(prev => ({
      ...prev,
      ...newSettings
    }));
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      
      // Save to localStorage
      localStorage.setItem('syncmasterpro-settings', JSON.stringify(settings));
      
      // Save Electron-specific settings
      if (isElectron && window.electronAPI?.startup) {
        try {
          // Check if each method exists before calling
          if (window.electronAPI.startup.setStartupWithWindows) {
            await window.electronAPI.startup.setStartupWithWindows(settings.startupWithWindows);
          }
          if (window.electronAPI.startup.setMinimizeToTray) {
            await window.electronAPI.startup.setMinimizeToTray(settings.minimizeToTray);
          }
          if (window.electronAPI.startup.setCloseToTray) {
            await window.electronAPI.startup.setCloseToTray(settings.closeToTray);
          }
          if (window.electronAPI.startup.setStartMinimized) {
            await window.electronAPI.startup.setStartMinimized(settings.startMinimized);
          }
        } catch (error) {
          // Silently handle missing Electron handlers - this is expected during development
          console.debug('Electron startup settings not available - using localStorage fallback');
        }
      }
      
      // Update original settings to match current
      setOriginalSettings({ ...settings });
      
      addNotification(t('settingsSaved'), 'success');
      
    } catch (error) {
      console.error('Error saving settings:', error);
      addNotification(t('errorSavingSettings'), 'error');
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = () => {
    setSettings({ ...defaultSettings });
  };

  const discardChanges = () => {
    setSettings({ ...originalSettings });
  };

  const value = {
    settings,
    originalSettings,
    defaultSettings,
    hasUnsavedChanges,
    loading,
    saving,
    isElectron,
    
    // Actions
    updateSetting,
    updateMultipleSettings,
    saveSettings,
    resetToDefaults,
    discardChanges,
    loadSettings
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};

export default SettingsContext;
