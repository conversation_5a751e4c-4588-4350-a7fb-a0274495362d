// Test script for real-time sync functionality
// Run this in browser console to test real-time sync

console.log('🧪 Testing Real-time Sync Functionality...\n');

class RealtimeSyncTester {
  constructor() {
    this.testResults = [];
    this.taskId = null;
  }

  async runAllTests() {
    console.log('🚀 Starting Real-time Sync Tests...\n');
    
    try {
      await this.test1_CheckEnvironment();
      await this.test2_CreateRealtimeTask();
      await this.test3_StartRealtimeSync();
      await this.test4_TestSocketEvents();
      await this.test5_StopRealtimeSync();
      await this.test6_CleanupTask();
      
      this.showResults();
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  async test1_CheckEnvironment() {
    this.logTest('Environment Check');
    
    // Check if we're logged in
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    this.assert(!!token, 'User is logged in');
    
    // Check Socket.IO connection
    const socket = window.socket;
    this.assert(!!socket, 'Socket.IO client exists');
    this.assert(socket.connected, 'Socket.IO is connected');
    
    // Check API availability
    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
    try {
      const response = await fetch(`${apiUrl}/sync/realtime/status`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      this.assert(response.ok, 'Real-time sync API is available');
    } catch (error) {
      this.assert(false, `Real-time sync API error: ${error.message}`);
    }
    
    console.log('✅ Environment check completed\n');
  }

  async test2_CreateRealtimeTask() {
    this.logTest('Create Real-time Sync Task');
    
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
    
    const taskData = {
      name: 'Test Real-time Sync',
      sourcePath: 'C:\\Test\\Source',
      destinationPath: 'C:\\Test\\Destination',
      syncType: 'real-time',
      filters: ['*'],
      options: {
        deleteExtraFiles: false,
        preserveTimestamps: true
      }
    };
    
    try {
      const response = await fetch(`${apiUrl}/sync/tasks`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(taskData)
      });
      
      this.assert(response.ok, 'Task creation API call successful');
      
      const result = await response.json();
      this.taskId = result.task.id;
      this.assert(!!this.taskId, 'Task ID received');
      this.assert(result.task.syncType === 'real-time', 'Task has real-time sync type');
      
      console.log(`✅ Created test task with ID: ${this.taskId}\n`);
    } catch (error) {
      this.assert(false, `Task creation failed: ${error.message}`);
    }
  }

  async test3_StartRealtimeSync() {
    this.logTest('Start Real-time Sync');
    
    if (!this.taskId) {
      this.assert(false, 'No task ID available');
      return;
    }
    
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
    
    try {
      const response = await fetch(`${apiUrl}/sync/tasks/${this.taskId}/realtime/start`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      this.assert(response.ok, 'Real-time sync start API call successful');
      
      const result = await response.json();
      this.assert(result.status === 'monitoring', 'Task status is monitoring');
      
      console.log('✅ Real-time sync started successfully\n');
      
      // Wait a bit for the watcher to initialize
      await this.delay(2000);
      
    } catch (error) {
      this.assert(false, `Real-time sync start failed: ${error.message}`);
    }
  }

  async test4_TestSocketEvents() {
    this.logTest('Socket.IO Events');
    
    const socket = window.socket;
    if (!socket) {
      this.assert(false, 'Socket.IO not available');
      return;
    }
    
    // Set up event listeners
    const events = [
      'realtime-sync-started',
      'file-change-detected',
      'realtime-sync-processing',
      'realtime-sync-completed',
      'realtime-sync-error'
    ];
    
    const receivedEvents = [];
    
    events.forEach(event => {
      socket.on(event, (data) => {
        console.log(`📡 Received ${event}:`, data);
        receivedEvents.push(event);
      });
    });
    
    // Simulate file change (if possible)
    console.log('💡 To test file changes:');
    console.log('   1. Create/modify files in: C:\\Test\\Source');
    console.log('   2. Watch for real-time events in console');
    console.log('   3. Check destination folder for synced files');
    
    this.assert(true, 'Socket event listeners set up');
    console.log('✅ Socket events test setup completed\n');
  }

  async test5_StopRealtimeSync() {
    this.logTest('Stop Real-time Sync');
    
    if (!this.taskId) {
      this.assert(false, 'No task ID available');
      return;
    }
    
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
    
    try {
      const response = await fetch(`${apiUrl}/sync/tasks/${this.taskId}/realtime/stop`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      this.assert(response.ok, 'Real-time sync stop API call successful');
      
      const result = await response.json();
      this.assert(result.status === 'idle', 'Task status is idle');
      
      console.log('✅ Real-time sync stopped successfully\n');
      
    } catch (error) {
      this.assert(false, `Real-time sync stop failed: ${error.message}`);
    }
  }

  async test6_CleanupTask() {
    this.logTest('Cleanup Test Task');
    
    if (!this.taskId) {
      this.assert(false, 'No task ID available');
      return;
    }
    
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
    
    try {
      const response = await fetch(`${apiUrl}/sync/tasks/${this.taskId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      this.assert(response.ok, 'Task deletion API call successful');
      
      console.log('✅ Test task cleaned up successfully\n');
      
    } catch (error) {
      this.assert(false, `Task cleanup failed: ${error.message}`);
    }
  }

  logTest(testName) {
    console.log(`🔸 Test: ${testName}`);
    console.log('─'.repeat(40));
  }

  assert(condition, message) {
    const result = {
      test: message,
      passed: condition,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    if (condition) {
      console.log(`✅ ${message}`);
    } else {
      console.log(`❌ ${message}`);
    }
  }

  showResults() {
    console.log('\n📊 Test Results Summary');
    console.log('═'.repeat(50));
    
    const passed = this.testResults.filter(r => r.passed).length;
    const total = this.testResults.length;
    
    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${total - passed}`);
    console.log(`Success Rate: ${Math.round((passed / total) * 100)}%`);
    
    console.log('\n📋 Detailed Results:');
    this.testResults.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.test}`);
    });
    
    if (passed === total) {
      console.log('\n🎉 All tests passed! Real-time sync is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Check the implementation.');
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Manual testing functions
window.realtimeSyncTester = {
  // Run full test suite
  runTests: async () => {
    const tester = new RealtimeSyncTester();
    await tester.runAllTests();
  },

  // Test file change simulation
  simulateFileChange: (filePath = 'test.txt') => {
    console.log(`🔄 Simulating file change: ${filePath}`);
    console.log('💡 In real scenario, this would be detected by chokidar');
    console.log('📂 Create/modify a file in your source folder to test');
  },

  // Monitor real-time events
  monitorEvents: () => {
    const socket = window.socket;
    if (!socket) {
      console.log('❌ Socket.IO not available');
      return;
    }

    console.log('👀 Monitoring real-time sync events...');
    
    const events = [
      'realtime-sync-started',
      'file-change-detected', 
      'realtime-sync-processing',
      'realtime-sync-completed',
      'realtime-sync-stopped',
      'realtime-sync-error'
    ];

    events.forEach(event => {
      socket.on(event, (data) => {
        console.log(`📡 ${event}:`, data);
      });
    });

    console.log('✅ Event monitoring started');
  },

  // Check real-time sync status
  checkStatus: async () => {
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
    
    try {
      const response = await fetch(`${apiUrl}/sync/realtime/status`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      const data = await response.json();
      console.log('📊 Real-time sync status:', data);
      
      return data;
    } catch (error) {
      console.error('❌ Failed to check status:', error);
    }
  }
};

// Auto-run tests
console.log('🎯 Real-time Sync Tester loaded!');
console.log('📝 Available commands:');
console.log('   - window.realtimeSyncTester.runTests()');
console.log('   - window.realtimeSyncTester.monitorEvents()');
console.log('   - window.realtimeSyncTester.checkStatus()');
console.log('   - window.realtimeSyncTester.simulateFileChange()');
console.log('');
