// Package web application for deployment
const fs = require('fs').promises;
const path = require('path');
const archiver = require('archiver');

async function packageWebApp() {
  console.log('📦 Packaging Web Application for Deployment\n');
  
  try {
    // Create dist-web directory
    const distWebDir = path.join(__dirname, '../dist-web');
    await fs.mkdir(distWebDir, { recursive: true });
    console.log('✅ Created dist-web directory');
    
    // Copy necessary files for web deployment
    const filesToCopy = [
      { src: 'build', dest: 'build' },
      { src: 'server', dest: 'server' },
      { src: 'database', dest: 'database' },
      { src: 'package.json', dest: 'package.json' },
      { src: '.env.web', dest: '.env' }
    ];
    
    for (const file of filesToCopy) {
      const srcPath = path.join(__dirname, '..', file.src);
      const destPath = path.join(distWebDir, file.dest);
      
      try {
        const stat = await fs.stat(srcPath);
        
        if (stat.isDirectory()) {
          await copyDirectory(srcPath, destPath);
          console.log(`✅ Copied directory: ${file.src} → ${file.dest}`);
        } else {
          await fs.mkdir(path.dirname(destPath), { recursive: true });
          await fs.copyFile(srcPath, destPath);
          console.log(`✅ Copied file: ${file.src} → ${file.dest}`);
        }
      } catch (error) {
        if (file.src === '.env.web') {
          console.log(`⚠️ Skipped ${file.src} (not found, will use default .env)`);
        } else {
          console.log(`❌ Failed to copy ${file.src}: ${error.message}`);
        }
      }
    }
    
    // Create web-specific package.json
    const webPackageJson = {
      "name": "syncmasterpro-web",
      "version": "1.0.0",
      "description": "SyncMasterPro Web Server",
      "main": "server/index.js",
      "scripts": {
        "start": "node server/index.js",
        "setup": "node scripts/setup.js"
      },
      "dependencies": {
        "express": "^4.18.2",
        "cors": "^2.8.5",
        "dotenv": "^16.3.1",
        "socket.io": "^4.7.4",
        "bcryptjs": "^2.4.3",
        "jsonwebtoken": "^9.0.2",
        "pg": "^8.11.3",
        "winston": "^3.11.0",
        "winston-daily-rotate-file": "^4.7.1",
        "multer": "^1.4.5-lts.1",
        "moment": "^2.29.4"
      }
    };
    
    await fs.writeFile(
      path.join(distWebDir, 'package.json'),
      JSON.stringify(webPackageJson, null, 2)
    );
    console.log('✅ Created web-specific package.json');
    
    // Create deployment instructions
    const deployInstructions = `# SyncMasterPro Web Deployment

## Quick Start

1. **Install dependencies:**
   \`\`\`bash
   npm install
   \`\`\`

2. **Configure environment:**
   \`\`\`bash
   # Edit .env file with your PostgreSQL settings
   nano .env
   \`\`\`

3. **Start the server:**
   \`\`\`bash
   npm start
   \`\`\`

## Environment Configuration

Update the \`.env\` file with your settings:

\`\`\`env
# Database configuration
DB_TYPE=postgresql
DB_HOST=your-postgres-host
DB_USER=your-postgres-user
DB_PASSWORD=your-postgres-password
DB_NAME=syncmasterpro
DB_PORT=5432

# Server configuration
PORT=5001
NODE_ENV=production

# Client URL for CORS
CLIENT_URL=http://your-domain.com

# JWT Secret (change this!)
JWT_SECRET=your-super-secret-jwt-key-here
\`\`\`

## Production Deployment

### Using PM2 (Recommended)

1. **Install PM2:**
   \`\`\`bash
   npm install -g pm2
   \`\`\`

2. **Start with PM2:**
   \`\`\`bash
   pm2 start server/index.js --name "syncmasterpro-web"
   pm2 save
   pm2 startup
   \`\`\`

### Using Docker

1. **Build Docker image:**
   \`\`\`bash
   docker build -t syncmasterpro-web .
   \`\`\`

2. **Run container:**
   \`\`\`bash
   docker run -d -p 5001:5001 --env-file .env syncmasterpro-web
   \`\`\`

## Nginx Configuration

\`\`\`nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
\`\`\`

## Database Setup

1. **Create PostgreSQL database:**
   \`\`\`sql
   CREATE DATABASE syncmasterpro;
   CREATE USER syncuser WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE syncmasterpro TO syncuser;
   \`\`\`

2. **Tables will be created automatically on first run**

## Monitoring

- **Logs:** Check \`logs/\` directory
- **Health:** GET \`/api/health\`
- **Status:** Monitor with PM2 or your preferred tool

## Security

- Change JWT_SECRET in production
- Use HTTPS in production
- Configure firewall rules
- Regular database backups
`;
    
    await fs.writeFile(
      path.join(distWebDir, 'DEPLOYMENT.md'),
      deployInstructions
    );
    console.log('✅ Created deployment instructions');
    
    // Create Dockerfile
    const dockerfile = `FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application files
COPY . .

# Create necessary directories
RUN mkdir -p logs data uploads temp

# Expose port
EXPOSE 5001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
  CMD curl -f http://localhost:5001/api/health || exit 1

# Start application
CMD ["npm", "start"]
`;
    
    await fs.writeFile(
      path.join(distWebDir, 'Dockerfile'),
      dockerfile
    );
    console.log('✅ Created Dockerfile');
    
    // Create archive
    const archivePath = path.join(__dirname, '../dist/syncmasterpro-web.zip');
    await fs.mkdir(path.dirname(archivePath), { recursive: true });
    
    await createArchive(distWebDir, archivePath);
    console.log(`✅ Created deployment archive: ${archivePath}`);
    
    console.log('\n🎉 Web application packaging completed!');
    console.log('\n📋 Deployment files created:');
    console.log(`   📁 dist-web/           - Web deployment files`);
    console.log(`   📦 dist/syncmasterpro-web.zip - Deployment archive`);
    console.log(`   📖 dist-web/DEPLOYMENT.md - Deployment instructions`);
    
  } catch (error) {
    console.error('❌ Packaging failed:', error);
    process.exit(1);
  }
}

async function copyDirectory(src, dest) {
  await fs.mkdir(dest, { recursive: true });
  const entries = await fs.readdir(src, { withFileTypes: true });
  
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);
    
    if (entry.isDirectory()) {
      await copyDirectory(srcPath, destPath);
    } else {
      await fs.copyFile(srcPath, destPath);
    }
  }
}

async function createArchive(sourceDir, outputPath) {
  return new Promise((resolve, reject) => {
    const output = require('fs').createWriteStream(outputPath);
    const archive = archiver('zip', { zlib: { level: 9 } });
    
    output.on('close', () => {
      console.log(`📦 Archive created: ${archive.pointer()} bytes`);
      resolve();
    });
    
    archive.on('error', reject);
    archive.pipe(output);
    archive.directory(sourceDir, false);
    archive.finalize();
  });
}

// Export for manual use
module.exports = { packageWebApp };

// Auto-run if called directly
if (require.main === module) {
  packageWebApp();
}

console.log('📦 Web Packager loaded!');
console.log('📝 Run: npm run package-web');
