import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';
import { useNotification } from '../contexts/NotificationContext';
import ResetPassword from '../components/Auth/ResetPassword';

const ResetPasswordPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { addNotification } = useNotification();
  const [token, setToken] = useState('');

  useEffect(() => {
    const resetToken = searchParams.get('token');
    if (resetToken) {
      setToken(resetToken);
    } else {
      // No token provided, redirect to login
      addNotification(t('invalidResetToken'), 'error');
      navigate('/login');
    }
  }, [searchParams, navigate, addNotification, t]);

  const handleSuccess = (data) => {
    addNotification(t('passwordResetSuccess'), 'success');
    // Redirect to login after successful reset
    setTimeout(() => {
      navigate('/login');
    }, 2000);
  };

  const handleError = (error) => {
    addNotification(error.message || t('resetPasswordError'), 'error');
    // If token is invalid, redirect to login after delay
    if (error.message?.includes('Invalid') || error.message?.includes('expired')) {
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    }
  };

  if (!token) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">{t('loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <ResetPassword
      token={token}
      onSuccess={handleSuccess}
      onError={handleError}
    />
  );
};

export default ResetPasswordPage;
