const { getDatabase, initializeDatabase } = require('../server/database/init');

async function clearAllSessions() {
  console.log('🗑️ Clearing All Sessions from Database\n');

  try {
    // Initialize database connection
    await initializeDatabase();
    const db = getDatabase();
    console.log('✅ Connected to database');

    // Check current sessions before deletion
    console.log('\n📊 BEFORE DELETION:');
    console.log('=' .repeat(50));
    
    const beforeResult = await db.query(`
      SELECT 
        COUNT(*) as total_sessions,
        COUNT(CASE WHEN expires_at > datetime('now') THEN 1 END) as active_sessions,
        COUNT(CASE WHEN expires_at <= datetime('now') THEN 1 END) as expired_sessions
      FROM sessions
    `);

    const beforeStats = beforeResult.rows ? beforeResult.rows[0] : beforeResult[0];
    console.log(`📋 Total sessions: ${beforeStats.total_sessions}`);
    console.log(`🟢 Active sessions: ${beforeStats.active_sessions}`);
    console.log(`🔴 Expired sessions: ${beforeStats.expired_sessions}`);

    // Show sessions by user
    const userSessions = await db.query(`
      SELECT 
        s.user_id,
        u.email,
        COUNT(*) as session_count,
        COUNT(CASE WHEN s.expires_at > datetime('now') THEN 1 END) as active_count
      FROM sessions s
      LEFT JOIN users u ON s.user_id = u.id
      GROUP BY s.user_id, u.email
      ORDER BY session_count DESC
    `);

    const userSessionRows = userSessions.rows || userSessions;
    if (userSessionRows.length > 0) {
      console.log('\n👥 Sessions by User:');
      userSessionRows.forEach(row => {
        console.log(`   👤 ${row.email || 'Unknown'} (ID: ${row.user_id}): ${row.session_count} total, ${row.active_count} active`);
      });
    }

    // Show recent sessions
    const recentSessions = await db.query(`
      SELECT 
        s.id,
        s.user_id,
        u.email,
        s.created_at,
        s.expires_at,
        CASE WHEN s.expires_at > datetime('now') THEN 'Active' ELSE 'Expired' END as status
      FROM sessions s
      LEFT JOIN users u ON s.user_id = u.id
      ORDER BY s.created_at DESC
      LIMIT 10
    `);

    const recentRows = recentSessions.rows || recentSessions;
    if (recentRows.length > 0) {
      console.log('\n📋 Recent Sessions (Last 10):');
      recentRows.forEach((row, index) => {
        console.log(`   ${index + 1}. ID: ${row.id} | User: ${row.email || 'Unknown'} | Status: ${row.status}`);
        console.log(`      Created: ${row.created_at} | Expires: ${row.expires_at}`);
      });
    }

    // Confirm deletion
    console.log('\n⚠️ WARNING: This will delete ALL sessions!');
    console.log('   - All users will be logged out');
    console.log('   - Desktop clients will need to login again');
    console.log('   - Web sessions will be invalidated');

    // Delete all sessions
    console.log('\n🗑️ DELETING ALL SESSIONS...');
    
    const deleteResult = await db.query('DELETE FROM sessions');
    const deletedCount = deleteResult.affectedRows || deleteResult.rowCount || deleteResult.changes || 0;
    
    console.log(`✅ Deleted ${deletedCount} sessions`);

    // Verify deletion
    console.log('\n📊 AFTER DELETION:');
    console.log('=' .repeat(50));
    
    const afterResult = await db.query('SELECT COUNT(*) as remaining_sessions FROM sessions');
    const afterStats = afterResult.rows ? afterResult.rows[0] : afterResult[0];
    console.log(`📋 Remaining sessions: ${afterStats.remaining_sessions}`);

    if (afterStats.remaining_sessions === 0) {
      console.log('✅ All sessions successfully deleted');
    } else {
      console.log(`⚠️ ${afterStats.remaining_sessions} sessions still remain`);
    }

    // Additional cleanup - remove expired sessions from desktop_clients if needed
    console.log('\n🖥️ CHECKING DESKTOP CLIENTS...');
    
    const clientsResult = await db.query(`
      SELECT COUNT(*) as total_clients,
             COUNT(CASE WHEN status = 'online' THEN 1 END) as online_clients,
             COUNT(CASE WHEN status = 'offline' THEN 1 END) as offline_clients
      FROM desktop_clients
    `);

    const clientStats = clientsResult.rows ? clientsResult.rows[0] : clientsResult[0];
    console.log(`📋 Total clients: ${clientStats.total_clients}`);
    console.log(`🟢 Online clients: ${clientStats.online_clients}`);
    console.log(`🔴 Offline clients: ${clientStats.offline_clients}`);

    // Optionally mark all clients as offline since sessions are cleared
    if (clientStats.online_clients > 0) {
      console.log('\n📴 Marking all clients as offline (since sessions are cleared)...');
      
      const updateResult = await db.query(`
        UPDATE desktop_clients 
        SET status = 'offline', last_seen = datetime('now')
        WHERE status = 'online'
      `);
      
      const updatedCount = updateResult.affectedRows || updateResult.rowCount || updateResult.changes || 0;
      console.log(`✅ Marked ${updatedCount} clients as offline`);
    }

    console.log('\n🎉 SESSION CLEANUP COMPLETED!');
    console.log('\n💡 NEXT STEPS:');
    console.log('   1. All users need to login again');
    console.log('   2. Desktop clients will reconnect automatically');
    console.log('   3. Web sessions are now clean');
    console.log('   4. Check Client Management to verify status');

  } catch (error) {
    console.error('❌ Failed to clear sessions:', error);
    
    if (error.message.includes('no such table')) {
      console.log('💡 Sessions table does not exist yet');
      console.log('   Start servers first to create database tables');
    }
  }
}

// Run cleanup
clearAllSessions();
