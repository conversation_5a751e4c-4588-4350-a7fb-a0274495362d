// Check SQLite database specifically
process.env.DB_TYPE = 'sqlite';
process.env.ELECTRON_ENV = 'true';

const { initializeDatabase, getDatabase } = require('../server/database/init');

async function checkSQLiteDatabase() {
  console.log('🔍 Checking SQLite Database Structure\n');
  
  try {
    // Initialize SQLite database
    console.log('1. 🗄️ Initializing SQLite database...');
    await initializeDatabase();
    console.log('✅ SQLite database initialized successfully');
    
    const db = getDatabase();
    
    // Check if desktop_clients table exists
    console.log('\n2. 📋 Checking desktop_clients table...');
    
    try {
      const clientsTableInfo = await db.query("PRAGMA table_info(desktop_clients)");
      
      if (clientsTableInfo.rows && clientsTableInfo.rows.length > 0) {
        console.log('✅ desktop_clients table exists');
        console.log('📊 Columns:');
        clientsTableInfo.rows.forEach(col => {
          console.log(`   - ${col.name}: ${col.type}`);
        });
      } else {
        console.log('❌ desktop_clients table does not exist');
      }
    } catch (error) {
      console.log('❌ Error checking desktop_clients table:', error.message);
    }
    
    // Check if client_commands table exists
    console.log('\n3. 📋 Checking client_commands table...');
    
    try {
      const commandsTableInfo = await db.query("PRAGMA table_info(client_commands)");
      
      if (commandsTableInfo.rows && commandsTableInfo.rows.length > 0) {
        console.log('✅ client_commands table exists');
        console.log('📊 Columns:');
        commandsTableInfo.rows.forEach(col => {
          console.log(`   - ${col.name}: ${col.type}`);
        });
      } else {
        console.log('❌ client_commands table does not exist');
      }
    } catch (error) {
      console.log('❌ Error checking client_commands table:', error.message);
    }
    
    // Check sync_tasks for client_id column
    console.log('\n4. 📋 Checking sync_tasks table...');
    
    try {
      const syncTasksTableInfo = await db.query("PRAGMA table_info(sync_tasks)");
      
      if (syncTasksTableInfo.rows && syncTasksTableInfo.rows.length > 0) {
        console.log('✅ sync_tasks table exists');
        
        const hasClientId = syncTasksTableInfo.rows.some(col => col.name === 'client_id');
        if (hasClientId) {
          console.log('✅ client_id column exists in sync_tasks');
        } else {
          console.log('❌ client_id column missing in sync_tasks');
        }
        
        console.log('📊 Key columns:');
        syncTasksTableInfo.rows.filter(col => 
          ['id', 'user_id', 'client_id', 'name', 'status'].includes(col.name)
        ).forEach(col => {
          console.log(`   - ${col.name}: ${col.type}`);
        });
      } else {
        console.log('❌ sync_tasks table does not exist');
      }
    } catch (error) {
      console.log('❌ Error checking sync_tasks table:', error.message);
    }
    
    // List all tables
    console.log('\n5. 📋 All tables in SQLite database:');
    
    try {
      const allTables = await db.query("SELECT name FROM sqlite_master WHERE type='table'");
      
      if (allTables.rows && allTables.rows.length > 0) {
        allTables.rows.forEach(table => {
          console.log(`   - ${table.name}`);
        });
        console.log(`\n📊 Total tables: ${allTables.rows.length}`);
      } else {
        console.log('❌ No tables found');
      }
    } catch (error) {
      console.log('❌ Error listing tables:', error.message);
    }
    
    // Test basic queries
    console.log('\n6. 🧪 Testing basic queries...');
    
    try {
      // Test users table
      const usersCount = await db.query("SELECT COUNT(*) as count FROM users");
      console.log(`✅ Users table: ${usersCount.rows[0].count} users`);
      
      // Test sync_tasks table
      const tasksCount = await db.query("SELECT COUNT(*) as count FROM sync_tasks");
      console.log(`✅ Sync tasks table: ${tasksCount.rows[0].count} tasks`);
      
      // Test desktop_clients table
      try {
        const clientsCount = await db.query("SELECT COUNT(*) as count FROM desktop_clients");
        console.log(`✅ Desktop clients table: ${clientsCount.rows[0].count} clients`);
      } catch (error) {
        console.log('❌ Desktop clients table query failed:', error.message);
      }
      
      // Test client_commands table
      try {
        const commandsCount = await db.query("SELECT COUNT(*) as count FROM client_commands");
        console.log(`✅ Client commands table: ${commandsCount.rows[0].count} commands`);
      } catch (error) {
        console.log('❌ Client commands table query failed:', error.message);
      }
      
    } catch (error) {
      console.log('❌ Error testing queries:', error.message);
    }
    
    console.log('\n📊 SQLITE DATABASE CHECK SUMMARY:');
    console.log('   Database initialization: ✅ SUCCESS');
    console.log('   Core tables: ✅ EXIST');
    console.log('   Client management tables: Check above results');
    console.log('   Basic queries: ✅ WORKING');
    
    console.log('\n💡 If client management tables are missing:');
    console.log('   1. Delete data/syncmasterpro.db');
    console.log('   2. Restart desktop server: npm run start-desktop');
    console.log('   3. Tables will be recreated automatically');
    
    console.log('\n🎯 NEXT STEPS:');
    console.log('   1. If tables exist: npm run test-web');
    console.log('   2. If tables missing: Delete DB and restart desktop');
    console.log('   3. Start web server: npm run start-web');
    
  } catch (error) {
    console.error('❌ SQLite database check failed:', error.message);
    console.error('Stack:', error.stack);
    
    console.log('\n🔧 TROUBLESHOOTING:');
    console.log('   1. Make sure data/ directory exists');
    console.log('   2. Check file permissions');
    console.log('   3. Try: npm run start-desktop (creates DB)');
  }
}

// Auto-run if called directly
if (require.main === module) {
  checkSQLiteDatabase().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { checkSQLiteDatabase };

console.log('🔍 SQLite Database Checker loaded!');
console.log('📝 Run: node scripts/check-sqlite.js');
