version: '3.8'

services:
  adminer:
    image: adminer:latest
    container_name: syncmasterpro-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      # Mount project directory to access SQLite files
      - .:/var/www/html/project:ro
      # Mount database directory specifically
      - ./database:/var/www/html/database:ro
      - ./data:/var/www/html/data:ro
    environment:
      ADMINER_DEFAULT_SERVER: sqlite
      ADMINER_DESIGN: pepa-linha-dark
    networks:
      - adminer-network

networks:
  adminer-network:
    driver: bridge
