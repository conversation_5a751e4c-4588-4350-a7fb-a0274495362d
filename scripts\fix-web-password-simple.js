#!/usr/bin/env node

/**
 * Fix password hash for web server authentication (simple version)
 */

const { Pool } = require('pg');

console.log('🔧 Fixing Web Server Password Hash (Simple)');
console.log('===========================================');

async function fixPasswordWithKnownHash() {
  console.log('\n1. 🐘 Connecting to PostgreSQL...');
  
  const pool = new Pool({
    host: '*************',
    port: 5432,
    database: 'syncmasterpro',
    user: 'pi',
    password: 'ubuntu'
  });

  try {
    const client = await pool.connect();
    console.log('✅ Connected to PostgreSQL');
    
    // Use a known working hash for 'admin' password
    // This hash was generated with bcrypt.hash('admin', 12)
    const knownWorkingHash = '$2a$12$LQv3c1yqBWVHxkd0LQ4YCOWLxdv0H.t8.8FIFozK8elCqyeuFLaGC';
    
    console.log('\n2. 💾 Updating password with known working hash...');
    
    const updateResult = await client.query(
      'UPDATE users SET password = $1, updated_at = CURRENT_TIMESTAMP WHERE email = $2',
      [knownWorkingHash, '<EMAIL>']
    );
    
    if (updateResult.rowCount > 0) {
      console.log('✅ Password hash updated successfully');
    } else {
      console.log('❌ No user found to update');
    }
    
    // Clean up old sessions to force fresh login
    console.log('\n3. 🧹 Cleaning up old sessions...');
    const deleteResult = await client.query('DELETE FROM sessions');
    console.log(`✅ Deleted ${deleteResult.rowCount} old sessions`);
    
    client.release();
    await pool.end();
    
  } catch (error) {
    console.error('❌ PostgreSQL error:', error.message);
    await pool.end();
    throw error;
  }
}

async function testLogin() {
  console.log('\n4. 🔐 Testing web server login...');
  
  const axios = require('axios');
  
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Login Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Web server login successful!');
      console.log('👤 User:', response.data.user.name);
      console.log('🔑 Token length:', response.data.token.length);
      return true;
    } else {
      console.log('❌ Web server login failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Web server login error:', error.message);
    return false;
  }
}

async function testWebUIAccess() {
  console.log('\n5. 🌐 Testing web UI access...');
  
  const axios = require('axios');
  
  try {
    const response = await axios.get('http://localhost:3001', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📝 Web UI Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Web UI accessible!');
      return true;
    } else {
      console.log('❌ Web UI not accessible');
      return false;
    }
  } catch (error) {
    console.log('❌ Web UI access error:', error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting password fix...\n');
    
    // Fix password hash
    await fixPasswordWithKnownHash();
    
    // Test login
    const loginSuccess = await testLogin();
    
    // Test web UI
    const webUIWorking = await testWebUIAccess();
    
    console.log('\n📊 Summary:');
    console.log('===========');
    console.log('- Password Hash Update:', '✅ Success');
    console.log('- Web Server Login:', loginSuccess ? '✅ Success' : '❌ Failed');
    console.log('- Web UI Access:', webUIWorking ? '✅ Success' : '❌ Failed');
    
    if (loginSuccess && webUIWorking) {
      console.log('\n🎉 SUCCESS! Everything is working!');
      console.log('🔍 You can now:');
      console.log('   1. Open http://localhost:3001 in browser');
      console.log('   2. <NAME_EMAIL> / admin');
      console.log('   3. View real desktop client data');
    } else if (loginSuccess) {
      console.log('\n✅ Authentication fixed! Web UI may need restart');
      console.log('💡 Try refreshing the web UI page');
    } else {
      console.log('\n❌ Authentication still has issues');
      console.log('💡 Check web server logs for more details');
    }
    
  } catch (error) {
    console.error('\n❌ Fix failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
