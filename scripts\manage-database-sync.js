const DatabaseSyncService = require('../server/services/DatabaseSyncService');
const RealTimeDatabaseSync = require('../server/services/RealTimeDatabaseSync');
const DatabaseChangeListener = require('../server/services/DatabaseChangeListener');

async function manageDatabaseSync() {
  console.log('🔧 Database Sync Management Tool\n');

  const command = process.argv[2];
  const options = process.argv.slice(3);

  if (!command) {
    showHelp();
    return;
  }

  try {
    switch (command) {
      case 'status':
        await showStatus();
        break;
      case 'sync':
        await performSync(options);
        break;
      case 'start':
        await startSync();
        break;
      case 'stop':
        await stopSync();
        break;
      case 'test':
        await testConnections();
        break;
      case 'compare':
        await compareData();
        break;
      case 'consistency':
        await checkConsistency();
        break;
      case 'config':
        await showConfig();
        break;
      default:
        console.log(`❌ Unknown command: ${command}`);
        showHelp();
    }
  } catch (error) {
    console.error('❌ Command failed:', error.message);
    process.exit(1);
  }
}

function showHelp() {
  console.log('📋 AVAILABLE COMMANDS:');
  console.log('=' .repeat(50));
  console.log('');
  console.log('🔍 status          - Show database sync status');
  console.log('🔄 sync [tables]   - Perform manual sync (optional: specific tables)');
  console.log('🚀 start           - Start auto sync services');
  console.log('🛑 stop            - Stop auto sync services');
  console.log('🔌 test            - Test database connections');
  console.log('📊 compare         - Compare data between databases');
  console.log('✅ consistency     - Check data consistency');
  console.log('⚙️ config          - Show current configuration');
  console.log('');
  console.log('📝 EXAMPLES:');
  console.log('  node scripts/manage-database-sync.js status');
  console.log('  node scripts/manage-database-sync.js sync');
  console.log('  node scripts/manage-database-sync.js sync users sync_tasks');
  console.log('  node scripts/manage-database-sync.js start');
  console.log('  node scripts/manage-database-sync.js test');
}

async function showStatus() {
  console.log('📊 DATABASE SYNC STATUS:');
  console.log('=' .repeat(50));

  const dbSyncService = new DatabaseSyncService();
  
  try {
    await dbSyncService.initialize();
    
    const status = dbSyncService.getStatus();
    
    console.log(`🔄 Auto Sync: ${status.isRunning ? '✅ Running' : '❌ Stopped'}`);
    console.log(`⏰ Last Sync: ${status.lastSyncTime || 'Never'}`);
    console.log(`🎯 Direction: ${status.config.syncDirection}`);
    console.log(`⏱️ Interval: ${status.config.syncIntervalMinutes} minutes`);
    console.log(`🔧 Smart Sync: ${status.config.enableSmartSync ? '✅ Enabled' : '❌ Disabled'}`);
    console.log(`🗑️ Deletion Sync: ${status.config.enableDeletionSync ? '✅ Enabled' : '❌ Disabled'}`);
    console.log(`✅ Consistency Check: ${status.config.enableConsistencyCheck ? '✅ Enabled' : '❌ Disabled'}`);
    
    console.log('\n🌍 ENVIRONMENT:');
    console.log(`  ENABLE_DB_SYNC: ${process.env.ENABLE_DB_SYNC}`);
    console.log(`  DB_SYNC_INTERVAL: ${process.env.DB_SYNC_INTERVAL}`);
    console.log(`  DB_SYNC_DIRECTION: ${process.env.DB_SYNC_DIRECTION}`);
    
    await dbSyncService.close();
    
  } catch (error) {
    console.error('❌ Failed to get status:', error.message);
  }
}

async function performSync(options) {
  console.log('🔄 PERFORMING DATABASE SYNC:');
  console.log('=' .repeat(50));

  const dbSyncService = new DatabaseSyncService();
  
  try {
    await dbSyncService.initialize();
    
    let result;
    
    if (options.length > 0) {
      // Targeted sync for specific tables
      console.log(`🎯 Syncing specific tables: ${options.join(', ')}`);
      result = await dbSyncService.syncSpecificTables(options);
    } else {
      // Full sync
      console.log('🔄 Performing full sync...');
      result = await dbSyncService.performSync();
    }
    
    console.log('\n📊 SYNC RESULTS:');
    console.log(`⏱️ Duration: ${result.duration}ms`);
    console.log(`🎯 Direction: ${result.direction}`);
    
    if (result.tables) {
      Object.entries(result.tables).forEach(([table, stats]) => {
        if (stats.sqliteToPg && stats.pgToSqlite) {
          // Bidirectional sync
          console.log(`\n📋 ${table}:`);
          console.log(`  SQLite → PostgreSQL: ${stats.sqliteToPg.synced} synced, ${stats.sqliteToPg.skipped} skipped, ${stats.sqliteToPg.errors} errors`);
          console.log(`  PostgreSQL → SQLite: ${stats.pgToSqlite.synced} synced, ${stats.pgToSqlite.skipped} skipped, ${stats.pgToSqlite.errors} errors`);
        } else {
          // Unidirectional sync
          console.log(`📋 ${table}: ${stats.synced} synced, ${stats.skipped} skipped, ${stats.errors} errors, ${stats.deleted || 0} deleted`);
        }
      });
    }
    
    console.log('\n✅ Sync completed successfully!');
    
    await dbSyncService.close();
    
  } catch (error) {
    console.error('❌ Sync failed:', error.message);
  }
}

async function startSync() {
  console.log('🚀 STARTING DATABASE SYNC SERVICES:');
  console.log('=' .repeat(50));

  const dbSyncService = new DatabaseSyncService();
  const realTimeSync = new RealTimeDatabaseSync(dbSyncService);
  const changeListener = new DatabaseChangeListener();
  
  try {
    await dbSyncService.initialize();
    
    // Initialize services
    changeListener.initialize(dbSyncService, realTimeSync);
    
    // Start services
    realTimeSync.enable();
    changeListener.startListening();
    dbSyncService.startAutoSync();
    
    console.log('✅ Scheduled sync: Started');
    console.log('✅ Real-time sync: Enabled');
    console.log('✅ Change listener: Listening');
    
    console.log('\n💡 Services are now running. Press Ctrl+C to stop.');
    
    // Keep process alive
    process.on('SIGINT', async () => {
      console.log('\n🛑 Stopping services...');
      
      dbSyncService.stopAutoSync();
      realTimeSync.disable();
      changeListener.stopListening();
      await dbSyncService.close();
      
      console.log('✅ Services stopped');
      process.exit(0);
    });
    
    // Keep alive
    setInterval(() => {
      // Just keep the process running
    }, 1000);
    
  } catch (error) {
    console.error('❌ Failed to start services:', error.message);
  }
}

async function stopSync() {
  console.log('🛑 STOPPING DATABASE SYNC SERVICES:');
  console.log('=' .repeat(50));
  
  // This would typically connect to running services and stop them
  // For now, just show instructions
  console.log('💡 To stop running sync services:');
  console.log('  1. Stop the desktop server (Ctrl+C)');
  console.log('  2. Stop the web server (Ctrl+C)');
  console.log('  3. Or use the API: POST /api/database-sync/stop');
}

async function testConnections() {
  console.log('🔌 TESTING DATABASE CONNECTIONS:');
  console.log('=' .repeat(50));

  const dbSyncService = new DatabaseSyncService();
  
  try {
    await dbSyncService.initialize();
    
    console.log('✅ SQLite: Connected');
    console.log('✅ PostgreSQL: Connected');
    
    await dbSyncService.close();
    
  } catch (error) {
    console.error('❌ Connection test failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('💡 PostgreSQL server may not be running');
      console.log('   Check connection details in .env files');
    }
  }
}

async function compareData() {
  console.log('📊 COMPARING DATABASE DATA:');
  console.log('=' .repeat(50));

  const dbSyncService = new DatabaseSyncService();
  
  try {
    await dbSyncService.initialize();
    
    const comparison = await dbSyncService.compareData();
    
    console.log(`📊 Overall Consistency: ${comparison.isConsistent ? '✅ Consistent' : '❌ Inconsistent'}`);
    console.log(`📋 Tables Checked: ${comparison.totalTables}`);
    console.log(`⚠️ Inconsistencies: ${comparison.totalInconsistencies}`);
    
    if (comparison.tables) {
      Object.entries(comparison.tables).forEach(([table, stats]) => {
        const status = stats.isConsistent ? '✅' : '❌';
        console.log(`  ${status} ${table}: SQLite(${stats.sqliteCount}) vs PostgreSQL(${stats.postgresqlCount})`);
        
        if (!stats.isConsistent && stats.differences) {
          stats.differences.forEach(diff => {
            console.log(`    - ${diff}`);
          });
        }
      });
    }
    
    await dbSyncService.close();
    
  } catch (error) {
    console.error('❌ Data comparison failed:', error.message);
  }
}

async function checkConsistency() {
  console.log('✅ CHECKING DATA CONSISTENCY:');
  console.log('=' .repeat(50));

  const dbSyncService = new DatabaseSyncService();
  
  try {
    await dbSyncService.initialize();
    
    const result = await dbSyncService.performConsistencyCheck();
    
    console.log(`📊 Consistency Check: ${result.isConsistent ? '✅ Passed' : '❌ Failed'}`);
    console.log(`⏱️ Duration: ${result.duration}ms`);
    
    if (result.details) {
      Object.entries(result.details).forEach(([table, details]) => {
        console.log(`📋 ${table}: ${details.isConsistent ? '✅ Consistent' : '❌ Inconsistent'}`);
      });
    }
    
    await dbSyncService.close();
    
  } catch (error) {
    console.error('❌ Consistency check failed:', error.message);
  }
}

async function showConfig() {
  console.log('⚙️ DATABASE SYNC CONFIGURATION:');
  console.log('=' .repeat(50));

  console.log('\n🌍 ENVIRONMENT VARIABLES:');
  console.log(`  ENABLE_DB_SYNC: ${process.env.ENABLE_DB_SYNC || 'not set'}`);
  console.log(`  DB_SYNC_INTERVAL: ${process.env.DB_SYNC_INTERVAL || 'not set'}`);
  console.log(`  DB_SYNC_DIRECTION: ${process.env.DB_SYNC_DIRECTION || 'not set'}`);
  console.log(`  PG_HOST: ${process.env.PG_HOST || 'not set'}`);
  console.log(`  PG_USER: ${process.env.PG_USER || 'not set'}`);
  console.log(`  PG_DATABASE: ${process.env.PG_DATABASE || 'not set'}`);
  console.log(`  PG_PORT: ${process.env.PG_PORT || 'not set'}`);
  
  console.log('\n📋 SYNC TABLES:');
  console.log('  - users');
  console.log('  - sync_tasks');
  console.log('  - sync_history');
  console.log('  - desktop_clients');
  console.log('  - sessions');
  
  console.log('\n🎯 SYNC DIRECTIONS:');
  console.log('  - sqlite-to-pg: SQLite → PostgreSQL only');
  console.log('  - pg-to-sqlite: PostgreSQL → SQLite only');
  console.log('  - bidirectional: Both directions (recommended)');
}

// Run management tool
manageDatabaseSync();
