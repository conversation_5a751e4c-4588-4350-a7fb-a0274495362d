import React from 'react';
import { useTheme } from '../../../contexts/ThemeContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useSettings } from '../../../contexts/SettingsContext';
import { useNotification } from '../../../contexts/NotificationContext';
import Toggle from '../../UI/Toggle';
import {
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
  RocketLaunchIcon
} from '@heroicons/react/24/outline';

const AppearanceSettingsPanel = () => {
  const { theme, setTheme, actualTheme } = useTheme();
  const { t } = useLanguage();
  const { settings, updateSetting, isElectron } = useSettings();
  const { addNotification } = useNotification();

  const handleThemeChange = async (newTheme) => {
    try {
      await setTheme(newTheme);
      updateSetting('theme', newTheme);
      addNotification(t('themeChanged'), 'success');
    } catch (error) {
      console.error('Failed to change theme:', error);
      addNotification(t('failedToChangeTheme'), 'error');
    }
  };

  const handleStartupSettingChange = (key, value) => {
    updateSetting(key, value);
  };

  const handleTestHideToTray = async () => {
    if (!isElectron || !window.electronAPI?.tray?.hideToTray) {
      addNotification(t('electronOnlyFeature'), 'warning');
      return;
    }

    try {
      const success = await window.electronAPI.tray.hideToTray();
      if (success) {
        addNotification(t('hiddenToTray'), 'info');
      }
    } catch (error) {
      console.debug('Tray functionality not available:', error.message);
      addNotification(t('errorHidingToTray'), 'error');
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t('appearanceSettings')}</h2>
        <p className="text-gray-600 dark:text-gray-300 mt-1">{t('appearanceSettingsDescription')}</p>
      </div>

      {/* Theme Settings */}
      <div className="space-y-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white">{t('themeSettings')}</h3>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            {t('colorTheme')}
          </label>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">{t('themeDescription')}</p>

          <div className="grid grid-cols-3 gap-3">
            <button
              onClick={() => handleThemeChange('light')}
              className={`p-4 border-2 rounded-lg transition-all ${
                theme === 'light'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
            >
              <div className="flex flex-col items-center space-y-2">
                <SunIcon className="w-6 h-6 text-yellow-500" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">{t('lightTheme')}</span>
              </div>
            </button>

            <button
              onClick={() => handleThemeChange('dark')}
              className={`p-4 border-2 rounded-lg transition-all ${
                theme === 'dark'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
            >
              <div className="flex flex-col items-center space-y-2">
                <MoonIcon className="w-6 h-6 text-blue-500" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">{t('darkTheme')}</span>
              </div>
            </button>

            <button
              onClick={() => handleThemeChange('system')}
              className={`p-4 border-2 rounded-lg transition-all ${
                theme === 'system'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
            >
              <div className="flex flex-col items-center space-y-2">
                <ComputerDesktopIcon className="w-6 h-6 text-gray-500" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">{t('systemTheme')}</span>
              </div>
            </button>
          </div>

          {theme === 'system' && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              {t('currentlyUsing')}: {actualTheme === 'dark' ? t('darkTheme') : t('lightTheme')}
            </p>
          )}
        </div>
      </div>

      {/* Startup & Behavior Settings (Electron only) */}
      {isElectron && (
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <RocketLaunchIcon className="w-5 h-5 text-blue-500" />
            <h3 className="text-md font-medium text-gray-900 dark:text-white">{t('startupBehavior')}</h3>
          </div>
          
          <div className="space-y-4">
            <Toggle
              label={t('startupWithWindows')}
              description={t('startupWithWindowsDescription')}
              checked={settings.startupWithWindows}
              onChange={(checked) => handleStartupSettingChange('startupWithWindows', checked)}
            />

            <Toggle
              label={t('startMinimized')}
              description={t('startMinimizedDescription')}
              checked={settings.startMinimized}
              onChange={(checked) => handleStartupSettingChange('startMinimized', checked)}
            />

            <Toggle
              label={t('minimizeToTray')}
              description={t('minimizeToTrayDescription')}
              checked={settings.minimizeToTray}
              onChange={(checked) => handleStartupSettingChange('minimizeToTray', checked)}
            />

            <Toggle
              label={t('closeToTray')}
              description={t('closeToTrayDescription')}
              checked={settings.closeToTray}
              onChange={(checked) => handleStartupSettingChange('closeToTray', checked)}
            />

            {/* Test Hide to Tray */}
            <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">{t('testHideToTray')}</h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{t('testHideToTrayDescription')}</p>
                </div>
                <button
                  onClick={handleTestHideToTray}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md"
                >
                  {t('hideToTray')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Web-only notice */}
      {!isElectron && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-blue-400 text-xl">ℹ️</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                {t('desktopFeaturesAvailable')}
              </h3>
              <p className="mt-1 text-sm text-blue-700 dark:text-blue-300">
                {t('desktopFeaturesDescription')}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AppearanceSettingsPanel;
