#!/usr/bin/env node

/**
 * Enable auto sync with advanced features
 */

const axios = require('axios');

async function enableAutoSync() {
  try {
    console.log('🔐 Logging in...');
    
    // Login
    const loginResponse = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Login successful');
    
    // Enable auto sync with advanced features
    console.log('⚙️ Enabling auto sync with advanced features...');
    
    const configResponse = await axios.put('http://localhost:5001/api/database-sync/config', {
      enableAutoSync: true,
      syncIntervalMinutes: 2, // Sync every 2 minutes for testing
      consistencyCheckInterval: 3, // Check consistency every 3 minutes
      enableSmartSync: true,
      enableConsistencyCheck: true,
      enableDataComparison: true,
      enableDeletionSync: true,
      syncDirection: 'bidirectional'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Auto sync configuration updated');
    console.log('📊 New Configuration:');
    console.log('   - Auto Sync: ✅ ENABLED');
    console.log('   - Sync Interval: 2 minutes');
    console.log('   - Consistency Check: Every 3 minutes');
    console.log('   - Smart Sync: ✅ ENABLED');
    console.log('   - Data Comparison: ✅ ENABLED');
    console.log('   - Deletion Sync: ✅ ENABLED');
    
    // Check status
    console.log('\n📊 Checking current status...');
    
    const statusResponse = await axios.get('http://localhost:5001/api/database-sync/status', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const status = statusResponse.data;
    console.log('✅ Status retrieved:');
    console.log(`   Auto Sync Running: ${status.isRunning ? '✅' : '❌'}`);
    console.log(`   Last Sync: ${status.lastSyncTime || 'Never'}`);
    console.log(`   Sync Direction: ${status.config.syncDirection}`);
    console.log(`   Sync Interval: ${status.config.syncIntervalMinutes} minutes`);
    
    console.log('\n🎯 Auto Sync is now running!');
    console.log('💡 The system will:');
    console.log('   - Sync databases every 2 minutes');
    console.log('   - Check consistency every 3 minutes');
    console.log('   - Skip sync if data is already identical');
    console.log('   - Auto-fix inconsistencies when detected');
    console.log('   - Monitor both SQLite and PostgreSQL');
    
    console.log('\n📝 Monitor the server logs to see:');
    console.log('   - Automatic sync operations');
    console.log('   - Consistency check results');
    console.log('   - Smart sync decisions');
    console.log('   - Data comparison results');
    
  } catch (error) {
    console.error('❌ Error enabling auto sync:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

enableAutoSync();
