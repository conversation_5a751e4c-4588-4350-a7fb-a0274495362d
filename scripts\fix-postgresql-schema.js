#!/usr/bin/env node

/**
 * Fix PostgreSQL schema to match SQLite
 */

const { Pool } = require('pg');

console.log('🔧 Fixing PostgreSQL Schema');
console.log('===========================');

async function fixPostgreSQLSchema() {
  console.log('\n1. 🐘 Connecting to PostgreSQL...');
  
  const pool = new Pool({
    host: '*************',
    port: 5432,
    database: 'syncmasterpro',
    user: 'pi',
    password: 'ubuntu'
  });

  try {
    const client = await pool.connect();
    console.log('✅ Connected to PostgreSQL');
    
    // Check existing tables
    console.log('\n2. 🔍 Checking existing tables...');
    const tables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log('📋 Existing tables:');
    tables.rows.forEach(table => {
      console.log(`   - ${table.table_name}`);
    });
    
    // Check if clients table exists
    const clientsTableExists = tables.rows.some(table => table.table_name === 'clients');
    
    if (!clientsTableExists) {
      console.log('\n3. 🔧 Creating clients table...');
      
      await client.query(`
        CREATE TABLE IF NOT EXISTS clients (
          id SERIAL PRIMARY KEY,
          client_id VARCHAR(255) UNIQUE NOT NULL,
          user_id INTEGER NOT NULL,
          hostname VARCHAR(255) NOT NULL,
          platform VARCHAR(100) NOT NULL,
          version VARCHAR(50),
          status VARCHAR(50) DEFAULT 'offline',
          last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          total_tasks INTEGER DEFAULT 0,
          active_tasks INTEGER DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      console.log('✅ Clients table created');
    } else {
      console.log('\n3. ✅ Clients table already exists');
    }
    
    // Update tables list
    const updatedTables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log('\n4. 📋 Updated tables:');
    updatedTables.rows.forEach(table => {
      console.log(`   - ${table.table_name}`);
    });
    
    // Check table counts
    console.log('\n5. 📊 Table record counts:');
    for (const table of updatedTables.rows) {
      try {
        const count = await client.query(`SELECT COUNT(*) as count FROM ${table.table_name}`);
        console.log(`   - ${table.table_name}: ${count.rows[0].count} records`);
      } catch (error) {
        console.log(`   - ${table.table_name}: Error reading (${error.message})`);
      }
    }
    
    client.release();
    await pool.end();
    return true;
    
  } catch (error) {
    console.error('❌ PostgreSQL error:', error.message);
    await pool.end();
    throw error;
  }
}

async function main() {
  try {
    console.log('🚀 Starting PostgreSQL schema fix...\n');
    
    const success = await fixPostgreSQLSchema();
    
    if (success) {
      console.log('\n🎉 SUCCESS! PostgreSQL schema updated!');
      console.log('📋 Changes made:');
      console.log('   ✅ Clients table created (if missing)');
      console.log('   ✅ Schema now matches SQLite');
      console.log('');
      console.log('💡 Next steps:');
      console.log('   1. Run database sync to sync data');
      console.log('   2. Both databases will have consistent schema');
      console.log('   3. Web manager will show consistent data');
    } else {
      console.log('\n❌ Schema fix failed');
    }
    
  } catch (error) {
    console.error('\n❌ Fix failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
