const { Client } = require('pg');
const Database = require('better-sqlite3');
const path = require('path');

async function cleanDuplicateTokens() {
  console.log('🧹 CLEANING DUPLICATE TOKENS FROM DATABASES\n');

  // Clean PostgreSQL (Web Server)
  await cleanPostgreSQLTokens();
  
  // Clean SQLite (Desktop App)
  await cleanSQLiteTokens();
}

async function cleanPostgreSQLTokens() {
  console.log('🐘 CLEANING POSTGRESQL (WEB SERVER) TOKENS');
  console.log('=' .repeat(50));

  const client = new Client({
    host: '*************',
    user: 'pi',
    password: 'ubuntu',
    database: 'syncmasterpro',
    port: 5432,
  });

  try {
    await client.connect();
    console.log('✅ Connected to PostgreSQL');

    // Check current token count
    const totalResult = await client.query('SELECT COUNT(*) as count FROM sessions');
    console.log(`📊 Total sessions before cleanup: ${totalResult.rows[0].count}`);

    // Find duplicate tokens
    const duplicatesResult = await client.query(`
      SELECT token, COUNT(*) as count, MIN(created_at) as first_created, MAX(created_at) as last_created
      FROM sessions 
      GROUP BY token 
      HAVING COUNT(*) > 1
      ORDER BY COUNT(*) DESC
    `);

    if (duplicatesResult.rows.length === 0) {
      console.log('✅ No duplicate tokens found in PostgreSQL');
    } else {
      console.log(`⚠️ Found ${duplicatesResult.rows.length} duplicate tokens:`);
      
      for (const dup of duplicatesResult.rows) {
        console.log(`   🔑 Token: ${dup.token.substring(0, 20)}... (${dup.count} duplicates)`);
        console.log(`      📅 First: ${dup.first_created}`);
        console.log(`      📅 Last: ${dup.last_created}`);
        
        // Keep only the latest session for each duplicate token
        const deleteResult = await client.query(`
          DELETE FROM sessions 
          WHERE token = $1 AND id NOT IN (
            SELECT id FROM sessions WHERE token = $1 ORDER BY created_at DESC LIMIT 1
          )
        `, [dup.token]);
        
        console.log(`   🗑️ Deleted ${deleteResult.rowCount} duplicate sessions`);
      }
    }

    // Clean up expired sessions
    const expiredResult = await client.query(`
      DELETE FROM sessions 
      WHERE expires_at < NOW()
    `);
    console.log(`🗑️ Deleted ${expiredResult.rowCount} expired sessions`);

    // Check final count
    const finalResult = await client.query('SELECT COUNT(*) as count FROM sessions');
    console.log(`📊 Total sessions after cleanup: ${finalResult.rows[0].count}`);

    // Show sessions by user
    const userSessionsResult = await client.query(`
      SELECT u.email, COUNT(s.id) as session_count
      FROM users u
      LEFT JOIN sessions s ON u.id = s.user_id
      GROUP BY u.id, u.email
      ORDER BY session_count DESC
    `);

    console.log('\n👥 Sessions by user:');
    for (const user of userSessionsResult.rows) {
      console.log(`   📧 ${user.email}: ${user.session_count} sessions`);
    }

  } catch (error) {
    console.error('❌ PostgreSQL cleanup failed:', error.message);
  } finally {
    await client.end();
    console.log('🔌 PostgreSQL connection closed\n');
  }
}

async function cleanSQLiteTokens() {
  console.log('📱 CLEANING SQLITE (DESKTOP APP) TOKENS');
  console.log('=' .repeat(50));

  const dbPath = path.join(process.cwd(), 'database.sqlite');
  
  try {
    const db = new Database(dbPath);
    console.log('✅ Connected to SQLite');

    // Check current token count
    const totalResult = db.prepare('SELECT COUNT(*) as count FROM sessions').get();
    console.log(`📊 Total sessions before cleanup: ${totalResult.count}`);

    // Find duplicate tokens
    const duplicatesResult = db.prepare(`
      SELECT token, COUNT(*) as count, MIN(created_at) as first_created, MAX(created_at) as last_created
      FROM sessions 
      GROUP BY token 
      HAVING COUNT(*) > 1
      ORDER BY COUNT(*) DESC
    `).all();

    if (duplicatesResult.length === 0) {
      console.log('✅ No duplicate tokens found in SQLite');
    } else {
      console.log(`⚠️ Found ${duplicatesResult.length} duplicate tokens:`);
      
      const deleteDuplicates = db.prepare(`
        DELETE FROM sessions 
        WHERE token = ? AND id NOT IN (
          SELECT id FROM sessions WHERE token = ? ORDER BY created_at DESC LIMIT 1
        )
      `);

      for (const dup of duplicatesResult) {
        console.log(`   🔑 Token: ${dup.token.substring(0, 20)}... (${dup.count} duplicates)`);
        console.log(`      📅 First: ${dup.first_created}`);
        console.log(`      📅 Last: ${dup.last_created}`);
        
        // Keep only the latest session for each duplicate token
        const result = deleteDuplicates.run(dup.token, dup.token);
        console.log(`   🗑️ Deleted ${result.changes} duplicate sessions`);
      }
    }

    // Clean up expired sessions
    const expiredResult = db.prepare(`
      DELETE FROM sessions 
      WHERE expires_at < datetime('now')
    `).run();
    console.log(`🗑️ Deleted ${expiredResult.changes} expired sessions`);

    // Check final count
    const finalResult = db.prepare('SELECT COUNT(*) as count FROM sessions').get();
    console.log(`📊 Total sessions after cleanup: ${finalResult.count}`);

    // Show sessions by user
    const userSessionsResult = db.prepare(`
      SELECT u.email, COUNT(s.id) as session_count
      FROM users u
      LEFT JOIN sessions s ON u.id = s.user_id
      GROUP BY u.id, u.email
      ORDER BY session_count DESC
    `).all();

    console.log('\n👥 Sessions by user:');
    for (const user of userSessionsResult) {
      console.log(`   📧 ${user.email}: ${user.session_count} sessions`);
    }

    db.close();
    console.log('🔌 SQLite connection closed\n');

  } catch (error) {
    console.error('❌ SQLite cleanup failed:', error.message);
  }
}

async function analyzeTokenUsage() {
  console.log('📊 TOKEN USAGE ANALYSIS');
  console.log('=' .repeat(50));

  console.log('✅ EXPECTED BEHAVIOR AFTER FIX:');
  console.log('   🔄 Desktop app: Reuses existing valid tokens');
  console.log('   🌐 Web app: Reuses existing valid tokens');
  console.log('   🆕 New token: Only created when no valid session exists');
  console.log('   🗑️ Cleanup: Expired sessions automatically removed');
  console.log('   📊 Limit: Maximum 10 sessions per user');
  console.log('');
  console.log('✅ TOKEN REUSE LOGIC:');
  console.log('   1. User logs in');
  console.log('   2. Check for existing valid session');
  console.log('   3. If found: Reuse existing token');
  console.log('   4. If not found: Create new token');
  console.log('   5. Clean up old sessions if over limit');
  console.log('');
  console.log('✅ BENEFITS:');
  console.log('   - No more duplicate tokens');
  console.log('   - Consistent behavior between desktop and web');
  console.log('   - Better database performance');
  console.log('   - Cleaner session management');
}

// Run cleanup
cleanDuplicateTokens()
  .then(() => {
    analyzeTokenUsage();
    console.log('\n🎉 TOKEN CLEANUP COMPLETED!');
    console.log('💡 Both desktop and web apps now reuse tokens properly');
  })
  .catch(error => {
    console.error('❌ Cleanup failed:', error);
  });
