const fs = require('fs').promises;
const path = require('path');
const { getDatabase } = require('../database/init');

class ConflictResolver {
  constructor(fileVersioning) {
    this.fileVersioning = fileVersioning;
    this.conflictStrategies = {
      'ask_user': this.askUserStrategy.bind(this),
      'keep_newer': this.keepNewerStrategy.bind(this),
      'keep_larger': this.keepLargerStrategy.bind(this),
      'keep_source': this.keepSourceStrategy.bind(this),
      'keep_destination': this.keepDestinationStrategy.bind(this),
      'merge_both': this.mergeBothStrategy.bind(this),
      'skip': this.skipStrategy.bind(this)
    };
  }

  // Main conflict resolution method
  async resolveConflict(task, sourceFile, destFile, strategy = 'ask_user') {
    try {
      console.log(`🔄 Resolving conflict for ${sourceFile.relativePath}`);
      
      // Create versions of both files before resolution
      if (this.fileVersioning) {
        await this.fileVersioning.createVersion(
          task.id, 
          sourceFile.fullPath, 
          sourceFile.relativePath, 
          'conflict_source'
        );
        
        await this.fileVersioning.createVersion(
          task.id, 
          destFile.fullPath, 
          destFile.relativePath, 
          'conflict_destination'
        );
      }

      // Log conflict
      await this.logConflict(task.id, sourceFile, destFile, strategy);

      // Apply resolution strategy
      const resolver = this.conflictStrategies[strategy] || this.conflictStrategies['ask_user'];
      const result = await resolver(task, sourceFile, destFile);

      console.log(`✅ Conflict resolved for ${sourceFile.relativePath}: ${result.action}`);
      return result;

    } catch (error) {
      console.error('Failed to resolve conflict:', error);
      return {
        action: 'skip',
        reason: `Error: ${error.message}`,
        success: false
      };
    }
  }

  // Strategy: Ask user (store for manual resolution)
  async askUserStrategy(task, sourceFile, destFile) {
    // Store conflict for user to resolve later
    await this.storeConflictForUser(task.id, sourceFile, destFile);
    
    return {
      action: 'ask_user',
      reason: 'Conflict stored for user resolution',
      success: true,
      requiresUserInput: true
    };
  }

  // Strategy: Keep newer file
  async keepNewerStrategy(task, sourceFile, destFile) {
    const sourceTime = new Date(sourceFile.mtime);
    const destTime = new Date(destFile.mtime);

    if (sourceTime > destTime) {
      await this.copyFile(sourceFile.fullPath, destFile.fullPath);
      return {
        action: 'keep_source',
        reason: `Source file is newer (${sourceTime.toISOString()})`,
        success: true
      };
    } else {
      return {
        action: 'keep_destination',
        reason: `Destination file is newer (${destTime.toISOString()})`,
        success: true
      };
    }
  }

  // Strategy: Keep larger file
  async keepLargerStrategy(task, sourceFile, destFile) {
    if (sourceFile.size > destFile.size) {
      await this.copyFile(sourceFile.fullPath, destFile.fullPath);
      return {
        action: 'keep_source',
        reason: `Source file is larger (${sourceFile.size} bytes)`,
        success: true
      };
    } else {
      return {
        action: 'keep_destination',
        reason: `Destination file is larger (${destFile.size} bytes)`,
        success: true
      };
    }
  }

  // Strategy: Always keep source
  async keepSourceStrategy(task, sourceFile, destFile) {
    await this.copyFile(sourceFile.fullPath, destFile.fullPath);
    return {
      action: 'keep_source',
      reason: 'Always keep source file',
      success: true
    };
  }

  // Strategy: Always keep destination
  async keepDestinationStrategy(task, sourceFile, destFile) {
    return {
      action: 'keep_destination',
      reason: 'Always keep destination file',
      success: true
    };
  }

  // Strategy: Keep both files with different names
  async mergeBothStrategy(task, sourceFile, destFile) {
    try {
      const ext = path.extname(destFile.fullPath);
      const baseName = path.basename(destFile.fullPath, ext);
      const dir = path.dirname(destFile.fullPath);
      
      // Create unique names for both files
      const timestamp = Date.now();
      const sourceConflictPath = path.join(dir, `${baseName}_source_${timestamp}${ext}`);
      const destConflictPath = path.join(dir, `${baseName}_dest_${timestamp}${ext}`);

      // Copy source file with conflict name
      await this.copyFile(sourceFile.fullPath, sourceConflictPath);
      
      // Rename destination file with conflict name
      await fs.rename(destFile.fullPath, destConflictPath);

      return {
        action: 'merge_both',
        reason: 'Both files kept with conflict names',
        success: true,
        sourceConflictPath,
        destConflictPath
      };

    } catch (error) {
      console.error('Failed to merge both files:', error);
      return {
        action: 'skip',
        reason: `Failed to merge: ${error.message}`,
        success: false
      };
    }
  }

  // Strategy: Skip conflicted file
  async skipStrategy(task, sourceFile, destFile) {
    return {
      action: 'skip',
      reason: 'Conflict skipped by strategy',
      success: true
    };
  }

  // Store conflict for user resolution
  async storeConflictForUser(taskId, sourceFile, destFile) {
    try {
      const db = getDatabase();
      
      await db.query(
        `INSERT INTO file_conflicts 
         (task_id, file_path, source_path, dest_path, source_size, dest_size, 
          source_mtime, dest_mtime, status, created_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', datetime('now'))`,
        [
          taskId,
          sourceFile.relativePath,
          sourceFile.fullPath,
          destFile.fullPath,
          sourceFile.size,
          destFile.size,
          sourceFile.mtime,
          destFile.mtime
        ]
      );

      console.log(`📝 Stored conflict for user resolution: ${sourceFile.relativePath}`);

    } catch (error) {
      console.error('Failed to store conflict:', error);
    }
  }

  // Log conflict resolution
  async logConflict(taskId, sourceFile, destFile, strategy) {
    try {
      const db = getDatabase();
      
      await db.query(
        `INSERT INTO conflict_log 
         (task_id, file_path, strategy, source_size, dest_size, 
          source_mtime, dest_mtime, created_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))`,
        [
          taskId,
          sourceFile.relativePath,
          strategy,
          sourceFile.size,
          destFile.size,
          sourceFile.mtime,
          destFile.mtime
        ]
      );

    } catch (error) {
      console.error('Failed to log conflict:', error);
    }
  }

  // Get pending conflicts for a task
  async getPendingConflicts(taskId) {
    try {
      const db = getDatabase();
      const result = await db.query(
        `SELECT * FROM file_conflicts 
         WHERE task_id = ? AND status = 'pending' 
         ORDER BY created_at DESC`,
        [taskId]
      );

      return result.rows.map(conflict => ({
        id: conflict.id,
        taskId: conflict.task_id,
        filePath: conflict.file_path,
        sourcePath: conflict.source_path,
        destPath: conflict.dest_path,
        sourceSize: conflict.source_size,
        destSize: conflict.dest_size,
        sourceMtime: conflict.source_mtime,
        destMtime: conflict.dest_mtime,
        status: conflict.status,
        createdAt: conflict.created_at
      }));

    } catch (error) {
      console.error('Failed to get pending conflicts:', error);
      return [];
    }
  }

  // Resolve user conflict
  async resolveUserConflict(conflictId, action, userChoice = null) {
    try {
      const db = getDatabase();
      
      // Get conflict details
      const result = await db.query(
        'SELECT * FROM file_conflicts WHERE id = ?',
        [conflictId]
      );

      if (result.rows.length === 0) {
        throw new Error('Conflict not found');
      }

      const conflict = result.rows[0];

      // Apply user choice
      let success = false;
      let reason = '';

      switch (action) {
        case 'keep_source':
          await this.copyFile(conflict.source_path, conflict.dest_path);
          success = true;
          reason = 'User chose to keep source file';
          break;
          
        case 'keep_destination':
          success = true;
          reason = 'User chose to keep destination file';
          break;
          
        case 'merge_both':
          const mergeResult = await this.mergeBothStrategy(
            { id: conflict.task_id },
            { fullPath: conflict.source_path, relativePath: conflict.file_path },
            { fullPath: conflict.dest_path }
          );
          success = mergeResult.success;
          reason = mergeResult.reason;
          break;
          
        default:
          reason = 'User skipped conflict';
          success = true;
      }

      // Update conflict status
      await db.query(
        `UPDATE file_conflicts 
         SET status = 'resolved', resolution = ?, resolved_at = datetime('now')
         WHERE id = ?`,
        [action, conflictId]
      );

      console.log(`✅ User conflict resolved: ${conflict.file_path} - ${action}`);
      return { success, reason };

    } catch (error) {
      console.error('Failed to resolve user conflict:', error);
      return { success: false, reason: error.message };
    }
  }

  // Helper method to copy file
  async copyFile(sourcePath, destPath) {
    try {
      const destDir = path.dirname(destPath);
      await fs.mkdir(destDir, { recursive: true });
      await fs.copyFile(sourcePath, destPath);
      return true;
    } catch (error) {
      console.error('Failed to copy file:', error);
      return false;
    }
  }

  // Get conflict statistics
  async getConflictStats(taskId) {
    try {
      const db = getDatabase();
      const result = await db.query(
        `SELECT 
           COUNT(*) as total_conflicts,
           SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_conflicts,
           SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved_conflicts
         FROM file_conflicts 
         WHERE task_id = ?`,
        [taskId]
      );

      return result.rows[0] || {
        total_conflicts: 0,
        pending_conflicts: 0,
        resolved_conflicts: 0
      };
    } catch (error) {
      console.error('Failed to get conflict stats:', error);
      return {
        total_conflicts: 0,
        pending_conflicts: 0,
        resolved_conflicts: 0
      };
    }
  }
}

module.exports = ConflictResolver;
