// Test script for task persistence across app restarts
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const baseURL = 'http://localhost:5002/api';

async function testTaskPersistence() {
  console.log('🔄 Testing Task Persistence Across Restarts\n');
  
  try {
    // 1. Login
    console.log('1. 🔐 Logging in...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    const headers = { 'Authorization': `Bearer ${token}` };
    console.log('✅ Login successful');
    
    // 2. Create test directories
    console.log('\n2. 📁 Setting up test directories...');
    const testDir = path.join(process.cwd(), 'test-persistence');
    const sourceDir = path.join(testDir, 'source');
    const destDir = path.join(testDir, 'dest');
    
    [testDir, sourceDir, destDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
    
    console.log(`📂 Source: ${sourceDir}`);
    console.log(`📂 Destination: ${destDir}`);
    
    // 3. Create real-time task
    console.log('\n3. ➕ Creating real-time sync task...');
    
    const taskData = {
      name: 'Persistence Test Task',
      sourcePath: sourceDir,
      destinationPath: destDir,
      syncType: 'source-to-destination',
      options: {
        deleteExtraFiles: false,
        preserveTimestamps: true,
        enableRealtime: true
      }
    };
    
    const createResponse = await axios.post(`${baseURL}/sync/tasks`, taskData, { headers });
    const taskId = createResponse.data.task.id;
    console.log(`✅ Created task with ID: ${taskId}`);
    
    // 4. Start real-time sync
    console.log('\n4. ⚡ Starting real-time sync...');
    
    const startResponse = await axios.post(
      `${baseURL}/sync/tasks/${taskId}/realtime/start`, 
      {}, 
      { headers }
    );
    
    console.log('✅ Real-time sync started:', startResponse.data);
    
    // 5. Verify task status
    console.log('\n5. 📊 Checking task status...');
    
    const tasksResponse = await axios.get(`${baseURL}/sync/tasks`, { headers });
    const tasks = tasksResponse.data.tasks || [];
    const ourTask = tasks.find(t => t.id === taskId);
    
    if (ourTask) {
      console.log(`📊 Task status: ${ourTask.status}`);
      console.log(`📊 Real-time enabled: ${ourTask.options?.enableRealtime}`);
      
      if (ourTask.status === 'monitoring') {
        console.log('✅ Real-time sync is active and monitoring');
      } else {
        console.log('❌ Real-time sync is not in monitoring state');
      }
    }
    
    // 6. Test file sync
    console.log('\n6. 📄 Testing file sync...');
    
    const testFile = path.join(sourceDir, 'persistence-test.txt');
    fs.writeFileSync(testFile, `Persistence test file\nCreated: ${new Date().toISOString()}`);
    
    console.log('✅ Test file created');
    console.log('⏳ Waiting 5 seconds for sync...');
    
    await delay(5000);
    
    const destFile = path.join(destDir, 'persistence-test.txt');
    const fileSynced = fs.existsSync(destFile);
    
    console.log(`📊 File synced: ${fileSynced ? '✅ YES' : '❌ NO'}`);
    
    // 7. Instructions for manual restart test
    console.log('\n7. 🔄 MANUAL RESTART TEST INSTRUCTIONS:');
    console.log('   📝 Follow these steps to test persistence:');
    console.log('   ');
    console.log('   1. ⚠️  DO NOT stop this script yet');
    console.log('   2. 🛑 Stop the SyncMasterPro server (Ctrl+C in server terminal)');
    console.log('   3. ⏳ Wait 3 seconds');
    console.log('   4. 🚀 Restart the SyncMasterPro server');
    console.log('   5. ⏳ Wait for server to fully start (look for "Task auto-resume completed")');
    console.log('   6. ✅ Press Enter here to continue testing...');
    console.log('   ');
    
    // Wait for user input
    await waitForEnter();
    
    // 8. Check if task was resumed
    console.log('\n8. 🔍 Checking if task was auto-resumed...');
    
    try {
      const resumedTasksResponse = await axios.get(`${baseURL}/sync/tasks`, { headers });
      const resumedTasks = resumedTasksResponse.data.tasks || [];
      const resumedTask = resumedTasks.find(t => t.id === taskId);
      
      if (resumedTask) {
        console.log(`📊 Task found after restart`);
        console.log(`📊 Status: ${resumedTask.status}`);
        console.log(`📊 Real-time enabled: ${resumedTask.options?.enableRealtime}`);
        
        if (resumedTask.status === 'monitoring') {
          console.log('✅ SUCCESS: Real-time sync was auto-resumed!');
        } else if (resumedTask.status === 'idle') {
          console.log('⚠️ Task exists but not monitoring (expected for regular tasks)');
        } else {
          console.log(`❌ Unexpected status: ${resumedTask.status}`);
        }
      } else {
        console.log('❌ Task not found after restart');
      }
      
    } catch (error) {
      console.log('❌ Failed to check resumed tasks:', error.message);
      console.log('💡 Server might still be starting up, try again in a few seconds');
    }
    
    // 9. Test sync after restart
    console.log('\n9. 📄 Testing sync after restart...');
    
    const testFile2 = path.join(sourceDir, 'post-restart-test.txt');
    fs.writeFileSync(testFile2, `Post-restart test file\nCreated: ${new Date().toISOString()}`);
    
    console.log('✅ Post-restart test file created');
    console.log('⏳ Waiting 8 seconds for sync...');
    
    await delay(8000);
    
    const destFile2 = path.join(destDir, 'post-restart-test.txt');
    const file2Synced = fs.existsSync(destFile2);
    
    console.log(`📊 Post-restart file synced: ${file2Synced ? '✅ YES' : '❌ NO'}`);
    
    // 10. Summary
    console.log('\n📊 PERSISTENCE TEST SUMMARY:');
    console.log(`   Task created: ✅ YES`);
    console.log(`   Real-time started: ✅ YES`);
    console.log(`   Pre-restart sync: ${fileSynced ? '✅ YES' : '❌ NO'}`);
    console.log(`   Post-restart sync: ${file2Synced ? '✅ YES' : '❌ NO'}`);
    
    const persistenceWorking = fileSynced && file2Synced;
    
    console.log(`\n🎯 PERSISTENCE STATUS: ${persistenceWorking ? '✅ WORKING' : '❌ NEEDS FIX'}`);
    
    if (persistenceWorking) {
      console.log('🎉 Task persistence is working correctly!');
      console.log('💡 Real-time sync survives app restarts');
    } else {
      console.log('🔧 Task persistence needs improvement');
      console.log('💡 Check server logs for auto-resume messages');
    }
    
    // Cleanup
    console.log('\n🧹 Cleaning up...');
    try {
      await axios.delete(`${baseURL}/sync/tasks/${taskId}`, { headers });
      console.log('✅ Test task deleted');
    } catch (error) {
      console.log('⚠️ Failed to delete test task:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function waitForEnter() {
  return new Promise(resolve => {
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.on('data', () => {
      process.stdin.setRawMode(false);
      process.stdin.pause();
      resolve();
    });
  });
}

// Export for manual use
module.exports = { testTaskPersistence };

// Auto-run if called directly
if (require.main === module) {
  testTaskPersistence();
}

console.log('🔄 Task Persistence Tester loaded!');
console.log('📝 Run: node scripts/test-task-persistence.js');
