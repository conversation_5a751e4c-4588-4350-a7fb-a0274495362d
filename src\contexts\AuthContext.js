import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [token, setToken] = useState(null);

  // API base URL - Auto-detect environment
  // Check multiple ways to detect Electron environment
  const isElectron = window.electronAPI !== undefined ||
                     window.platform !== undefined ||
                     navigator.userAgent.toLowerCase().indexOf('electron') > -1 ||
                     (window.process && window.process.versions && window.process.versions.electron);

  const API_BASE_URL = isElectron
    ? 'http://localhost:5002/api'  // Desktop uses port 5002
    : (process.env.REACT_APP_API_URL || 'http://localhost:5000/api'); // Web uses port 5000

  useEffect(() => {
    // Check for stored auth data on app start
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      // Check electron store for auth data
      if (window.electronAPI) {
        const storedToken = await window.electronAPI.store.get('authToken');
        const storedUser = await window.electronAPI.store.get('user');
        
        if (storedToken && storedUser) {
          setToken(storedToken);
          setUser(storedUser);
          
          // Verify token with server
          try {
            const response = await axios.get(`${API_BASE_URL}/auth/verify`, {
              headers: { Authorization: `Bearer ${storedToken}` }
            });
            
            if (response.data.valid) {
              setUser(response.data.user);
            } else {
              // Token invalid, clear auth data
              await logout();
            }
          } catch (error) {
            console.error('Token verification failed:', error);
            // If server is offline, use cached user data
            if (error.code === 'ECONNREFUSED') {
              console.log('Server offline, using cached auth data');
            } else {
              await logout();
            }
          }
        }
      } else {
        // Web version - check localStorage
        const storedToken = localStorage.getItem('authToken');
        const storedUser = localStorage.getItem('user');
        
        if (storedToken && storedUser) {
          setToken(storedToken);
          setUser(JSON.parse(storedUser));
        }
      }
    } catch (error) {
      console.error('Auth check failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password, rememberMe = false) => {
    console.log('🔍 AuthContext: Login attempt for', email);
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/login`, {
        email,
        password
      });

      const { token: newToken, user: userData, tokenReused, sessionInfo } = response.data;
      console.log('✅ AuthContext: Login successful, token received');
      console.log('🔑 Token preview:', newToken.substring(0, 20) + '...');
      console.log(`🔄 Token reused: ${tokenReused ? 'Yes' : 'No'}`);
      if (sessionInfo) {
        console.log(`📊 Session info: ${sessionInfo.totalSessions} total sessions, expires: ${sessionInfo.expiresAt}`);
      }

      setToken(newToken);
      setUser(userData);

      // Store auth data
      if (window.electronAPI) {
        await window.electronAPI.store.set('authToken', newToken);
        await window.electronAPI.store.set('user', userData);
        if (rememberMe) {
          await window.electronAPI.store.set('rememberMe', true);
        }
      } else {
        localStorage.setItem('authToken', newToken);
        localStorage.setItem('user', JSON.stringify(userData));
        if (rememberMe) {
          localStorage.setItem('rememberMe', 'true');
        }
      }

      return { success: true, user: userData };
    } catch (error) {
      console.error('Login failed:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Login failed'
      };
    }
  };

  const register = async (userData) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/register`, userData);
      
      const { token: newToken, user: newUser } = response.data;
      
      setToken(newToken);
      setUser(newUser);

      // Store auth data
      if (window.electronAPI) {
        await window.electronAPI.store.set('authToken', newToken);
        await window.electronAPI.store.set('user', newUser);
      } else {
        localStorage.setItem('authToken', newToken);
        localStorage.setItem('user', JSON.stringify(newUser));
      }

      return { success: true, user: newUser };
    } catch (error) {
      console.error('Registration failed:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Registration failed'
      };
    }
  };

  const logout = async () => {
    try {
      if (token) {
        await axios.post(`${API_BASE_URL}/auth/logout`, {}, {
          headers: { Authorization: `Bearer ${token}` }
        });
      }
    } catch (error) {
      console.error('Logout request failed:', error);
    }

    // Clear auth data
    setToken(null);
    setUser(null);

    if (window.electronAPI) {
      await window.electronAPI.store.delete('authToken');
      await window.electronAPI.store.delete('user');
      await window.electronAPI.store.delete('rememberMe');
    } else {
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      localStorage.removeItem('rememberMe');
    }
  };

  const updateProfile = async (profileData) => {
    try {
      // For demo purposes, simulate API call with realistic delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Validate input
      if (!profileData.name || profileData.name.trim().length < 2) {
        return {
          success: false,
          error: 'Name must be at least 2 characters long'
        };
      }

      // Simulate successful update
      const updatedUser = {
        ...user,
        name: profileData.name.trim(),
        updatedAt: new Date().toISOString()
      };

      setUser(updatedUser);

      // Update stored user data
      if (window.electronAPI) {
        await window.electronAPI.store.set('user', updatedUser);
      } else {
        localStorage.setItem('user', JSON.stringify(updatedUser));
      }

      return { success: true, user: updatedUser };
    } catch (error) {
      console.error('Profile update failed:', error);
      return {
        success: false,
        error: 'Failed to update profile'
      };
    }
  };

  const value = {
    user,
    token,
    loading,
    login,
    register,
    logout,
    updateProfile,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
