#!/usr/bin/env node

/**
 * Test environment variables loading
 */

console.log('🧪 Testing Environment Variables');
console.log('================================');

console.log('\n📋 Current Environment Variables:');
console.log('- NODE_ENV:', process.env.NODE_ENV);
console.log('- ELECTRON_ENV:', process.env.ELECTRON_ENV);
console.log('- DB_TYPE:', process.env.DB_TYPE);
console.log('- PORT:', process.env.PORT);
console.log('- WEB_SERVER_URL:', process.env.WEB_SERVER_URL);
console.log('- CLIENT_URL:', process.env.CLIENT_URL);

console.log('\n🔍 All Environment Variables containing "WEB" or "URL":');
Object.keys(process.env)
  .filter(key => key.includes('WEB') || key.includes('URL'))
  .forEach(key => {
    console.log(`- ${key}:`, process.env[key]);
  });

console.log('\n🔍 All Environment Variables containing "SERVER":');
Object.keys(process.env)
  .filter(key => key.includes('SERVER'))
  .forEach(key => {
    console.log(`- ${key}:`, process.env[key]);
  });

console.log('\n📊 Summary:');
console.log('- Total env vars:', Object.keys(process.env).length);
console.log('- WEB_SERVER_URL present:', !!process.env.WEB_SERVER_URL);
console.log('- WEB_SERVER_URL value:', process.env.WEB_SERVER_URL || 'NOT SET');

// Test ClientManager default URL logic
const defaultUrl = process.env.WEB_SERVER_URL || 'http://*************:5001';
console.log('\n🎯 ClientManager would use URL:', defaultUrl);

if (defaultUrl.includes('*************')) {
  console.log('⚠️ WARNING: Still using ************* instead of localhost!');
} else {
  console.log('✅ Using correct localhost URL');
}
