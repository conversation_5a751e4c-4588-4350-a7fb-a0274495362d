const { getDatabase } = require('../database/init');

class AnalyticsService {
  constructor() {
    this.db = getDatabase();
  }

  // Get comprehensive sync analytics
  async getSyncAnalytics(userId, timeRange = '30d') {
    try {
      const dateFilter = this.getDateFilter(timeRange);
      
      // Basic sync statistics
      const syncStats = await this.getSyncStatistics(userId, dateFilter);
      
      // File transfer analytics
      const transferStats = await this.getTransferAnalytics(userId, dateFilter);
      
      // Conflict analytics
      const conflictStats = await this.getConflictAnalytics(userId, dateFilter);
      
      // Performance metrics
      const performanceStats = await this.getPerformanceMetrics(userId, dateFilter);
      
      // Storage analytics
      const storageStats = await this.getStorageAnalytics(userId, dateFilter);
      
      return {
        timeRange,
        period: this.getPeriodInfo(timeRange),
        sync: syncStats,
        transfers: transferStats,
        conflicts: conflictStats,
        performance: performanceStats,
        storage: storageStats,
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Analytics error:', error);
      throw error;
    }
  }

  // Get sync statistics
  async getSyncStatistics(userId, dateFilter) {
    const stats = await this.db.query(`
      SELECT
        COUNT(*) as total_syncs,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_syncs,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_syncs,
        COUNT(CASE WHEN status = 'running' THEN 1 END) as running_syncs,
        AVG(CASE WHEN status = 'completed' AND completed_at IS NOT NULL AND started_at IS NOT NULL
            THEN (julianday(completed_at) - julianday(started_at)) * 86400 END) as avg_duration,
        SUM(CASE WHEN status = 'completed' THEN files_processed ELSE 0 END) as total_files_processed,
        SUM(CASE WHEN status = 'completed' THEN bytes_transferred ELSE 0 END) as total_bytes_transferred
      FROM sync_history
      WHERE user_id = ? ${dateFilter.replace('date_column', 'started_at')}
    `, [userId]);

    const result = stats.rows[0];
    
    return {
      totalSyncs: parseInt(result.total_syncs) || 0,
      successfulSyncs: parseInt(result.successful_syncs) || 0,
      failedSyncs: parseInt(result.failed_syncs) || 0,
      runningSyncs: parseInt(result.running_syncs) || 0,
      successRate: result.total_syncs > 0 
        ? ((result.successful_syncs / result.total_syncs) * 100).toFixed(2)
        : '0.00',
      avgDuration: parseFloat(result.avg_duration) || 0,
      totalFilesProcessed: parseInt(result.total_files_processed) || 0,
      totalBytesTransferred: parseInt(result.total_bytes_transferred) || 0,
      totalBytesTransferredMB: ((parseInt(result.total_bytes_transferred) || 0) / (1024 * 1024)).toFixed(2)
    };
  }

  // Get transfer analytics
  async getTransferAnalytics(userId, dateFilter) {
    const dailyTransfers = await this.db.query(`
      SELECT
        DATE(started_at) as date,
        COUNT(*) as sync_count,
        SUM(files_processed) as files_count,
        SUM(bytes_transferred) as bytes_transferred
      FROM sync_history
      WHERE user_id = ? AND status = 'completed' ${dateFilter.replace('date_column', 'started_at')}
      GROUP BY DATE(started_at)
      ORDER BY date DESC
      LIMIT 30
    `, [userId]);

    const hourlyPattern = await this.db.query(`
      SELECT
        CAST(strftime('%H', started_at) AS INTEGER) as hour,
        COUNT(*) as sync_count,
        AVG(CASE WHEN completed_at IS NOT NULL AND started_at IS NOT NULL
            THEN (julianday(completed_at) - julianday(started_at)) * 86400 END) as avg_duration
      FROM sync_history
      WHERE user_id = ? AND status = 'completed' ${dateFilter.replace('date_column', 'started_at')}
      GROUP BY CAST(strftime('%H', started_at) AS INTEGER)
      ORDER BY hour
    `, [userId]);

    return {
      dailyTransfers: dailyTransfers.rows.map(row => ({
        date: row.date,
        syncCount: parseInt(row.sync_count),
        filesCount: parseInt(row.files_count) || 0,
        bytesTransferred: parseInt(row.bytes_transferred) || 0,
        bytesTransferredMB: ((parseInt(row.bytes_transferred) || 0) / (1024 * 1024)).toFixed(2)
      })),
      hourlyPattern: hourlyPattern.rows.map(row => ({
        hour: row.hour,
        syncCount: parseInt(row.sync_count),
        avgDuration: parseFloat(row.avg_duration) || 0
      }))
    };
  }

  // Get conflict analytics
  async getConflictAnalytics(userId, dateFilter) {
    const conflictStats = await this.db.query(`
      SELECT
        COUNT(*) as total_conflicts,
        COUNT(CASE WHEN fc.status = 'resolved' THEN 1 END) as resolved_conflicts,
        COUNT(CASE WHEN fc.status = 'pending' THEN 1 END) as pending_conflicts
      FROM file_conflicts fc
      JOIN sync_tasks st ON fc.task_id = st.id
      WHERE st.user_id = ? ${dateFilter.replace('date_column', 'fc.created_at')}
    `, [userId]);

    const resolutionStrategies = await this.db.query(`
      SELECT
        strategy as resolution_strategy,
        COUNT(*) as count
      FROM conflict_log cl
      JOIN sync_tasks st ON cl.task_id = st.id
      WHERE st.user_id = ? AND strategy IS NOT NULL ${dateFilter.replace('date_column', 'cl.created_at')}
      GROUP BY strategy
      ORDER BY count DESC
    `, [userId]);

    const result = conflictStats.rows[0];
    
    return {
      totalConflicts: parseInt(result.total_conflicts) || 0,
      resolvedConflicts: parseInt(result.resolved_conflicts) || 0,
      pendingConflicts: parseInt(result.pending_conflicts) || 0,
      resolutionRate: result.total_conflicts > 0 
        ? ((result.resolved_conflicts / result.total_conflicts) * 100).toFixed(2)
        : '0.00',
      resolutionStrategies: resolutionStrategies.rows.map(row => ({
        strategy: row.resolution_strategy,
        count: parseInt(row.count),
        percentage: result.resolved_conflicts > 0 
          ? ((row.count / result.resolved_conflicts) * 100).toFixed(1)
          : '0.0'
      }))
    };
  }

  // Get performance metrics
  async getPerformanceMetrics(userId, dateFilter) {
    const performanceData = await this.db.query(`
      SELECT
        AVG(CASE WHEN completed_at IS NOT NULL AND started_at IS NOT NULL
            THEN (julianday(completed_at) - julianday(started_at)) * 86400 END) as avg_duration,
        MIN(CASE WHEN completed_at IS NOT NULL AND started_at IS NOT NULL
            THEN (julianday(completed_at) - julianday(started_at)) * 86400 END) as min_duration,
        MAX(CASE WHEN completed_at IS NOT NULL AND started_at IS NOT NULL
            THEN (julianday(completed_at) - julianday(started_at)) * 86400 END) as max_duration,
        AVG(CASE WHEN bytes_transferred > 0 AND completed_at IS NOT NULL AND started_at IS NOT NULL
            THEN bytes_transferred / ((julianday(completed_at) - julianday(started_at)) * 86400) ELSE 0 END) as avg_transfer_speed
      FROM sync_history
      WHERE user_id = ? AND status = 'completed' AND completed_at IS NOT NULL AND started_at IS NOT NULL ${dateFilter.replace('date_column', 'started_at')}
    `, [userId]);

    const result = performanceData.rows[0];
    
    return {
      avgDuration: parseFloat(result.avg_duration) || 0,
      minDuration: parseFloat(result.min_duration) || 0,
      maxDuration: parseFloat(result.max_duration) || 0,
      avgTransferSpeed: parseFloat(result.avg_transfer_speed) || 0,
      avgTransferSpeedMBps: ((parseFloat(result.avg_transfer_speed) || 0) / (1024 * 1024)).toFixed(2)
    };
  }

  // Get storage analytics
  async getStorageAnalytics(userId, dateFilter) {
    const storageData = await this.db.query(`
      SELECT 
        COUNT(DISTINCT st.source_path) as unique_sources,
        COUNT(DISTINCT st.destination_path) as unique_destinations,
        COUNT(*) as total_tasks
      FROM sync_tasks st
      WHERE st.user_id = ? ${dateFilter.replace('date_column', 'st.created_at')}
    `, [userId]);

    const versioningData = await this.db.query(`
      SELECT 
        COUNT(*) as total_versions,
        SUM(file_size) as total_version_size,
        COUNT(DISTINCT file_path) as unique_files_versioned
      FROM file_versions fv
      JOIN sync_tasks st ON fv.task_id = st.id
      WHERE st.user_id = ? ${dateFilter.replace('date_column', 'fv.created_at')}
    `, [userId]);

    const storageResult = storageData.rows[0];
    const versionResult = versioningData.rows[0];
    
    return {
      uniqueSources: parseInt(storageResult.unique_sources) || 0,
      uniqueDestinations: parseInt(storageResult.unique_destinations) || 0,
      totalTasks: parseInt(storageResult.total_tasks) || 0,
      totalVersions: parseInt(versionResult.total_versions) || 0,
      totalVersionSize: parseInt(versionResult.total_version_size) || 0,
      totalVersionSizeMB: ((parseInt(versionResult.total_version_size) || 0) / (1024 * 1024)).toFixed(2),
      uniqueFilesVersioned: parseInt(versionResult.unique_files_versioned) || 0
    };
  }

  // Generate detailed report
  async generateReport(userId, reportType = 'comprehensive', timeRange = '30d') {
    try {
      const analytics = await this.getSyncAnalytics(userId, timeRange);
      
      const report = {
        id: `report_${Date.now()}`,
        type: reportType,
        userId,
        timeRange,
        generatedAt: new Date().toISOString(),
        analytics,
        summary: this.generateSummary(analytics),
        recommendations: this.generateRecommendations(analytics)
      };

      // Save report to database
      await this.saveReport(report);
      
      return report;
    } catch (error) {
      console.error('Report generation error:', error);
      throw error;
    }
  }

  // Generate summary
  generateSummary(analytics) {
    const { sync, transfers, conflicts, performance, storage } = analytics;
    
    return {
      totalActivity: sync.totalSyncs,
      successRate: `${sync.successRate}%`,
      avgPerformance: `${performance.avgTransferSpeedMBps} MB/s`,
      conflictRate: conflicts.totalConflicts > 0 
        ? `${((conflicts.totalConflicts / sync.totalSyncs) * 100).toFixed(1)}%`
        : '0.0%',
      storageEfficiency: storage.totalVersions > 0 
        ? `${storage.totalVersionSizeMB} MB in versions`
        : 'No versioning data',
      topPerformanceDay: transfers.dailyTransfers.length > 0 
        ? transfers.dailyTransfers.reduce((max, day) => 
            day.bytesTransferred > max.bytesTransferred ? day : max
          ).date
        : 'No data',
      recommendations: this.getQuickRecommendations(analytics)
    };
  }

  // Generate recommendations
  generateRecommendations(analytics) {
    const recommendations = [];
    const { sync, conflicts, performance, storage } = analytics;
    
    // Performance recommendations
    if (parseFloat(sync.successRate) < 95) {
      recommendations.push({
        type: 'performance',
        priority: 'high',
        title: 'Improve Sync Reliability',
        description: `Success rate is ${sync.successRate}%. Consider reviewing failed sync logs and optimizing sync configurations.`,
        action: 'Review sync settings and error logs'
      });
    }
    
    // Conflict recommendations
    if (conflicts.totalConflicts > sync.totalSyncs * 0.1) {
      recommendations.push({
        type: 'conflicts',
        priority: 'medium',
        title: 'High Conflict Rate',
        description: `${conflicts.totalConflicts} conflicts detected. Consider adjusting conflict resolution strategies.`,
        action: 'Review conflict resolution settings'
      });
    }
    
    // Storage recommendations
    if (parseFloat(storage.totalVersionSizeMB) > 1000) {
      recommendations.push({
        type: 'storage',
        priority: 'medium',
        title: 'Version Storage Cleanup',
        description: `${storage.totalVersionSizeMB} MB used for file versions. Consider cleanup of old versions.`,
        action: 'Run version cleanup or adjust retention policy'
      });
    }
    
    // Performance recommendations
    if (parseFloat(performance.avgTransferSpeedMBps) < 1) {
      recommendations.push({
        type: 'performance',
        priority: 'low',
        title: 'Transfer Speed Optimization',
        description: `Average transfer speed is ${performance.avgTransferSpeedMBps} MB/s. Consider bandwidth optimization.`,
        action: 'Review bandwidth settings and network conditions'
      });
    }
    
    return recommendations;
  }

  // Get quick recommendations
  getQuickRecommendations(analytics) {
    const recommendations = this.generateRecommendations(analytics);
    return recommendations.slice(0, 3).map(r => r.title);
  }

  // Save report to database
  async saveReport(report) {
    await this.db.query(`
      INSERT INTO analytics_reports (user_id, report_id, type, time_range, data, created_at)
      VALUES (?, ?, ?, ?, ?, datetime('now'))
    `, [
      report.userId,
      report.id,
      report.type,
      report.timeRange,
      JSON.stringify(report)
    ]);
  }

  // Get saved reports
  async getReports(userId, limit = 10) {
    const result = await this.db.query(`
      SELECT * FROM analytics_reports 
      WHERE user_id = ? 
      ORDER BY created_at DESC 
      LIMIT ?
    `, [userId, limit]);

    return result.rows.map(row => ({
      id: row.report_id,
      type: row.type,
      timeRange: row.time_range,
      createdAt: row.created_at,
      summary: JSON.parse(row.data).summary
    }));
  }

  // Utility methods
  getDateFilter(timeRange) {
    const now = new Date();
    let startDate;
    
    switch (timeRange) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
    
    return `AND date_column >= '${startDate.toISOString()}'`;
  }

  getPeriodInfo(timeRange) {
    const now = new Date();
    const periods = {
      '7d': { days: 7, label: 'Last 7 days' },
      '30d': { days: 30, label: 'Last 30 days' },
      '90d': { days: 90, label: 'Last 90 days' },
      '1y': { days: 365, label: 'Last year' }
    };
    
    const period = periods[timeRange] || periods['30d'];
    const startDate = new Date(now.getTime() - period.days * 24 * 60 * 60 * 1000);
    
    return {
      label: period.label,
      days: period.days,
      startDate: startDate.toISOString(),
      endDate: now.toISOString()
    };
  }
}

module.exports = AnalyticsService;
