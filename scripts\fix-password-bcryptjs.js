#!/usr/bin/env node

/**
 * Fix password hash using bcryptjs (same as server)
 */

const { Pool } = require('pg');
const bcrypt = require('bcryptjs'); // Same as server

console.log('🔧 Fixing Password Hash with bcryptjs');
console.log('=====================================');

async function generateAndUpdatePassword() {
  console.log('\n1. 🐘 Connecting to PostgreSQL...');
  
  const pool = new Pool({
    host: '*************',
    port: 5432,
    database: 'syncmasterpro',
    user: 'pi',
    password: 'ubuntu'
  });

  try {
    const client = await pool.connect();
    console.log('✅ Connected to PostgreSQL');
    
    // Generate new hash with bcryptjs (same as server)
    console.log('\n2. 🔐 Generating password hash with bcryptjs...');
    const password = 'admin';
    const saltRounds = 12; // Same as server
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    
    console.log(`📝 Generated hash: ${hashedPassword.substring(0, 30)}...`);
    
    // Test the hash immediately
    console.log('\n3. 🧪 Testing hash before saving...');
    const testResult = await bcrypt.compare('admin', hashedPassword);
    
    if (!testResult) {
      console.log('❌ Hash test failed! Something is wrong.');
      return;
    }
    
    console.log('✅ Hash test passed');
    
    // Update password in database
    console.log('\n4. 💾 Updating password in database...');
    const updateResult = await client.query(
      'UPDATE users SET password = $1, updated_at = CURRENT_TIMESTAMP WHERE email = $2',
      [hashedPassword, '<EMAIL>']
    );
    
    if (updateResult.rowCount > 0) {
      console.log('✅ Password updated successfully');
    } else {
      console.log('❌ No user found to update');
      return;
    }
    
    // Verify the update
    console.log('\n5. 🔍 Verifying database update...');
    const verifyResult = await client.query(
      'SELECT password FROM users WHERE email = $1',
      ['<EMAIL>']
    );
    
    if (verifyResult.rows.length > 0) {
      const dbHash = verifyResult.rows[0].password;
      console.log(`📝 Database hash: ${dbHash.substring(0, 30)}...`);
      
      // Test the hash from database
      const dbTestResult = await bcrypt.compare('admin', dbHash);
      
      if (dbTestResult) {
        console.log('✅ Database hash verification passed');
      } else {
        console.log('❌ Database hash verification failed');
      }
    }
    
    // Clean up sessions
    console.log('\n6. 🧹 Cleaning up old sessions...');
    const deleteResult = await client.query('DELETE FROM sessions');
    console.log(`✅ Deleted ${deleteResult.rowCount} old sessions`);
    
    client.release();
    await pool.end();
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    await pool.end();
    throw error;
  }
}

async function testLogin() {
  console.log('\n7. 🔐 Testing web server login...');
  
  const axios = require('axios');
  
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Login Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Web server login successful!');
      console.log('👤 User:', response.data.user.name);
      console.log('🔑 Token length:', response.data.token.length);
      return true;
    } else {
      console.log('❌ Web server login failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Web server login error:', error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting password fix with bcryptjs...\n');
    
    // Generate and update password
    await generateAndUpdatePassword();
    
    // Test login
    const loginSuccess = await testLogin();
    
    console.log('\n📊 Summary:');
    console.log('===========');
    console.log('- Password Hash Generation:', '✅ Success');
    console.log('- Database Update:', '✅ Success');
    console.log('- Web Server Login:', loginSuccess ? '✅ Success' : '❌ Failed');
    
    if (loginSuccess) {
      console.log('\n🎉 SUCCESS! Web server authentication is now working!');
      console.log('🔍 You can now:');
      console.log('   1. Open http://localhost:3001 in browser');
      console.log('   2. <NAME_EMAIL> / admin');
      console.log('   3. View real desktop client data');
    } else {
      console.log('\n❌ Authentication still has issues');
      console.log('💡 Check web server logs for more details');
    }
    
  } catch (error) {
    console.error('\n❌ Fix failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
