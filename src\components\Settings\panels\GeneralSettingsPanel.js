import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useSettings } from '../../../contexts/SettingsContext';
import { useNotification } from '../../../contexts/NotificationContext';
import Toggle from '../../UI/Toggle';

const GeneralSettingsPanel = ({ user }) => {
  const { updateProfile } = useAuth();
  const { language, setLanguage, t } = useLanguage();
  const { settings, updateSetting } = useSettings();
  const { addNotification } = useNotification();
  
  const [profileData, setProfileData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    avatar: user?.avatar || ''
  });
  const [profileLoading, setProfileLoading] = useState(false);

  // Update profile data when user changes
  useEffect(() => {
    if (user) {
      setProfileData({
        name: user.name || '',
        email: user.email || '',
        avatar: user.avatar || ''
      });
    }
  }, [user]);

  const handleProfileUpdate = async (e) => {
    e.preventDefault();
    setProfileLoading(true);

    try {
      const result = await updateProfile(profileData);
      if (result.success) {
        addNotification(t('profileUpdatedSuccessfully'), 'success');
      } else {
        addNotification(result.error || t('failedToUpdateProfile'), 'error');
      }
    } catch (error) {
      addNotification(t('failedToUpdateProfile'), 'error');
    } finally {
      setProfileLoading(false);
    }
  };

  const handleLanguageChange = async (newLanguage) => {
    try {
      await setLanguage(newLanguage);
      updateSetting('language', newLanguage);
      addNotification(t('languageChanged'), 'success');
    } catch (error) {
      console.error('Failed to change language:', error);
      addNotification(t('failedToChangeLanguage'), 'error');
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t('generalSettings')}</h2>
        <p className="text-gray-600 dark:text-gray-300 mt-1">{t('generalSettingsDescription')}</p>
      </div>

      {/* Profile Settings */}
      <div className="space-y-6">
        <h3 className="text-md font-medium text-gray-900 dark:text-white">{t('profileInformation')}</h3>
        
        <form onSubmit={handleProfileUpdate} className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('fullName')}
            </label>
            <input
              type="text"
              id="name"
              value={profileData.name}
              onChange={(e) => setProfileData({ ...profileData, name: e.target.value })}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('emailAddress')}
            </label>
            <input
              type="email"
              id="email"
              value={profileData.email}
              disabled
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{t('emailCannotBeChanged')}</p>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={profileLoading}
              className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 dark:focus:ring-offset-gray-800"
            >
              {profileLoading ? t('saving') : t('updateProfile')}
            </button>
          </div>
        </form>
      </div>

      {/* Language Settings */}
      <div className="space-y-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white">{t('languageSettings')}</h3>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            {t('interfaceLanguage')}
          </label>
          
          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={() => handleLanguageChange('en')}
              className={`p-4 border-2 rounded-lg transition-all ${
                language === 'en'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
            >
              <div className="flex items-center space-x-3">
                <span className="text-2xl">🇺🇸</span>
                <div className="text-left">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">English</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">United States</div>
                </div>
              </div>
            </button>

            <button
              onClick={() => handleLanguageChange('vi')}
              className={`p-4 border-2 rounded-lg transition-all ${
                language === 'vi'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
            >
              <div className="flex items-center space-x-3">
                <span className="text-2xl">🇻🇳</span>
                <div className="text-left">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">Tiếng Việt</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Việt Nam</div>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Application Settings */}
      <div className="space-y-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white">{t('applicationSettings')}</h3>
        
        <div className="space-y-4">
          <Toggle
            label={t('showNotifications')}
            description={t('showNotificationsDescription')}
            checked={settings.showNotifications}
            onChange={(checked) => updateSetting('showNotifications', checked)}
          />

          <Toggle
            label={t('soundEnabled')}
            description={t('soundEnabledDescription')}
            checked={settings.soundEnabled}
            onChange={(checked) => updateSetting('soundEnabled', checked)}
          />
        </div>
      </div>
    </div>
  );
};

export default GeneralSettingsPanel;
