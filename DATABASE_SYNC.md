# Database Synchronization Guide

## 🎯 Overview

SyncMasterPro supports both SQLite (local) and PostgreSQL (web server) databases with bidirectional synchronization capabilities.

## 🗄️ Database Setup

### Current Setup
- **Local (Desktop)**: SQLite database at `./data/syncmasterpro.db`
- **Web Server**: PostgreSQL database (to be configured)

## 🚀 PostgreSQL Setup

### Step 1: Configure PostgreSQL Connection

1. **Edit `.env.postgresql`** with your server details:
```env
DB_TYPE=postgresql
DB_HOST=your-server-ip-or-domain
DB_PORT=5432
DB_NAME=syncmasterpro
DB_USER=syncuser
DB_PASSWORD=your_secure_password_here
```

2. **Create database on PostgreSQL server**:
```sql
-- Connect to PostgreSQL as admin
psql -U postgres -h your-server-ip

-- Run the setup script
\i database/setup-postgresql.sql
```

### Step 2: Initialize PostgreSQL Database

```bash
# Load PostgreSQL config and create tables
npm run setup-pg
```

## 🔄 Database Synchronization

### Available Commands

```bash
# Test PostgreSQL connection
npm run test-pg

# Create PostgreSQL tables
npm run create-tables

# Sync from SQLite to PostgreSQL
npm run sync-to-pg
```

### Sync Scenarios

#### 1. **Initial Setup - Local to Web**
```bash
# Export current SQLite data
npm run export-data

# Setup PostgreSQL tables
npm run setup-pg

# Sync data to PostgreSQL
npm run sync-to-pg
```

#### 2. **Web to Local Sync**
```bash
# Download latest data from web
npm run sync-from-pg
```

#### 3. **Local to Web Sync**
```bash
# Upload local changes to web
npm run sync-to-pg
```

## ⚙️ Configuration Modes

### Development Mode (SQLite)
```env
DB_TYPE=sqlite
```
- Uses local SQLite database
- Perfect for development and testing
- No network dependency

### Production Mode (PostgreSQL)
```env
DB_TYPE=postgresql
DB_HOST=your-server.com
DB_NAME=syncmasterpro
DB_USER=syncuser
DB_PASSWORD=secure_password
```
- Uses remote PostgreSQL database
- Supports multiple users
- Web-based management

### Hybrid Mode
- Desktop app uses SQLite locally
- Web interface uses PostgreSQL
- Manual sync between databases

## 🔧 Advanced Usage

### Manual Database Operations

```javascript
const DatabaseSync = require('./scripts/sync-databases');

const sync = new DatabaseSync();
await sync.initialize();

// Custom sync operations
await sync.syncSQLiteToPostgreSQL();
await sync.exportSQLiteData();
await sync.close();
```

### Automated Sync

You can set up automated sync using cron jobs or scheduled tasks:

```bash
# Linux/Mac crontab
# Sync every hour
0 * * * * cd /path/to/syncmasterpro && npm run sync-to-pg

# Windows Task Scheduler
# Create task to run: npm run sync-to-pg
```

## 🛡️ Security Considerations

### Database Security
- Use strong passwords for PostgreSQL
- Enable SSL connections
- Restrict database access by IP
- Regular backups

### Data Privacy
- Sensitive data is hashed (passwords)
- JWT tokens for authentication
- Session management
- Input validation

## 📊 Monitoring

### Check Sync Status
```bash
# View database sizes
ls -la data/
psql -U syncuser -d syncmasterpro -c "SELECT schemaname,tablename,n_tup_ins,n_tup_upd,n_tup_del FROM pg_stat_user_tables;"
```

### Backup Strategies
```bash
# SQLite backup
cp data/syncmasterpro.db data/backup-$(date +%Y%m%d).db

# PostgreSQL backup
pg_dump -U syncuser -h your-server syncmasterpro > backup-$(date +%Y%m%d).sql
```

## 🚨 Troubleshooting

### Common Issues

#### Connection Failed
```
Error: connect ECONNREFUSED
```
**Solution**: Check PostgreSQL server, host, port, and credentials

#### Permission Denied
```
Error: permission denied for table users
```
**Solution**: Grant proper privileges to database user

#### Sync Conflicts
```
Error: duplicate key value violates unique constraint
```
**Solution**: Use `ON CONFLICT` handling in sync scripts

### Recovery Procedures

#### Restore from Backup
```bash
# SQLite restore
cp data/backup-20240101.db data/syncmasterpro.db

# PostgreSQL restore
psql -U syncuser -d syncmasterpro < backup-20240101.sql
```

#### Reset Database
```bash
# Clear and reinitialize
rm data/syncmasterpro.db
npm run setup
npm run setup-pg
```

## 📈 Performance Tips

### Optimization
- Regular VACUUM for SQLite
- Index optimization for PostgreSQL
- Batch operations for large syncs
- Connection pooling

### Monitoring
- Database size monitoring
- Sync performance metrics
- Error rate tracking
- User activity logs

## 🔮 Future Enhancements

- Real-time sync with WebSockets
- Conflict resolution UI
- Multi-master replication
- Cloud database integration
- Automated failover
