import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../contexts/LanguageContext';
import { useSync } from '../../contexts/SyncContext';

const QuickActions = () => {
  const navigate = useNavigate();
  const { startAllSyncs, stopAllSyncs, activeSyncs } = useSync();
  const { t } = useLanguage();

  const actions = [
    {
      title: t('createNewTask'),
      description: t('createNewTaskDescription'),
      icon: <PlusIcon className="w-5 h-5" />,
      color: 'blue',
      onClick: () => navigate('/sync-tasks?action=new')
    },
    {
      title: t('startAllSyncs'),
      description: t('startAllSyncsDescription'),
      icon: <PlayIcon className="w-5 h-5" />,
      color: 'green',
      onClick: startAllSyncs,
      disabled: activeSyncs.size > 0
    },
    {
      title: t('stopAllSyncs'),
      description: t('stopAllSyncsDescription'),
      icon: <StopIcon className="w-5 h-5" />,
      color: 'red',
      onClick: stopAllSyncs,
      disabled: activeSyncs.size === 0
    },
    {
      title: t('viewAllTasks'),
      description: t('viewAllTasksDescription'),
      icon: <ClockIcon className="w-5 h-5" />,
      color: 'purple',
      onClick: () => navigate('/history')
    }
  ];

  const getColorClasses = (color, disabled = false) => {
    if (disabled) {
      return 'bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 border-gray-200 dark:border-gray-700 cursor-not-allowed';
    }

    const colors = {
      blue: 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-700 hover:bg-blue-100 dark:hover:bg-blue-800',
      green: 'bg-green-50 dark:bg-green-900 text-green-600 dark:text-green-400 border-green-200 dark:border-green-700 hover:bg-green-100 dark:hover:bg-green-800',
      red: 'bg-red-50 dark:bg-red-900 text-red-600 dark:text-red-400 border-red-200 dark:border-red-700 hover:bg-red-100 dark:hover:bg-red-800',
      purple: 'bg-purple-50 dark:bg-purple-900 text-purple-600 dark:text-purple-400 border-purple-200 dark:border-purple-700 hover:bg-purple-100 dark:hover:bg-purple-800'
    };
    return colors[color];
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t('quickActions')}</h2>
      </div>
      <div className="p-6 space-y-3">
        {actions.map((action, index) => (
          <button
            key={index}
            onClick={action.onClick}
            disabled={action.disabled}
            className={`
              w-full p-4 rounded-lg border transition-colors text-left
              ${getColorClasses(action.color, action.disabled)}
            `}
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                {action.icon}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium">{action.title}</p>
                <p className="text-xs opacity-75">{action.description}</p>
              </div>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

// Icon components
const PlusIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
  </svg>
);

const PlayIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z" />
  </svg>
);

const StopIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M5.25 7.5A2.25 2.25 0 0 1 7.5 5.25h9a2.25 2.25 0 0 1 2.25 2.25v9a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-9Z" />
  </svg>
);

const ClockIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
);

export default QuickActions;
