#!/usr/bin/env node

/**
 * Test web UI for console errors
 */

const axios = require('axios');

console.log('🔍 Testing Web UI Console Errors');
console.log('=================================');

async function testWebUIEndpoints() {
  console.log('\n1. 🌐 Testing web UI endpoints...');
  
  try {
    // Test web UI is accessible
    const webUIResponse = await axios.get('http://localhost:3001', {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Web UI Response:', webUIResponse.status);
    
    if (webUIResponse.status === 200) {
      console.log('✅ Web UI is accessible');
    } else {
      console.log('❌ Web UI not accessible:', webUIResponse.status);
      return false;
    }
    
    // Test API endpoints
    console.log('\n2. 🔌 Testing API endpoints...');
    
    // Test login
    const loginResponse = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (loginResponse.status === 200) {
      console.log('✅ Login API working');
      const token = loginResponse.data.token;
      
      // Test clients API
      const clientsResponse = await axios.get('http://localhost:5001/api/clients', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000,
        validateStatus: () => true
      });
      
      if (clientsResponse.status === 200) {
        console.log('✅ Clients API working');
        console.log(`📊 Found ${clientsResponse.data.clients?.length || 0} clients`);
      } else {
        console.log('❌ Clients API failed:', clientsResponse.status);
      }
      
      // Test sync tasks API
      const tasksResponse = await axios.get('http://localhost:5001/api/sync/tasks', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000,
        validateStatus: () => true
      });
      
      if (tasksResponse.status === 200) {
        console.log('✅ Sync Tasks API working');
        console.log(`📋 Found ${tasksResponse.data.tasks?.length || 0} tasks`);
      } else {
        console.log('❌ Sync Tasks API failed:', tasksResponse.status);
      }
      
      // Test sync history API
      const historyResponse = await axios.get('http://localhost:5001/api/sync/history', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000,
        validateStatus: () => true
      });
      
      if (historyResponse.status === 200) {
        console.log('✅ Sync History API working');
        console.log(`📈 Found ${historyResponse.data.history?.length || 0} history entries`);
      } else {
        console.log('❌ Sync History API failed:', historyResponse.status);
      }
      
      return true;
      
    } else {
      console.log('❌ Login API failed:', loginResponse.status);
      return false;
    }
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    return false;
  }
}

async function checkCommonIssues() {
  console.log('\n3. 🔍 Checking common issues...');
  
  // Check if AuthContext exists
  const fs = require('fs');
  const path = require('path');
  
  const authContextPath = path.join(__dirname, '../web-ui/src/contexts/AuthContext.js');
  if (fs.existsSync(authContextPath)) {
    console.log('✅ AuthContext.js exists');
  } else {
    console.log('❌ AuthContext.js missing');
  }
  
  // Check if all page components exist
  const pagesDir = path.join(__dirname, '../web-ui/src/pages');
  const requiredPages = [
    'Dashboard.js',
    'ClientManagement.js',
    'SyncTasks.js',
    'Analytics.js',
    'Settings.js',
    'Login.js'
  ];
  
  console.log('\n📄 Checking page components:');
  for (const page of requiredPages) {
    const pagePath = path.join(pagesDir, page);
    if (fs.existsSync(pagePath)) {
      console.log(`✅ ${page} exists`);
    } else {
      console.log(`❌ ${page} missing`);
    }
  }
  
  // Check package.json
  const packagePath = path.join(__dirname, '../web-ui/package.json');
  if (fs.existsSync(packagePath)) {
    console.log('✅ web-ui package.json exists');
    
    try {
      const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      const requiredDeps = ['react', 'react-dom', 'react-router-dom'];
      
      console.log('\n📦 Checking dependencies:');
      for (const dep of requiredDeps) {
        if (packageData.dependencies && packageData.dependencies[dep]) {
          console.log(`✅ ${dep}: ${packageData.dependencies[dep]}`);
        } else {
          console.log(`❌ ${dep} missing`);
        }
      }
    } catch (error) {
      console.log('❌ Error reading package.json:', error.message);
    }
  } else {
    console.log('❌ web-ui package.json missing');
  }
}

async function main() {
  try {
    console.log('🚀 Starting web UI console error test...\n');
    
    // Test endpoints
    const endpointsWorking = await testWebUIEndpoints();
    
    // Check common issues
    await checkCommonIssues();
    
    console.log('\n📊 Summary:');
    console.log('===========');
    console.log('- Web UI Accessible:', endpointsWorking ? '✅' : '❌');
    console.log('- API Endpoints:', endpointsWorking ? '✅' : '❌');
    
    if (endpointsWorking) {
      console.log('\n🎉 Web UI appears to be working!');
      console.log('');
      console.log('💡 If you see console errors in browser:');
      console.log('   1. Open browser DevTools (F12)');
      console.log('   2. Go to Console tab');
      console.log('   3. Check for any red error messages');
      console.log('   4. Look for failed network requests in Network tab');
      console.log('');
      console.log('🌐 Access web UI at: http://localhost:3001');
      console.log('🔐 Login with: <EMAIL> / admin');
    } else {
      console.log('\n❌ Web UI has issues');
      console.log('💡 Check if both servers are running:');
      console.log('   - Web Server: npm run server-web');
      console.log('   - Web UI: npm run web-ui');
      console.log('   - Or combined: npm run start-web-management');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
