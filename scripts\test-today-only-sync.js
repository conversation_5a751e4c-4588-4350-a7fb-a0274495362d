// Test script for "today-only" sync type
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const baseURL = 'http://localhost:5002/api';

async function testTodayOnlySync() {
  console.log('📅 Testing Today-Only Sync Type\n');
  
  try {
    // 1. Login
    console.log('1. 🔐 Logging in...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    const headers = { 'Authorization': `Bearer ${token}` };
    console.log('✅ Login successful');
    
    // 2. Create test directories
    console.log('\n2. 📁 Creating test directories...');
    const testDir = path.join(process.cwd(), 'test-today-only');
    const sourceDir = path.join(testDir, 'source');
    const destDir = path.join(testDir, 'dest');
    
    // Create directories
    [testDir, sourceDir, destDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
    
    console.log(`📂 Source: ${sourceDir}`);
    console.log(`📂 Destination: ${destDir}`);
    
    // 3. Create test files with different dates
    console.log('\n3. 📄 Creating test files...');
    
    // Today's files
    const todayFile1 = path.join(sourceDir, 'today-file-1.txt');
    const todayFile2 = path.join(sourceDir, 'today-file-2.txt');
    
    fs.writeFileSync(todayFile1, `Today's file 1 - Created: ${new Date().toISOString()}`);
    fs.writeFileSync(todayFile2, `Today's file 2 - Created: ${new Date().toISOString()}`);
    
    // Old file (simulate by creating and changing mtime)
    const oldFile = path.join(sourceDir, 'old-file.txt');
    fs.writeFileSync(oldFile, `Old file - Should not sync`);

    // Change old file's timestamps to yesterday
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    fs.utimesSync(oldFile, yesterday, yesterday);

    // Check actual file timestamps
    const oldFileStats = fs.statSync(oldFile);
    console.log(`📊 Old file birthtime: ${oldFileStats.birthtime.toISOString()}`);
    console.log(`📊 Old file mtime: ${oldFileStats.mtime.toISOString()}`);

    // Note: On Windows, birthtime cannot be changed after creation
    // So we'll create the old file in a different way
    
    console.log('✅ Created today-file-1.txt (today)');
    console.log('✅ Created today-file-2.txt (today)');
    console.log('✅ Created old-file.txt (yesterday)');
    
    // 4. Create today-only sync task
    console.log('\n4. ➕ Creating today-only sync task...');
    
    const taskData = {
      name: 'Today Only Test Task',
      sourcePath: sourceDir,
      destinationPath: destDir,
      syncType: 'today-only',
      options: {
        deleteExtraFiles: false,
        preserveTimestamps: true,
        enableRealtime: false
      }
    };
    
    const createResponse = await axios.post(`${baseURL}/sync/tasks`, taskData, { headers });
    const taskId = createResponse.data.task.id;
    
    console.log(`✅ Created task with ID: ${taskId}`);
    
    // 5. Run sync
    console.log('\n5. 🔄 Running today-only sync...');
    
    const startResponse = await axios.post(`${baseURL}/sync/tasks/${taskId}/start`, {}, { headers });
    console.log('✅ Sync started');
    
    // Wait for sync to complete
    console.log('⏳ Waiting for sync to complete...');
    await delay(5000);
    
    // 6. Check results
    console.log('\n6. 📊 Checking sync results...');
    
    const todayFile1Dest = path.join(destDir, 'today-file-1.txt');
    const todayFile2Dest = path.join(destDir, 'today-file-2.txt');
    const oldFileDest = path.join(destDir, 'old-file.txt');
    
    const todayFile1Synced = fs.existsSync(todayFile1Dest);
    const todayFile2Synced = fs.existsSync(todayFile2Dest);
    const oldFileSynced = fs.existsSync(oldFileDest);
    
    console.log(`📄 today-file-1.txt synced: ${todayFile1Synced ? '✅ YES' : '❌ NO'}`);
    console.log(`📄 today-file-2.txt synced: ${todayFile2Synced ? '✅ YES' : '❌ NO'}`);
    console.log(`📄 old-file.txt synced: ${oldFileSynced ? '❌ YES (should not)' : '✅ NO (correct)'}`);
    
    // 7. Check history
    console.log('\n7. 📋 Checking sync history...');
    
    const historyResponse = await axios.get(`${baseURL}/sync/history?taskId=${taskId}&limit=5`, { headers });
    const history = historyResponse.data.history || [];
    
    console.log(`📊 History entries: ${history.length}`);
    
    if (history.length > 0) {
      const latestEntry = history[0];
      console.log(`📄 Latest sync: ${latestEntry.status} - ${latestEntry.filesProcessed} files processed`);
      
      if (latestEntry.details && typeof latestEntry.details === 'object') {
        console.log(`📊 Sync type: ${latestEntry.details.syncType || 'unknown'}`);
      }
    }
    
    // 8. Test real-time sync
    console.log('\n8. ⚡ Testing real-time today-only sync...');
    
    // Update task to enable real-time
    await axios.put(`${baseURL}/sync/tasks/${taskId}`, {
      options: {
        deleteExtraFiles: false,
        preserveTimestamps: true,
        enableRealtime: true
      }
    }, { headers });
    
    // Start real-time sync
    await axios.post(`${baseURL}/sync/tasks/${taskId}/realtime/start`, {}, { headers });
    console.log('✅ Real-time sync started');
    
    // Create new today file
    const realtimeFile = path.join(sourceDir, `realtime-today-${Date.now()}.txt`);
    fs.writeFileSync(realtimeFile, `Real-time today file - ${new Date().toISOString()}`);
    console.log(`📄 Created real-time test file: ${path.basename(realtimeFile)}`);
    
    // Wait for real-time sync
    console.log('⏳ Waiting for real-time sync...');
    await delay(8000);
    
    // Check if real-time file was synced
    const realtimeFileDest = path.join(destDir, path.basename(realtimeFile));
    const realtimeFileSynced = fs.existsSync(realtimeFileDest);
    
    console.log(`📄 Real-time file synced: ${realtimeFileSynced ? '✅ YES' : '❌ NO'}`);
    
    // Stop real-time sync
    await axios.post(`${baseURL}/sync/tasks/${taskId}/realtime/stop`, {}, { headers });
    console.log('🛑 Real-time sync stopped');
    
    // 9. Summary
    console.log('\n📊 TEST SUMMARY:');
    console.log(`   Today files synced: ${todayFile1Synced && todayFile2Synced ? '✅ YES' : '❌ NO'}`);
    console.log(`   Old file filtered: ${!oldFileSynced ? '✅ YES' : '❌ NO'}`);
    console.log(`   Real-time working: ${realtimeFileSynced ? '✅ YES' : '❌ NO'}`);
    console.log(`   History created: ${history.length > 0 ? '✅ YES' : '❌ NO'}`);
    
    const allTestsPassed = todayFile1Synced && todayFile2Synced && !oldFileSynced && realtimeFileSynced && history.length > 0;
    
    console.log(`\n🎯 OVERALL RESULT: ${allTestsPassed ? '✅ SUCCESS' : '❌ FAILED'}`);
    
    if (allTestsPassed) {
      console.log('🎉 Today-Only sync type is working perfectly!');
    } else {
      console.log('🔧 Some issues found. Check the logs above.');
    }
    
    // Cleanup
    console.log('\n🧹 Cleaning up...');
    await axios.delete(`${baseURL}/sync/tasks/${taskId}`, { headers });
    console.log('✅ Test task deleted');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Export for manual use
module.exports = { testTodayOnlySync };

// Auto-run if called directly
if (require.main === module) {
  testTodayOnlySync();
}

console.log('📅 Today-Only Sync Tester loaded!');
console.log('📝 Run: node scripts/test-today-only-sync.js');
