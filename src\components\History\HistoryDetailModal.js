import React from 'react';
import { useLanguage } from '../../contexts/LanguageContext';

const HistoryDetailModal = ({ isOpen, onClose, entry }) => {
  const { t } = useLanguage();

  if (!isOpen || !entry) return null;

  // Format file size
  const formatFileSize = (bytes) => {
    if (!bytes || bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format duration
  const formatDuration = (ms) => {
    if (!ms || ms === 0) return '0s';
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900';
      case 'error':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900';
      case 'running':
        return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700';
    }
  };

  // Calculate transfer rate
  const getTransferRate = () => {
    if (!entry.totalSize || !entry.duration) return 'N/A';
    const bytesPerSecond = entry.totalSize / (entry.duration / 1000);
    return formatFileSize(bytesPerSecond) + '/s';
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-75 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('syncHistoryDetails')}
            </h3>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(entry.status)}`}>
              {entry.status === 'completed' ? t('completed') :
               entry.status === 'error' ? t('failed') :
               entry.status === 'running' ? t('syncing') :
               entry.status.charAt(0).toUpperCase() + entry.status.slice(1)}
            </span>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-150"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="mt-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="space-y-4">
              <h4 className="text-md font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                {t('basicInformation')}
              </h4>

              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400">{t('task')} ID:</span>
                  <span className="text-sm text-gray-900 dark:text-white">#{entry.taskId}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400">{t('status')}:</span>
                  <span className={`text-sm font-medium ${entry.status === 'completed' ? 'text-green-600 dark:text-green-400' : entry.status === 'error' ? 'text-red-600 dark:text-red-400' : 'text-blue-600 dark:text-blue-400'}`}>
                    {entry.status === 'completed' ? t('completed') :
                     entry.status === 'error' ? t('failed') :
                     entry.status === 'running' ? t('syncing') :
                     entry.status.charAt(0).toUpperCase() + entry.status.slice(1)}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Started At:</span>
                  <span className="text-sm text-gray-900 dark:text-white">
                    {new Date(entry.timestamp || entry.startedAt).toLocaleString()}
                  </span>
                </div>

                {entry.completedAt && (
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Completed At:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {new Date(entry.completedAt).toLocaleString()}
                    </span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400">{t('duration')}:</span>
                  <span className="text-sm text-gray-900 dark:text-white">
                    {formatDuration(entry.duration || 0)}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-md font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                {t('fileStatistics')}
              </h4>

              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400">{t('filesProcessed')}:</span>
                  <span className="text-sm text-gray-900 dark:text-white">
                    {(entry.filesProcessed || 0).toLocaleString()}
                  </span>
                </div>

                {entry.filesAdded !== undefined && (
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Files Added:</span>
                    <span className="text-sm text-green-600 dark:text-green-400">
                      +{(entry.filesAdded || 0).toLocaleString()}
                    </span>
                  </div>
                )}

                {entry.filesUpdated !== undefined && (
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Files Updated:</span>
                    <span className="text-sm text-blue-600 dark:text-blue-400">
                      ~{(entry.filesUpdated || 0).toLocaleString()}
                    </span>
                  </div>
                )}

                {entry.filesDeleted !== undefined && (
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Files Deleted:</span>
                    <span className="text-sm text-red-600 dark:text-red-400">
                      -{(entry.filesDeleted || 0).toLocaleString()}
                    </span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Size:</span>
                  <span className="text-sm text-gray-900 dark:text-white">
                    {formatFileSize(entry.totalSize || 0)}
                  </span>
                </div>

                {entry.bytesTransferred && (
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Bytes Transferred:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {formatFileSize(entry.bytesTransferred)}
                    </span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Transfer Rate:</span>
                  <span className="text-sm text-gray-900 dark:text-white">
                    {getTransferRate()}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Error Information */}
          {entry.error && (
            <div className="mb-6">
              <h4 className="text-md font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2 mb-4">
                {t('errorInformation')}
              </h4>
              <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                      {t('syncError')}
                    </h3>
                    <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                      <p>{entry.error}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Additional Details */}
          {entry.details && (
            <div className="mb-6">
              <h4 className="text-md font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2 mb-4">
                {t('additionalDetails')}
              </h4>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-md p-4">
                <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                  {typeof entry.details === 'string'
                    ? entry.details
                    : JSON.stringify(entry.details, null, 2)
                  }
                </pre>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 dark:bg-gray-700 text-white text-sm font-medium rounded-md hover:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-150"
          >
            {t('close')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default HistoryDetailModal;
