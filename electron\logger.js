const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');
const { app } = require('electron');

// Get logs directory
const getLogsDir = () => {
  if (app) {
    return path.join(app.getPath('userData'), 'logs');
  }
  return path.join(__dirname, '..', 'logs', 'desktop');
};

// Create logger configuration
const createLogger = () => {
  const logsDir = getLogsDir();
  
  // Console format for development
  const consoleFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.colorize(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
      let metaStr = '';
      if (Object.keys(meta).length > 0) {
        metaStr = ' ' + JSON.stringify(meta);
      }
      return `[${timestamp}] ${level}: ${message}${metaStr}`;
    })
  );

  // File format for production
  const fileFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    winston.format.json()
  );

  // Create transports
  const transports = [
    // Console transport (always enabled in development)
    new winston.transports.Console({
      level: 'debug',
      format: consoleFormat
    }),

    // Daily rotate file for all logs
    new DailyRotateFile({
      filename: path.join(logsDir, 'desktop-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '14d',
      level: 'info',
      format: fileFormat
    }),

    // Error-only file
    new DailyRotateFile({
      filename: path.join(logsDir, 'desktop-error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '30d',
      level: 'error',
      format: fileFormat
    }),

    // Debug file (only in development)
    ...(process.env.NODE_ENV === 'development' ? [
      new DailyRotateFile({
        filename: path.join(logsDir, 'desktop-debug-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        maxSize: '50m',
        maxFiles: '7d',
        level: 'debug',
        format: fileFormat
      })
    ] : [])
  ];

  // Create logger
  const logger = winston.createLogger({
    level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
    transports,
    exitOnError: false
  });

  // Handle uncaught exceptions and rejections
  logger.exceptions.handle(
    new DailyRotateFile({
      filename: path.join(logsDir, 'desktop-exceptions-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '30d',
      format: fileFormat
    })
  );

  logger.rejections.handle(
    new DailyRotateFile({
      filename: path.join(logsDir, 'desktop-rejections-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '30d',
      format: fileFormat
    })
  );

  return logger;
};

// Create and export logger instance
const logger = createLogger();

// Add helper methods
logger.logSync = (level, taskId, message, meta = {}) => {
  logger[level](`[SYNC-${taskId}] ${message}`, meta);
};

logger.logAPI = (method, url, status, duration, meta = {}) => {
  const level = status >= 400 ? 'error' : status >= 300 ? 'warn' : 'info';
  logger[level](`[API] ${method} ${url} - ${status} (${duration}ms)`, meta);
};

logger.logElectron = (event, message, meta = {}) => {
  logger.info(`[ELECTRON] ${event}: ${message}`, meta);
};

// Log startup info
logger.info('🚀 Desktop Logger initialized', {
  logsDir: getLogsDir(),
  level: logger.level,
  environment: process.env.NODE_ENV || 'development'
});

module.exports = logger;
