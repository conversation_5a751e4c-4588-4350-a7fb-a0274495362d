// Fix PostgreSQL schema to match SQLite
const { Pool } = require('pg');

async function fixPostgreSQLSchema() {
  console.log('🔧 Fixing PostgreSQL Schema to Match SQLite\n');
  
  const pgPool = new Pool({
    user: process.env.PG_USER || 'pi',
    host: process.env.PG_HOST || '*************',
    database: process.env.PG_DATABASE || 'syncmasterpro',
    password: process.env.PG_PASSWORD || 'ubuntu',
    port: process.env.PG_PORT || 5432,
  });
  
  try {
    console.log('🔌 Connecting to PostgreSQL...');
    await pgPool.query('SELECT NOW()');
    console.log('✅ PostgreSQL connected');
    
    // 1. Fix users table
    console.log('\n1. 👥 Fixing users table...');
    
    const usersMigrations = [
      {
        name: 'role',
        sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS role VARCHAR(20) DEFAULT \'user\''
      },
      {
        name: 'status', 
        sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT \'active\''
      },
      {
        name: 'last_login',
        sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP'
      },
      {
        name: 'login_count',
        sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS login_count INTEGER DEFAULT 0'
      },
      {
        name: 'preferences',
        sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS preferences TEXT'
      }
    ];
    
    for (const migration of usersMigrations) {
      try {
        await pgPool.query(migration.sql);
        console.log(`✅ Added ${migration.name} column to users table`);
      } catch (error) {
        if (error.code === '42701') { // Column already exists
          console.log(`ℹ️ Column ${migration.name} already exists`);
        } else {
          console.error(`❌ Failed to add ${migration.name}:`, error.message);
        }
      }
    }
    
    // 2. Fix sync_history table
    console.log('\n2. 📜 Fixing sync_history table...');
    
    const historyMigrations = [
      {
        name: 'bytes_transferred',
        sql: 'ALTER TABLE sync_history ADD COLUMN IF NOT EXISTS bytes_transferred INTEGER DEFAULT 0'
      },
      {
        name: 'user_id_nullable',
        sql: 'ALTER TABLE sync_history ALTER COLUMN user_id DROP NOT NULL'
      }
    ];
    
    for (const migration of historyMigrations) {
      try {
        await pgPool.query(migration.sql);
        console.log(`✅ Applied ${migration.name} to sync_history table`);
      } catch (error) {
        if (error.code === '42701') { // Column already exists
          console.log(`ℹ️ Migration ${migration.name} already applied`);
        } else {
          console.error(`❌ Failed to apply ${migration.name}:`, error.message);
        }
      }
    }
    
    // 3. Verify schema fixes
    console.log('\n3. ✅ Verifying schema fixes...');
    
    // Check users table
    const usersSchema = await pgPool.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      ORDER BY ordinal_position
    `);
    
    console.log('📊 Updated users table schema:');
    usersSchema.rows.forEach(col => {
      console.log(`   ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
    });
    
    // Check sync_history table
    const historySchema = await pgPool.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'sync_history' 
      ORDER BY ordinal_position
    `);
    
    console.log('\n📊 Updated sync_history table schema:');
    historySchema.rows.forEach(col => {
      console.log(`   ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
    });
    
    console.log('\n🎉 PostgreSQL schema fixes completed!');
    console.log('💡 You can now run database sync again');
    
  } catch (error) {
    console.error('❌ Schema fix failed:', error);
  } finally {
    await pgPool.end();
    console.log('🔌 PostgreSQL connection closed');
  }
}

// Export for manual use
module.exports = { fixPostgreSQLSchema };

// Auto-run if called directly
if (require.main === module) {
  fixPostgreSQLSchema();
}

console.log('🔧 PostgreSQL Schema Fixer loaded!');
console.log('📝 Run: node scripts/fix-pg-schema.js');
