const axios = require('axios');

async function testProfessionalClientManagement() {
  console.log('🧪 Testing Professional Client Management System\n');

  const servers = [
    { name: 'Desktop Server', url: 'http://localhost:5002' },
    { name: 'Web Server', url: 'http://localhost:5001' }
  ];

  let token = null;

  try {
    // Test 1: Login and get token
    console.log('🔐 TEST 1: Authentication');
    console.log('=' .repeat(50));

    for (const server of servers) {
      try {
        const loginResponse = await axios.post(`${server.url}/api/auth/login`, {
          email: '<EMAIL>',
          password: 'password123'
        }, { timeout: 5000 });

        token = loginResponse.data.token;
        console.log(`✅ ${server.name}: Login successful`);
        break;
      } catch (error) {
        console.log(`❌ ${server.name}: ${error.code === 'ECONNREFUSED' ? 'Not running' : error.message}`);
      }
    }

    if (!token) {
      console.log('❌ No servers available for testing');
      return;
    }

    // Test 2: Check client management endpoint
    console.log('\n📊 TEST 2: Client Management API');
    console.log('=' .repeat(50));

    const webServerUrl = 'http://localhost:5001';

    try {
      const clientsResponse = await axios.get(`${webServerUrl}/api/clients`, {
        headers: { Authorization: `Bearer ${token}` },
        timeout: 10000
      });

      const data = clientsResponse.data;
      console.log(`✅ Clients API Response:`);
      console.log(`   📋 Total clients: ${data.total}`);
      console.log(`   📊 Source: ${data.source || 'unknown'}`);
      
      if (data.stats) {
        console.log(`   📈 Stats: ${data.stats.online} online, ${data.stats.offline} offline`);
      }

      if (data.clients && data.clients.length > 0) {
        console.log(`\n📋 CLIENT DETAILS:`);
        data.clients.forEach((client, index) => {
          console.log(`   ${index + 1}. ${client.client_id}`);
          console.log(`      🏠 Hostname: ${client.hostname}`);
          console.log(`      📊 Status: ${client.status === 'online' ? '🟢 ONLINE' : '🔴 OFFLINE'}`);
          console.log(`      👁️ Last Seen: ${client.last_seen}`);
          console.log(`      📋 Tasks: ${client.total_tasks || 0} total, ${client.active_tasks || 0} active`);
          
          if (client.in_memory_status) {
            console.log(`      🧠 Memory Status: ${client.in_memory_status}`);
          }
          
          if (client.is_valid !== undefined) {
            console.log(`      ✅ Valid: ${client.is_valid}`);
          }
          console.log('');
        });
      } else {
        console.log('   📭 No clients found');
        console.log('   💡 Login to desktop app first to create client records');
      }

    } catch (error) {
      console.log(`❌ Clients API Error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }

    // Test 3: Test client state consistency
    console.log('\n🔍 TEST 3: Client State Consistency');
    console.log('=' .repeat(50));

    try {
      // Check multiple times to see if data disappears
      const checks = [];
      
      for (let i = 0; i < 5; i++) {
        const response = await axios.get(`${webServerUrl}/api/clients`, {
          headers: { Authorization: `Bearer ${token}` },
          timeout: 5000
        });
        
        checks.push({
          attempt: i + 1,
          total: response.data.total,
          source: response.data.source,
          timestamp: new Date().toISOString()
        });
        
        // Wait 2 seconds between checks
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      console.log('📊 CONSISTENCY CHECK RESULTS:');
      checks.forEach(check => {
        console.log(`   ${check.attempt}. Total: ${check.total}, Source: ${check.source}, Time: ${check.timestamp.split('T')[1].split('.')[0]}`);
      });

      // Check for consistency
      const totals = checks.map(c => c.total);
      const isConsistent = totals.every(total => total === totals[0]);
      
      if (isConsistent) {
        console.log('   ✅ Data is CONSISTENT across multiple checks');
      } else {
        console.log('   ❌ Data is INCONSISTENT - clients disappearing/appearing');
        console.log('   🔧 This indicates a problem with the client management logic');
      }

    } catch (error) {
      console.log(`❌ Consistency check failed: ${error.message}`);
    }

    // Test 4: Database sync status
    console.log('\n🔄 TEST 4: Database Sync Status');
    console.log('=' .repeat(50));

    try {
      const syncResponse = await axios.get(`${webServerUrl}/api/database-sync/status`, {
        headers: { Authorization: `Bearer ${token}` },
        timeout: 5000
      });

      const syncData = syncResponse.data;
      console.log(`✅ Database Sync Status:`);
      console.log(`   🔄 Enabled: ${syncData.enabled}`);
      
      if (syncData.databaseSync) {
        console.log(`   📊 Running: ${syncData.databaseSync.isRunning}`);
        console.log(`   ⏰ Last Sync: ${syncData.databaseSync.lastSyncTime || 'Never'}`);
        console.log(`   🎯 Direction: ${syncData.databaseSync.config?.syncDirection}`);
      }
      
      if (syncData.realTimeSync) {
        console.log(`   ⚡ Real-time: ${syncData.realTimeSync.enabled ? 'Enabled' : 'Disabled'}`);
        console.log(`   📋 Pending: ${syncData.realTimeSync.pendingChanges?.length || 0} changes`);
      }

    } catch (error) {
      console.log(`❌ Database sync status error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }

    // Test 5: Manual sync trigger
    console.log('\n🔄 TEST 5: Manual Database Sync');
    console.log('=' .repeat(50));

    try {
      const syncTriggerResponse = await axios.post(`${webServerUrl}/api/database-sync/trigger`, {
        tables: ['desktop_clients']
      }, {
        headers: { Authorization: `Bearer ${token}` },
        timeout: 15000
      });

      console.log(`✅ Manual sync completed:`);
      console.log(`   ⏱️ Duration: ${syncTriggerResponse.data.result?.duration}ms`);
      console.log(`   🎯 Direction: ${syncTriggerResponse.data.result?.direction}`);
      
      if (syncTriggerResponse.data.result?.tables) {
        Object.entries(syncTriggerResponse.data.result.tables).forEach(([table, stats]) => {
          console.log(`   📋 ${table}: ${JSON.stringify(stats)}`);
        });
      }

    } catch (error) {
      console.log(`❌ Manual sync error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }

    // Test Summary
    console.log('\n📋 TEST SUMMARY:');
    console.log('=' .repeat(50));
    console.log('✅ Professional client management system tested');
    console.log('');
    console.log('🔧 EXPECTED BEHAVIOR:');
    console.log('1. 🔐 Login → Client registration → Status = online');
    console.log('2. 🚪 Logout → Client status = offline');
    console.log('3. 🔐 Login again → Client status = online (same client_id)');
    console.log('4. 📊 Client data should be CONSISTENT and NOT disappear');
    console.log('5. 🔄 Real-time sync should keep web-ui updated');
    console.log('');
    console.log('🐛 IF CLIENTS STILL DISAPPEAR:');
    console.log('1. Check ClientStateManager initialization');
    console.log('2. Check database sync conflicts');
    console.log('3. Check real-time sync infinite loops');
    console.log('4. Check web-ui data fetching logic');
    console.log('5. Monitor server logs for errors');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run test
testProfessionalClientManagement();
