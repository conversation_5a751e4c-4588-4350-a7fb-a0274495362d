// Project cleanup script
const fs = require('fs');
const path = require('path');

console.log('🧹 SyncMasterPro Project Cleanup\n');

// Files and directories to remove
const itemsToRemove = {
  // Test directories
  testDirectories: [
    'debug-today',
    'test-today-only'
  ],
  
  // Standalone test files
  testFiles: [
    'electron-startup-test.js',
    'startup-test-console.js',
    'test-settings.md',
    'test-startup-behaviors.md'
  ],
  
  // Old/unused scripts (keep essential ones)
  oldScripts: [
    'scripts/debug-electron-api.html',
    'scripts/debug-realtime-history.js',
    'scripts/debug-realtime-now.js',
    'scripts/debug-task-data.js',
    'scripts/debug-today-only.js',
    'scripts/deep-debug-history.js',
    'scripts/demo-realtime-workflow.js',
    'scripts/fix-admin-routes.js',
    'scripts/fix-analytics-schema.js',
    'scripts/fix-realtime-history.js',
    'scripts/history-features-summary.md',
    'scripts/manual-history-test.md',
    'scripts/open-history-test.js',
    'scripts/quick-realtime-test.js',
    'scripts/restart-and-test.js',
    'scripts/simple-sync.js',
    'scripts/test-admin-login.js',
    'scripts/test-auth.js',
    'scripts/test-create-task-debug.js',
    'scripts/test-create-task-ui.js',
    'scripts/test-database-separation.js',
    'scripts/test-database-writing.js',
    'scripts/test-delete-fixed.js',
    'scripts/test-delete-history.js',
    'scripts/test-delete-task.js',
    'scripts/test-desktop-fix.js',
    'scripts/test-desktop-realtime.js',
    'scripts/test-duplicate-fix.js',
    'scripts/test-edit-options.js',
    'scripts/test-electron-detection.js',
    'scripts/test-fixes.js',
    'scripts/test-folder-picker.js',
    'scripts/test-history-api.js',
    'scripts/test-history-browser.js',
    'scripts/test-history-features.js',
    'scripts/test-history-fix.js',
    'scripts/test-history-table-backend.js',
    'scripts/test-history-table.js',
    'scripts/test-hoisting-fix.js',
    'scripts/test-hybrid-folder-picker.js',
    'scripts/test-integration.js',
    'scripts/test-login-simple.js',
    'scripts/test-native-folder-picker.js',
    'scripts/test-new-features.js',
    'scripts/test-phase3-apis.js',
    'scripts/test-phase3-complete.js',
    'scripts/test-phase4-complete.js',
    'scripts/test-postgresql-connection.js',
    'scripts/test-postgresql-separate.js',
    'scripts/test-real-sync.js',
    'scripts/test-realtime-simple.js',
    'scripts/test-realtime-sync.js',
    'scripts/test-realtime-toggle.js',
    'scripts/test-realtime-updates.js',
    'scripts/test-realtime.js',
    'scripts/test-socket-fix.js',
    'scripts/test-sync-api.js',
    'scripts/test-sync-data-update.js',
    'scripts/test-sync-engine.js',
    'scripts/test-sync-tasks.js',
    'scripts/test-today-only-sync.js',
    'scripts/test-toggle-conversion.js',
    'scripts/test-web-create-task.html',
    'scripts/verify-architecture.js',
    'scripts/verify-fixes.js'
  ],
  
  // Keep essential scripts
  keepScripts: [
    'scripts/README.md',
    'scripts/check-database-info.js',
    'scripts/check-users.js',
    'scripts/create-admin-users.js',
    'scripts/create-pg-admin.js',
    'scripts/create-sqlite-admin.js',
    'scripts/create-tables.js',
    'scripts/reset-admin-password.js',
    'scripts/setup.js',
    'scripts/cleanup-project.js' // This script
  ]
};

// Function to safely remove file/directory
function safeRemove(itemPath) {
  try {
    if (fs.existsSync(itemPath)) {
      const stats = fs.statSync(itemPath);
      
      if (stats.isDirectory()) {
        // Remove directory recursively
        fs.rmSync(itemPath, { recursive: true, force: true });
        console.log(`✅ Removed directory: ${itemPath}`);
      } else {
        // Remove file
        fs.unlinkSync(itemPath);
        console.log(`✅ Removed file: ${itemPath}`);
      }
      
      return true;
    } else {
      console.log(`⚠️ Not found: ${itemPath}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Failed to remove ${itemPath}: ${error.message}`);
    return false;
  }
}

// Function to get directory size
function getDirectorySize(dirPath) {
  if (!fs.existsSync(dirPath)) return 0;
  
  let totalSize = 0;
  
  function calculateSize(currentPath) {
    const stats = fs.statSync(currentPath);
    
    if (stats.isDirectory()) {
      const files = fs.readdirSync(currentPath);
      files.forEach(file => {
        calculateSize(path.join(currentPath, file));
      });
    } else {
      totalSize += stats.size;
    }
  }
  
  try {
    calculateSize(dirPath);
  } catch (error) {
    // Ignore errors
  }
  
  return totalSize;
}

// Function to format bytes
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Main cleanup function
function cleanup() {
  let totalRemoved = 0;
  let totalSize = 0;
  
  console.log('1. 📁 Removing test directories...');
  itemsToRemove.testDirectories.forEach(dir => {
    const size = getDirectorySize(dir);
    if (safeRemove(dir)) {
      totalRemoved++;
      totalSize += size;
    }
  });
  
  console.log('\n2. 📄 Removing test files...');
  itemsToRemove.testFiles.forEach(file => {
    const size = fs.existsSync(file) ? fs.statSync(file).size : 0;
    if (safeRemove(file)) {
      totalRemoved++;
      totalSize += size;
    }
  });
  
  console.log('\n3. 🗂️ Removing old scripts...');
  itemsToRemove.oldScripts.forEach(script => {
    const size = fs.existsSync(script) ? fs.statSync(script).size : 0;
    if (safeRemove(script)) {
      totalRemoved++;
      totalSize += size;
    }
  });
  
  console.log('\n📊 CLEANUP SUMMARY:');
  console.log(`   Files/directories removed: ${totalRemoved}`);
  console.log(`   Space freed: ${formatBytes(totalSize)}`);
  
  console.log('\n✅ KEPT ESSENTIAL FILES:');
  itemsToRemove.keepScripts.forEach(script => {
    if (fs.existsSync(script)) {
      console.log(`   ✅ ${script}`);
    }
  });
  
  console.log('\n🎯 PROJECT STRUCTURE AFTER CLEANUP:');
  console.log('   📁 src/ - Frontend React code');
  console.log('   📁 server/ - Backend Node.js code');
  console.log('   📁 electron/ - Electron desktop app');
  console.log('   📁 public/ - Static assets');
  console.log('   📁 scripts/ - Essential utility scripts');
  console.log('   📁 docs/ - Documentation');
  console.log('   📁 data/ - Database files');
  console.log('   📄 package.json - Dependencies');
  console.log('   📄 README.md - Main documentation');
  
  console.log('\n🎉 Project cleanup completed!');
  console.log('💡 Your project is now cleaner and more organized.');
}

// Run cleanup
cleanup();
