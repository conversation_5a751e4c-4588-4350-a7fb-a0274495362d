import React, { createContext, useContext, useEffect, useState } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from './AuthContext';
import { useNotification } from './NotificationContext';

const SocketContext = createContext();

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export const SocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [syncProgress, setSyncProgress] = useState({});
  const { user, token } = useAuth();
  const { addNotification } = useNotification();

  useEffect(() => {
    if (token && user) {
      console.log('🔌 Connecting to Socket.IO server...');
      
      // Auto-detect environment for Socket.IO URL
      // Check multiple ways to detect Electron environment
      const isElectron = window.electronAPI !== undefined ||
                         window.platform !== undefined ||
                         navigator.userAgent.toLowerCase().indexOf('electron') > -1 ||
                         (window.process && window.process.versions && window.process.versions.electron);

      // Socket.IO URL (remove /api suffix if present)
      let socketURL;
      if (isElectron) {
        socketURL = 'http://localhost:5002';  // Desktop uses port 5002
      } else {
        // Web uses port 5000, but remove /api suffix for Socket.IO
        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
        socketURL = apiUrl.replace('/api', ''); // Remove /api for Socket.IO
      }

      console.log(`🔌 Connecting to Socket.IO server: ${socketURL} (${isElectron ? 'Desktop' : 'Web'})`);

      // Create socket connection
      const newSocket = io(socketURL, {
        auth: {
          token: token
        },
        transports: ['polling', 'websocket'], // Try polling first
        forceNew: true,
        reconnection: true,
        timeout: 10000,
        autoConnect: true
      });

      // Connection events
      newSocket.on('connect', () => {
        console.log('✅ Socket.IO connected:', newSocket.id);
        setIsConnected(true);
        
        // Join user room for personalized updates
        newSocket.emit('join-user-room', user.id);
      });

      newSocket.on('disconnect', () => {
        console.log('❌ Socket.IO disconnected');
        setIsConnected(false);
      });

      newSocket.on('connect_error', (error) => {
        console.error('❌ Socket.IO connection error:', error);
        console.error('Error details:', {
          message: error.message,
          type: error.type,
          description: error.description
        });
        setIsConnected(false);

        // Try to reconnect after a delay
        setTimeout(() => {
          if (!newSocket.connected) {
            console.log('🔄 Attempting to reconnect...');
            newSocket.connect();
          }
        }, 3000);
      });

      // Sync events
      newSocket.on('sync-started', (data) => {
        console.log('🔄 Sync started:', data);
        setSyncProgress(prev => ({
          ...prev,
          [data.taskId]: {
            status: 'running',
            progress: 0,
            message: 'Starting sync...',
            timestamp: data.timestamp
          }
        }));
        
        addNotification(`Sync started: ${data.taskName}`, 'info');
      });

      newSocket.on('sync-progress', (data) => {
        console.log('📊 Sync progress:', data);
        setSyncProgress(prev => ({
          ...prev,
          [data.taskId]: {
            status: data.status,
            progress: data.progress || 0,
            message: data.message || `Processing ${data.currentFile}`,
            filesProcessed: data.filesProcessed,
            filesTotal: data.filesTotal,
            currentFile: data.currentFile,
            bytesTransferred: data.bytesTransferred,
            timestamp: data.timestamp
          }
        }));
      });

      newSocket.on('sync-completed', (data) => {
        console.log('✅ Sync completed:', data);
        setSyncProgress(prev => ({
          ...prev,
          [data.taskId]: {
            status: 'completed',
            progress: 100,
            message: 'Sync completed successfully',
            stats: data.stats,
            timestamp: data.timestamp
          }
        }));
        
        addNotification(`Sync completed: ${data.taskName}`, 'success');
        
        // Show desktop notification if supported
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('SyncMasterPro', {
            body: `Sync completed: ${data.taskName}`,
            icon: '/favicon.ico'
          });
        }
        
        // Clear progress after 5 seconds
        setTimeout(() => {
          setSyncProgress(prev => {
            const newProgress = { ...prev };
            delete newProgress[data.taskId];
            return newProgress;
          });
        }, 5000);
      });

      newSocket.on('sync-error', (data) => {
        console.error('❌ Sync error:', data);
        setSyncProgress(prev => ({
          ...prev,
          [data.taskId]: {
            status: 'error',
            progress: 0,
            message: `Error: ${data.error}`,
            error: data.error,
            timestamp: data.timestamp
          }
        }));
        
        addNotification(`Sync error: ${data.taskName} - ${data.error}`, 'error');
        
        // Show desktop notification for errors
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('SyncMasterPro - Error', {
            body: `Sync failed: ${data.taskName}`,
            icon: '/favicon.ico'
          });
        }
        
        // Clear error after 10 seconds
        setTimeout(() => {
          setSyncProgress(prev => {
            const newProgress = { ...prev };
            delete newProgress[data.taskId];
            return newProgress;
          });
        }, 10000);
      });

      setSocket(newSocket);

      // Cleanup on unmount
      return () => {
        console.log('🔌 Disconnecting Socket.IO...');
        newSocket.disconnect();
      };
    }
  }, [token, user?.id]); // Only depend on token and user.id, not the whole user object

  // Request notification permission on mount
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        console.log('🔔 Notification permission:', permission);
      });
    }
  }, []);

  const value = {
    socket,
    isConnected,
    syncProgress,
    
    // Helper methods
    getSyncProgress: (taskId) => syncProgress[taskId] || null,
    clearSyncProgress: (taskId) => {
      setSyncProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[taskId];
        return newProgress;
      });
    },
    
    // Emit events
    emitSyncStart: (taskId, userId) => {
      if (socket) {
        socket.emit('sync-start', { taskId, userId });
      }
    },
    
    emitSyncProgress: (data) => {
      if (socket) {
        socket.emit('sync-progress', data);
      }
    },
    
    emitSyncComplete: (data) => {
      if (socket) {
        socket.emit('sync-complete', data);
      }
    },
    
    emitSyncError: (data) => {
      if (socket) {
        socket.emit('sync-error', data);
      }
    }
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
