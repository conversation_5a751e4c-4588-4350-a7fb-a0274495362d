const express = require('express');
const router = express.Router();
const FileVersioning = require('../services/FileVersioning');
const authenticateToken = require('../middleware/auth');

const fileVersioning = new FileVersioning();

// Get all versions for a file
router.get('/tasks/:taskId/files/:filePath/versions', authenticateToken, async (req, res) => {
  try {
    const { taskId, filePath } = req.params;
    const decodedFilePath = decodeURIComponent(filePath);
    
    const versions = await fileVersioning.getFileVersions(taskId, decodedFilePath);
    
    res.json({
      success: true,
      versions,
      count: versions.length
    });
  } catch (error) {
    console.error('Failed to get file versions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get file versions'
    });
  }
});

// Get version statistics for a task
router.get('/tasks/:taskId/stats', authenticateToken, async (req, res) => {
  try {
    const { taskId } = req.params;
    
    const stats = await fileVersioning.getVersionStats(taskId);
    
    res.json({
      success: true,
      stats: {
        totalVersions: parseInt(stats.total_versions) || 0,
        uniqueFiles: parseInt(stats.unique_files) || 0,
        totalSize: parseInt(stats.total_size) || 0,
        latestVersion: stats.latest_version,
        totalSizeMB: ((parseInt(stats.total_size) || 0) / (1024 * 1024)).toFixed(2)
      }
    });
  } catch (error) {
    console.error('Failed to get version stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get version stats'
    });
  }
});

// Restore a specific version
router.post('/versions/:versionId/restore', authenticateToken, async (req, res) => {
  try {
    const { versionId } = req.params;
    const { targetPath } = req.body;
    
    if (!targetPath) {
      return res.status(400).json({
        success: false,
        error: 'Target path is required'
      });
    }
    
    const success = await fileVersioning.restoreVersion(versionId, targetPath);
    
    if (success) {
      res.json({
        success: true,
        message: 'Version restored successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to restore version'
      });
    }
  } catch (error) {
    console.error('Failed to restore version:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to restore version'
    });
  }
});

// Create a manual version (backup)
router.post('/tasks/:taskId/files/:filePath/backup', authenticateToken, async (req, res) => {
  try {
    const { taskId, filePath } = req.params;
    const { reason = 'manual_backup' } = req.body;
    const decodedFilePath = decodeURIComponent(filePath);
    
    // Note: This would need the actual file path on disk
    // For now, we'll return a placeholder response
    res.json({
      success: true,
      message: 'Manual backup feature requires file path implementation',
      taskId,
      filePath: decodedFilePath,
      reason
    });
  } catch (error) {
    console.error('Failed to create manual backup:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create manual backup'
    });
  }
});

// Delete old versions for a task
router.delete('/tasks/:taskId/versions/cleanup', authenticateToken, async (req, res) => {
  try {
    const { taskId } = req.params;
    
    const success = await fileVersioning.deleteTaskVersions(taskId);
    
    if (success) {
      res.json({
        success: true,
        message: 'Task versions cleaned up successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to cleanup task versions'
      });
    }
  } catch (error) {
    console.error('Failed to cleanup versions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cleanup versions'
    });
  }
});

// Get version details
router.get('/versions/:versionId', authenticateToken, async (req, res) => {
  try {
    const { versionId } = req.params;
    const { getDatabase } = require('../database/init');
    const db = getDatabase();
    
    const result = await db.query(
      'SELECT * FROM file_versions WHERE id = ?',
      [versionId]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Version not found'
      });
    }
    
    const version = result.rows[0];
    
    res.json({
      success: true,
      version: {
        id: version.id,
        taskId: version.task_id,
        filePath: version.file_path,
        versionPath: version.version_path,
        fileHash: version.file_hash,
        fileSize: version.file_size,
        reason: version.reason,
        createdAt: version.created_at,
        fileSizeMB: (version.file_size / (1024 * 1024)).toFixed(2)
      }
    });
  } catch (error) {
    console.error('Failed to get version details:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get version details'
    });
  }
});

module.exports = router;
