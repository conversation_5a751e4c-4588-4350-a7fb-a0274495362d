const { getDatabase } = require('../server/database/init');

async function fixMigrationErrors() {
  console.log('🔧 Fixing Database Migration Errors\n');

  try {
    // Force SQLite mode
    process.env.DB_TYPE = 'sqlite';
    process.env.ELECTRON_ENV = 'true';

    // Override database init to force SQLite
    const originalEnv = process.env.DB_TYPE;
    
    const { initializeDatabase } = require('../server/database/init');
    await initializeDatabase();
    
    const db = getDatabase();
    
    console.log('1. 📋 Checking existing table schemas...');
    
    // Check existing columns in each table
    const tables = ['users', 'sync_tasks', 'sync_history', 'sessions'];
    
    for (const table of tables) {
      try {
        const result = await db.query(`PRAGMA table_info(${table})`);
        const columns = result.map(row => row.name);
        
        console.log(`   📊 Table ${table}:`);
        console.log(`      Columns: ${columns.join(', ')}`);
        
        // Check for problematic columns
        const problematicColumns = [
          'bytes_transferred', 'role', 'status', 'last_login', 
          'login_count', 'preferences', 'settings', 'client_id',
          'ip_address', 'user_agent', 'last_used'
        ];
        
        const existingProblematic = columns.filter(col => problematicColumns.includes(col));
        if (existingProblematic.length > 0) {
          console.log(`      ✅ Already has: ${existingProblematic.join(', ')}`);
        }
        
      } catch (error) {
        console.log(`   ❌ Error checking ${table}: ${error.message}`);
      }
    }

    console.log('\n2. 🔧 Creating migration tracking table...');
    
    // Create migration tracking table to prevent duplicate migrations
    try {
      await db.query(`
        CREATE TABLE IF NOT EXISTS schema_migrations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          migration_name VARCHAR(255) UNIQUE NOT NULL,
          applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('   ✅ Migration tracking table created');
    } catch (error) {
      console.log(`   ℹ️ Migration table already exists: ${error.message}`);
    }

    console.log('\n3. 📝 Recording existing migrations...');
    
    // Record existing migrations to prevent re-running
    const migrations = [
      'add_bytes_transferred_to_sync_history',
      'add_user_fields',
      'add_client_id_to_sync_tasks', 
      'add_session_fields'
    ];
    
    for (const migration of migrations) {
      try {
        await db.query(`
          INSERT OR IGNORE INTO schema_migrations (migration_name) 
          VALUES (?)
        `, [migration]);
        console.log(`   ✅ Recorded migration: ${migration}`);
      } catch (error) {
        console.log(`   ⚠️ Failed to record ${migration}: ${error.message}`);
      }
    }

    console.log('\n4. 🧹 Cleaning up duplicate session tokens...');
    
    // Clean up duplicate session tokens
    try {
      const duplicates = await db.query(`
        SELECT token, COUNT(*) as count 
        FROM sessions 
        GROUP BY token 
        HAVING COUNT(*) > 1
      `);
      
      if (duplicates.length > 0) {
        console.log(`   🔍 Found ${duplicates.length} duplicate tokens`);
        
        for (const dup of duplicates) {
          // Keep only the latest session for each duplicate token
          await db.query(`
            DELETE FROM sessions 
            WHERE token = ? AND id NOT IN (
              SELECT MAX(id) FROM sessions WHERE token = ?
            )
          `, [dup.token, dup.token]);
          
          console.log(`   🗑️ Cleaned up duplicates for token: ${dup.token.substring(0, 20)}...`);
        }
      } else {
        console.log('   ✅ No duplicate tokens found');
      }
    } catch (error) {
      console.log(`   ⚠️ Error cleaning duplicates: ${error.message}`);
    }

    console.log('\n5. 🔍 Verifying database integrity...');
    
    // Run integrity check
    try {
      const integrity = await db.query('PRAGMA integrity_check');
      if (integrity[0].integrity_check === 'ok') {
        console.log('   ✅ Database integrity: OK');
      } else {
        console.log('   ⚠️ Database integrity issues found');
        console.log('   📋 Issues:', integrity);
      }
    } catch (error) {
      console.log(`   ⚠️ Integrity check failed: ${error.message}`);
    }

    console.log('\n🎉 Migration Error Fix Completed!');
    
    console.log('\n📋 SUMMARY:');
    console.log('   ✅ Checked existing table schemas');
    console.log('   ✅ Created migration tracking table');
    console.log('   ✅ Recorded existing migrations');
    console.log('   ✅ Cleaned up duplicate session tokens');
    console.log('   ✅ Verified database integrity');
    
    console.log('\n💡 NEXT STEPS:');
    console.log('   1. Restart desktop server to see if migration errors are gone');
    console.log('   2. Check web server is running on port 5001');
    console.log('   3. Test client-server connection');

  } catch (error) {
    console.error('❌ Fix failed:', error);
    console.error('Stack:', error.stack);
  }
}

// Run fix
fixMigrationErrors();
