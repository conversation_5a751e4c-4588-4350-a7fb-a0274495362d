# SyncMasterPro - Quick Start Guide

## 🚀 Get Started in 5 Minutes

### Step 1: Install Dependencies
```bash
npm install
```

### Step 2: Setup Application
```bash
npm run setup
```

### Step 3: Start Application
```bash
npm start
```

That's it! SyncMasterPro will open automatically.

## 📱 Alternative Start Methods

### Windows Users
Double-click `start.bat` file

### Mac/Linux Users
```bash
chmod +x start.sh
./start.sh
```

### Manual Start (Development)
```bash
# Terminal 1: Backend server
npm run server

# Terminal 2: React app
npm run dev

# Terminal 3: Electron app
npm run electron-dev
```

## 🎯 First Steps

### 1. Create Account
- Open the app
- Click "Create Account" 
- Fill in your details
- Or use "Offline Mode" for local sync only

### 2. Create Your First Sync Task
- Click "New Sync Task"
- Choose source folder (where files are)
- Choose destination folder (where to sync)
- Select sync type:
  - **Bidirectional**: Both folders stay in sync
  - **One-way**: Source → Destination only
  - **Mirror**: Exact copy with deletions
- Click "Create Task"

### 3. Start Syncing
- Click "Start" on your sync task
- Watch real-time progress
- Files will sync automatically

## ⚙️ Quick Configuration

### Environment Setup
Edit `.env` file for custom settings:
```env
# Change ports if needed
PORT=5000
REACT_APP_API_URL=http://localhost:5000/api

# Database type (sqlite for desktop, postgresql for web)
DB_TYPE=sqlite

# Security
JWT_SECRET=your-secret-key-here
```

### Common Settings
- **Real-time sync**: Files sync as you change them
- **Scheduled sync**: Set specific times to sync
- **Filters**: Exclude certain file types or folders
- **Conflict resolution**: Choose how to handle file conflicts

## 🔧 Troubleshooting

### App Won't Start?
1. Check Node.js version: `node --version` (need 16+)
2. Clear cache: `npm cache clean --force`
3. Reinstall: `rm -rf node_modules && npm install`

### Sync Not Working?
1. Check folder permissions
2. Verify paths exist
3. Check available disk space
4. Look at logs in `logs/` folder

### Performance Issues?
1. Exclude large files/folders
2. Use selective sync
3. Adjust file watch settings
4. Check system resources

## 📚 Key Features

### ✅ What You Can Do
- **Real-time sync** between any folders
- **Bidirectional sync** keeps both sides updated
- **Conflict resolution** handles file conflicts
- **Scheduling** for automatic sync
- **History tracking** of all sync activities
- **Web interface** for remote management
- **Offline mode** works without internet

### 🎛️ Sync Types
- **Bidirectional**: Changes sync both ways
- **One-way**: Source → Destination only  
- **Mirror**: Exact copy, deletes extra files
- **Backup**: Incremental backup with versions

### 🔍 Filters & Exclusions
- File types: `*.tmp`, `*.log`
- Folders: `node_modules/`, `.git/`
- Size limits: Skip files over X MB
- Date filters: Only recent files

## 🌐 Web Interface

Access from any browser at `http://localhost:3000`

### Features
- Remote sync management
- Real-time status monitoring
- History and statistics
- User account management
- Mobile-friendly interface

## 📊 Monitoring

### Dashboard
- Active sync tasks
- Real-time progress
- Recent activity
- System status

### History
- Complete sync logs
- Success/failure rates
- File counts and sizes
- Error details

### Notifications
- Desktop notifications
- Email alerts (coming soon)
- System tray status
- Sound notifications

## 🔒 Security

### Local Security
- Encrypted local database
- Secure file permissions
- User authentication
- Session management

### Network Security
- HTTPS support
- JWT tokens
- API rate limiting
- Input validation

## 📁 File Organization

```
SyncMasterPro/
├── data/           # Database and user data
├── logs/           # Application logs
├── uploads/        # Temporary file uploads
├── temp/           # Temporary processing files
├── src/            # React frontend source
├── server/         # Node.js backend
├── electron/       # Electron main process
└── build/          # Production build
```

## 🚀 Advanced Usage

### Command Line
```bash
# Build for production
npm run build

# Create installer
npm run dist

# Run tests
npm test

# Debug mode
DEBUG=syncmasterpro:* npm start
```

### API Access
```javascript
// Example API call
fetch('http://localhost:5000/api/sync/tasks', {
  headers: {
    'Authorization': 'Bearer your-token-here'
  }
})
```

### Plugins (Coming Soon)
- Cloud storage integration
- Custom sync algorithms
- Third-party notifications
- Advanced filtering

## 📞 Getting Help

### Documentation
- Full documentation: `README.md`
- Installation guide: `INSTALL.md`
- API reference: `/docs` (coming soon)

### Support
- GitHub Issues: Report bugs and feature requests
- Email: <EMAIL>
- Community: Discord/Slack (coming soon)

### Logs
Check these locations for troubleshooting:
- **Windows**: `%APPDATA%/SyncMasterPro/logs/`
- **macOS**: `~/Library/Application Support/SyncMasterPro/logs/`
- **Linux**: `~/.config/SyncMasterPro/logs/`
- **Development**: `./logs/`

## 🎉 What's Next?

### Immediate
1. Set up your important sync tasks
2. Configure notifications
3. Test conflict resolution
4. Set up scheduling

### Advanced
1. Explore web interface
2. Set up remote access
3. Configure advanced filters
4. Monitor performance

### Coming Soon
- Mobile app for monitoring
- Cloud storage integration
- Team collaboration features
- Enterprise features

---

**Need more help?** Check the full [README.md](README.md) or [Installation Guide](INSTALL.md)
