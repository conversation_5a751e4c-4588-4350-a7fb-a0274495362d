const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

async function createPostgreSQLAdmin() {
  console.log('🐘 Creating Admin User in PostgreSQL...');

  const pool = new Pool({
    user: 'pi',
    host: '*************',
    database: 'syncmasterpro',
    password: 'ubuntu',
    port: 5432,
  });

  try {
    const client = await pool.connect();
    console.log('✅ Connected to PostgreSQL');

    // Check if admin user exists
    const existingUser = await client.query(
      'SELECT id, email FROM users WHERE email = $1',
      ['<EMAIL>']
    );

    if (existingUser.rows.length > 0) {
      console.log('ℹ️ Admin user already exists:', existingUser.rows[0]);
    } else {
      // Hash password
      const hashedPassword = await bcrypt.hash('admin123', 12);
      
      // Create admin user
      const result = await client.query(
        `INSERT INTO users (email, password, name, avatar, settings, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
         RETURNING id, email, name, created_at`,
        [
          '<EMAIL>',
          hashedPassword,
          'Admin User',
          null,
          '{}'
        ]
      );

      console.log('✅ Admin user created successfully:');
      console.log('📊 User ID:', result.rows[0].id);
      console.log('📧 Email:', result.rows[0].email);
      console.log('👤 Name:', result.rows[0].name);
      console.log('📅 Created:', result.rows[0].created_at);
    }

    // Verify user exists
    const verifyUser = await client.query(
      'SELECT id, email, name, created_at FROM users WHERE email = $1',
      ['<EMAIL>']
    );

    if (verifyUser.rows.length > 0) {
      console.log('\n✅ Verification successful:');
      console.log('📊 User found:', verifyUser.rows[0]);
    } else {
      console.log('\n❌ Verification failed: User not found');
    }

    client.release();
    await pool.end();

    console.log('\n🎉 PostgreSQL admin setup complete!');
    console.log('📋 Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');

  } catch (error) {
    console.log('\n❌ PostgreSQL error:');
    console.log('Error type:', error.constructor.name);
    console.log('Error message:', error.message);
    console.log('Error code:', error.code);
    await pool.end();
  }
}

createPostgreSQLAdmin();
