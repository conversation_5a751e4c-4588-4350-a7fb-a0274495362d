#!/usr/bin/env node

/**
 * Check database synchronization between SQLite (desktop) and PostgreSQL (web)
 */

const Database = require('better-sqlite3');
const { Pool } = require('pg');
const path = require('path');

console.log('🔍 Checking Database Synchronization');
console.log('====================================');

async function checkDatabaseSync() {
  console.log('\n1. 📊 Connecting to databases...');
  
  // Connect to SQLite (Desktop)
  const sqlitePath = path.join(__dirname, '../data/syncmasterpro.db');
  const sqlite = new Database(sqlitePath);
  console.log('✅ SQLite connected:', sqlitePath);
  
  // Connect to PostgreSQL (Web)
  const pgPool = new Pool({
    host: '*************',
    port: 5432,
    database: 'syncmasterpro',
    user: 'pi',
    password: 'ubuntu'
  });
  console.log('✅ PostgreSQL connected');
  
  try {
    // Check Users
    console.log('\n2. 👥 Comparing Users...');
    const sqliteUsers = sqlite.prepare('SELECT id, email, name, created_at FROM users ORDER BY id').all();
    const pgUsers = await pgPool.query('SELECT id, email, name, created_at FROM users ORDER BY id');
    
    console.log(`📊 SQLite Users: ${sqliteUsers.length}`);
    console.log(`📊 PostgreSQL Users: ${pgUsers.rows.length}`);
    
    if (sqliteUsers.length !== pgUsers.rows.length) {
      console.log('⚠️ User count mismatch!');
    }
    
    // Check Sync Tasks
    console.log('\n3. 🔄 Comparing Sync Tasks...');
    const sqliteTasks = sqlite.prepare('SELECT id, name, source_path, destination_path, sync_type, status, user_id FROM sync_tasks ORDER BY id').all();
    const pgTasks = await pgPool.query('SELECT id, name, source_path, destination_path, sync_type, status, user_id FROM sync_tasks ORDER BY id');
    
    console.log(`📊 SQLite Tasks: ${sqliteTasks.length}`);
    console.log(`📊 PostgreSQL Tasks: ${pgTasks.rows.length}`);
    
    if (sqliteTasks.length !== pgTasks.rows.length) {
      console.log('⚠️ Task count mismatch!');
      
      console.log('\n📋 SQLite Tasks:');
      sqliteTasks.forEach(task => {
        console.log(`   - ${task.id}: ${task.name} (${task.sync_type}) - ${task.status}`);
      });
      
      console.log('\n📋 PostgreSQL Tasks:');
      pgTasks.rows.forEach(task => {
        console.log(`   - ${task.id}: ${task.name} (${task.sync_type}) - ${task.status}`);
      });
    }
    
    // Check Sync History
    console.log('\n4. 📈 Comparing Sync History...');
    const sqliteHistory = sqlite.prepare('SELECT id, task_id, status, started_at, completed_at FROM sync_history ORDER BY id DESC LIMIT 10').all();
    const pgHistory = await pgPool.query('SELECT id, task_id, status, started_at, completed_at FROM sync_history ORDER BY id DESC LIMIT 10');
    
    console.log(`📊 SQLite History (last 10): ${sqliteHistory.length}`);
    console.log(`📊 PostgreSQL History (last 10): ${pgHistory.rows.length}`);
    
    if (sqliteHistory.length !== pgHistory.rows.length) {
      console.log('⚠️ History count mismatch!');
    }
    
    // Check Clients
    console.log('\n5. 🖥️ Comparing Clients...');
    const sqliteClients = sqlite.prepare('SELECT id, client_id, hostname, platform, status FROM clients ORDER BY id').all();
    const pgClients = await pgPool.query('SELECT id, client_id, hostname, platform, status FROM clients ORDER BY id');
    
    console.log(`📊 SQLite Clients: ${sqliteClients.length}`);
    console.log(`📊 PostgreSQL Clients: ${pgClients.rows.length}`);
    
    if (sqliteClients.length !== pgClients.rows.length) {
      console.log('⚠️ Client count mismatch!');
      
      console.log('\n📋 SQLite Clients:');
      sqliteClients.forEach(client => {
        console.log(`   - ${client.id}: ${client.hostname} (${client.client_id}) - ${client.status}`);
      });
      
      console.log('\n📋 PostgreSQL Clients:');
      pgClients.rows.forEach(client => {
        console.log(`   - ${client.id}: ${client.hostname} (${client.client_id}) - ${client.status}`);
      });
    }
    
    // Summary
    console.log('\n📊 Synchronization Summary:');
    console.log('===========================');
    console.log(`- Users: SQLite(${sqliteUsers.length}) vs PostgreSQL(${pgUsers.rows.length}) ${sqliteUsers.length === pgUsers.rows.length ? '✅' : '❌'}`);
    console.log(`- Tasks: SQLite(${sqliteTasks.length}) vs PostgreSQL(${pgTasks.rows.length}) ${sqliteTasks.length === pgTasks.rows.length ? '✅' : '❌'}`);
    console.log(`- History: SQLite(${sqliteHistory.length}) vs PostgreSQL(${pgHistory.rows.length}) ${sqliteHistory.length === pgHistory.rows.length ? '✅' : '❌'}`);
    console.log(`- Clients: SQLite(${sqliteClients.length}) vs PostgreSQL(${pgClients.rows.length}) ${sqliteClients.length === pgClients.rows.length ? '✅' : '❌'}`);
    
    const isInSync = (
      sqliteUsers.length === pgUsers.rows.length &&
      sqliteTasks.length === pgTasks.rows.length &&
      sqliteHistory.length === pgHistory.rows.length &&
      sqliteClients.length === pgClients.rows.length
    );
    
    if (isInSync) {
      console.log('\n🎉 Databases are in sync!');
    } else {
      console.log('\n⚠️ Databases are NOT in sync!');
      console.log('💡 Database sync service may not be working properly');
    }
    
    return {
      isInSync,
      sqlite: {
        users: sqliteUsers.length,
        tasks: sqliteTasks.length,
        history: sqliteHistory.length,
        clients: sqliteClients.length
      },
      postgresql: {
        users: pgUsers.rows.length,
        tasks: pgTasks.rows.length,
        history: pgHistory.rows.length,
        clients: pgClients.rows.length
      }
    };
    
  } finally {
    sqlite.close();
    await pgPool.end();
  }
}

async function checkSyncService() {
  console.log('\n6. 🔧 Checking Database Sync Service...');
  
  try {
    const response = await require('axios').get('http://localhost:5001/api/health', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Web server is running');
      console.log('📝 Health check response:', response.data);
    } else {
      console.log('❌ Web server health check failed');
    }
  } catch (error) {
    console.log('❌ Web server not accessible:', error.message);
  }
}

async function triggerManualSync() {
  console.log('\n7. 🔄 Triggering manual database sync...');
  
  try {
    const response = await require('axios').post('http://localhost:5001/api/sync/manual', {}, {
      timeout: 30000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Manual sync triggered successfully');
      console.log('📝 Sync response:', response.data);
    } else {
      console.log('❌ Manual sync failed:', response.data);
    }
  } catch (error) {
    console.log('❌ Manual sync error:', error.message);
  }
}

async function main() {
  try {
    console.log('🚀 Starting database sync check...\n');
    
    // Check current sync status
    const syncStatus = await checkDatabaseSync();
    
    // Check sync service
    await checkSyncService();
    
    // If not in sync, trigger manual sync
    if (!syncStatus.isInSync) {
      await triggerManualSync();
      
      // Wait a bit and check again
      console.log('\n⏳ Waiting 5 seconds for sync to complete...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      console.log('\n🔄 Checking sync status again...');
      const newSyncStatus = await checkDatabaseSync();
      
      if (newSyncStatus.isInSync) {
        console.log('\n🎉 SUCCESS! Databases are now synchronized!');
      } else {
        console.log('\n❌ Databases still not synchronized');
        console.log('💡 Manual intervention may be required');
      }
    }
    
    console.log('\n📋 Recommendations:');
    console.log('===================');
    console.log('1. Desktop app should be the primary data source');
    console.log('2. Web manager should sync FROM desktop TO web database');
    console.log('3. Database sync service should run automatically');
    console.log('4. Check sync service logs for any errors');
    
  } catch (error) {
    console.error('\n❌ Check failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
