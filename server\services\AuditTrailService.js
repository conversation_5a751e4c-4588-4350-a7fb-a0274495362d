const { getDatabase } = require('../database/init');

class AuditTrailService {
  constructor() {
    this.db = getDatabase();
  }

  // Log audit event
  async logEvent(eventData) {
    try {
      const {
        userId,
        action,
        resource,
        resourceId,
        details = {},
        ipAddress,
        userAgent,
        sessionId
      } = eventData;

      const auditEntry = {
        user_id: userId,
        action,
        resource,
        resource_id: resourceId,
        details: JSON.stringify(details),
        ip_address: ipAddress,
        user_agent: userAgent,
        session_id: sessionId,
        timestamp: new Date().toISOString(),
        severity: this.determineSeverity(action)
      };

      await this.db.query(`
        INSERT INTO audit_trail (
          user_id, action, resource, resource_id, details, 
          ip_address, user_agent, session_id, timestamp, severity
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        auditEntry.user_id,
        auditEntry.action,
        auditEntry.resource,
        auditEntry.resource_id,
        auditEntry.details,
        auditEntry.ip_address,
        auditEntry.user_agent,
        auditEntry.session_id,
        auditEntry.timestamp,
        auditEntry.severity
      ]);

      // Check for security alerts
      await this.checkSecurityAlerts(auditEntry);

      return auditEntry;
    } catch (error) {
      console.error('Audit logging error:', error);
      throw error;
    }
  }

  // Get audit trail with filters
  async getAuditTrail(filters = {}) {
    try {
      const {
        userId,
        action,
        resource,
        startDate,
        endDate,
        severity,
        limit = 100,
        offset = 0
      } = filters;

      let whereClause = 'WHERE 1=1';
      const params = [];

      if (userId) {
        whereClause += ' AND user_id = ?';
        params.push(userId);
      }

      if (action) {
        whereClause += ' AND action = ?';
        params.push(action);
      }

      if (resource) {
        whereClause += ' AND resource = ?';
        params.push(resource);
      }

      if (startDate) {
        whereClause += ' AND timestamp >= ?';
        params.push(startDate);
      }

      if (endDate) {
        whereClause += ' AND timestamp <= ?';
        params.push(endDate);
      }

      if (severity) {
        whereClause += ' AND severity = ?';
        params.push(severity);
      }

      const result = await this.db.query(`
        SELECT at.*, u.email as user_email
        FROM audit_trail at
        LEFT JOIN users u ON at.user_id = u.id
        ${whereClause}
        ORDER BY timestamp DESC
        LIMIT ? OFFSET ?
      `, [...params, limit, offset]);

      const countResult = await this.db.query(`
        SELECT COUNT(*) as total
        FROM audit_trail at
        ${whereClause}
      `, params);

      return {
        entries: result.rows.map(row => ({
          id: row.id,
          userId: row.user_id,
          userEmail: row.user_email,
          action: row.action,
          resource: row.resource,
          resourceId: row.resource_id,
          details: JSON.parse(row.details || '{}'),
          ipAddress: row.ip_address,
          userAgent: row.user_agent,
          sessionId: row.session_id,
          timestamp: row.timestamp,
          severity: row.severity
        })),
        total: countResult.rows[0].total,
        limit,
        offset
      };
    } catch (error) {
      console.error('Audit trail retrieval error:', error);
      throw error;
    }
  }

  // Get audit statistics
  async getAuditStatistics(timeRange = '30d') {
    try {
      const dateFilter = this.getDateFilter(timeRange);

      // Action statistics
      const actionStats = await this.db.query(`
        SELECT action, COUNT(*) as count
        FROM audit_trail
        WHERE 1=1 ${dateFilter}
        GROUP BY action
        ORDER BY count DESC
      `);

      // Severity statistics
      const severityStats = await this.db.query(`
        SELECT severity, COUNT(*) as count
        FROM audit_trail
        WHERE 1=1 ${dateFilter}
        GROUP BY severity
        ORDER BY 
          CASE severity 
            WHEN 'critical' THEN 1 
            WHEN 'high' THEN 2 
            WHEN 'medium' THEN 3 
            WHEN 'low' THEN 4 
          END
      `);

      // User activity
      const userActivity = await this.db.query(`
        SELECT at.user_id, u.email, COUNT(*) as activity_count
        FROM audit_trail at
        LEFT JOIN users u ON at.user_id = u.id
        WHERE 1=1 ${dateFilter}
        GROUP BY at.user_id, u.email
        ORDER BY activity_count DESC
        LIMIT 10
      `);

      // Daily activity
      const dailyActivity = await this.db.query(`
        SELECT DATE(timestamp) as date, COUNT(*) as count
        FROM audit_trail
        WHERE 1=1 ${dateFilter}
        GROUP BY DATE(timestamp)
        ORDER BY date DESC
        LIMIT 30
      `);

      // Resource access
      const resourceAccess = await this.db.query(`
        SELECT resource, COUNT(*) as access_count
        FROM audit_trail
        WHERE 1=1 ${dateFilter}
        GROUP BY resource
        ORDER BY access_count DESC
      `);

      return {
        timeRange,
        actionStatistics: actionStats.rows.map(row => ({
          action: row.action,
          count: parseInt(row.count)
        })),
        severityStatistics: severityStats.rows.map(row => ({
          severity: row.severity,
          count: parseInt(row.count)
        })),
        userActivity: userActivity.rows.map(row => ({
          userId: row.user_id,
          userEmail: row.email,
          activityCount: parseInt(row.activity_count)
        })),
        dailyActivity: dailyActivity.rows.map(row => ({
          date: row.date,
          count: parseInt(row.count)
        })),
        resourceAccess: resourceAccess.rows.map(row => ({
          resource: row.resource,
          accessCount: parseInt(row.access_count)
        }))
      };
    } catch (error) {
      console.error('Audit statistics error:', error);
      throw error;
    }
  }

  // Check for security alerts
  async checkSecurityAlerts(auditEntry) {
    try {
      const alerts = [];

      // Check for multiple failed login attempts
      if (auditEntry.action === 'login_failed') {
        const recentFailures = await this.db.query(`
          SELECT COUNT(*) as count
          FROM audit_trail
          WHERE user_id = ? AND action = 'login_failed' 
          AND timestamp >= datetime('now', '-1 hour')
        `, [auditEntry.user_id]);

        if (recentFailures.rows[0].count >= 5) {
          alerts.push({
            type: 'security',
            severity: 'high',
            message: 'Multiple failed login attempts detected',
            userId: auditEntry.user_id,
            details: { failureCount: recentFailures.rows[0].count }
          });
        }
      }

      // Check for unusual IP address
      if (auditEntry.action === 'login_success') {
        const recentIPs = await this.db.query(`
          SELECT DISTINCT ip_address
          FROM audit_trail
          WHERE user_id = ? AND action = 'login_success'
          AND timestamp >= datetime('now', '-7 days')
          AND ip_address != ?
        `, [auditEntry.user_id, auditEntry.ip_address]);

        if (recentIPs.rows.length === 0 && auditEntry.ip_address) {
          alerts.push({
            type: 'security',
            severity: 'medium',
            message: 'Login from new IP address',
            userId: auditEntry.user_id,
            details: { ipAddress: auditEntry.ip_address }
          });
        }
      }

      // Check for bulk operations
      if (['bulk_delete', 'bulk_update', 'bulk_sync'].includes(auditEntry.action)) {
        alerts.push({
          type: 'operational',
          severity: 'medium',
          message: 'Bulk operation performed',
          userId: auditEntry.user_id,
          details: { action: auditEntry.action }
        });
      }

      // Save alerts
      for (const alert of alerts) {
        await this.createSecurityAlert(alert);
      }

      return alerts;
    } catch (error) {
      console.error('Security alert check error:', error);
      return [];
    }
  }

  // Create security alert
  async createSecurityAlert(alert) {
    try {
      await this.db.query(`
        INSERT INTO security_alerts (
          user_id, type, severity, message, details, created_at, status
        ) VALUES (?, ?, ?, ?, ?, datetime('now'), 'active')
      `, [
        alert.userId,
        alert.type,
        alert.severity,
        alert.message,
        JSON.stringify(alert.details)
      ]);
    } catch (error) {
      console.error('Security alert creation error:', error);
    }
  }

  // Get security alerts
  async getSecurityAlerts(filters = {}) {
    try {
      const {
        userId,
        type,
        severity,
        status = 'active',
        limit = 50,
        offset = 0
      } = filters;

      let whereClause = 'WHERE 1=1';
      const params = [];

      if (userId) {
        whereClause += ' AND user_id = ?';
        params.push(userId);
      }

      if (type) {
        whereClause += ' AND type = ?';
        params.push(type);
      }

      if (severity) {
        whereClause += ' AND severity = ?';
        params.push(severity);
      }

      if (status) {
        whereClause += ' AND sa.status = ?';
        params.push(status);
      }

      const result = await this.db.query(`
        SELECT sa.*, u.email as user_email
        FROM security_alerts sa
        LEFT JOIN users u ON sa.user_id = u.id
        ${whereClause}
        ORDER BY sa.created_at DESC
        LIMIT ? OFFSET ?
      `, [...params, limit, offset]);

      return result.rows.map(row => ({
        id: row.id,
        userId: row.user_id,
        userEmail: row.user_email,
        type: row.type,
        severity: row.severity,
        message: row.message,
        details: JSON.parse(row.details || '{}'),
        status: row.status,
        createdAt: row.created_at,
        resolvedAt: row.resolved_at
      }));
    } catch (error) {
      console.error('Security alerts retrieval error:', error);
      throw error;
    }
  }

  // Resolve security alert
  async resolveSecurityAlert(alertId, resolvedBy) {
    try {
      await this.db.query(`
        UPDATE security_alerts 
        SET status = 'resolved', resolved_at = datetime('now'), resolved_by = ?
        WHERE id = ?
      `, [resolvedBy, alertId]);

      return { success: true, message: 'Alert resolved successfully' };
    } catch (error) {
      console.error('Security alert resolution error:', error);
      throw error;
    }
  }

  // Generate compliance report
  async generateComplianceReport(timeRange = '30d') {
    try {
      const auditStats = await this.getAuditStatistics(timeRange);
      const securityAlerts = await this.getSecurityAlerts({ status: 'active' });
      
      const report = {
        id: `compliance_${Date.now()}`,
        timeRange,
        generatedAt: new Date().toISOString(),
        auditStatistics: auditStats,
        securityAlerts: {
          total: securityAlerts.length,
          bySeverity: this.groupBySeverity(securityAlerts),
          byType: this.groupByType(securityAlerts)
        },
        compliance: {
          auditCoverage: this.calculateAuditCoverage(auditStats),
          securityScore: this.calculateSecurityScore(securityAlerts),
          recommendations: this.generateComplianceRecommendations(auditStats, securityAlerts)
        }
      };

      return report;
    } catch (error) {
      console.error('Compliance report generation error:', error);
      throw error;
    }
  }

  // Helper methods
  determineSeverity(action) {
    const severityMap = {
      'login_failed': 'medium',
      'login_success': 'low',
      'logout': 'low',
      'sync_create': 'low',
      'sync_update': 'low',
      'sync_delete': 'medium',
      'user_create': 'high',
      'user_update': 'medium',
      'user_delete': 'high',
      'profile_create': 'low',
      'profile_update': 'low',
      'profile_delete': 'medium',
      'conflict_resolve': 'medium',
      'version_restore': 'medium',
      'bulk_delete': 'high',
      'bulk_update': 'high',
      'system_config': 'critical'
    };

    return severityMap[action] || 'low';
  }

  getDateFilter(timeRange) {
    const days = {
      '7d': 7,
      '30d': 30,
      '90d': 90,
      '1y': 365
    };

    const daysBack = days[timeRange] || 30;
    return `AND timestamp >= datetime('now', '-${daysBack} days')`;
  }

  groupBySeverity(alerts) {
    return alerts.reduce((acc, alert) => {
      acc[alert.severity] = (acc[alert.severity] || 0) + 1;
      return acc;
    }, {});
  }

  groupByType(alerts) {
    return alerts.reduce((acc, alert) => {
      acc[alert.type] = (acc[alert.type] || 0) + 1;
      return acc;
    }, {});
  }

  calculateAuditCoverage(auditStats) {
    const totalActions = auditStats.actionStatistics.reduce((sum, stat) => sum + stat.count, 0);
    const criticalActions = auditStats.actionStatistics
      .filter(stat => ['user_create', 'user_delete', 'bulk_delete'].includes(stat.action))
      .reduce((sum, stat) => sum + stat.count, 0);

    return {
      totalActions,
      criticalActions,
      coveragePercentage: totalActions > 0 ? ((criticalActions / totalActions) * 100).toFixed(2) : '0.00'
    };
  }

  calculateSecurityScore(alerts) {
    const severityWeights = { critical: 10, high: 5, medium: 2, low: 1 };
    const totalScore = alerts.reduce((score, alert) => {
      return score + (severityWeights[alert.severity] || 1);
    }, 0);

    // Lower score is better (fewer/less severe alerts)
    const maxScore = 100;
    const securityScore = Math.max(0, maxScore - totalScore);

    return {
      score: securityScore,
      grade: this.getSecurityGrade(securityScore),
      totalAlerts: alerts.length
    };
  }

  getSecurityGrade(score) {
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }

  generateComplianceRecommendations(auditStats, alerts) {
    const recommendations = [];

    // Check for high-severity alerts
    const highSeverityAlerts = alerts.filter(alert => 
      ['critical', 'high'].includes(alert.severity)
    );

    if (highSeverityAlerts.length > 0) {
      recommendations.push({
        priority: 'high',
        title: 'Address High-Severity Security Alerts',
        description: `${highSeverityAlerts.length} high-severity alerts require immediate attention.`,
        action: 'Review and resolve security alerts'
      });
    }

    // Check audit coverage
    const coverage = this.calculateAuditCoverage(auditStats);
    if (parseFloat(coverage.coveragePercentage) < 50) {
      recommendations.push({
        priority: 'medium',
        title: 'Improve Audit Coverage',
        description: 'Critical actions audit coverage is below recommended threshold.',
        action: 'Enable comprehensive audit logging'
      });
    }

    return recommendations;
  }
}

module.exports = AuditTrailService;
