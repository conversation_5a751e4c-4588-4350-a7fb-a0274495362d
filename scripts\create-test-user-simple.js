const axios = require('axios');

async function createTestUser() {
  console.log('🔧 Creating test user for multi-client testing...');

  const baseURL = 'http://localhost:5002/api';
  const testUser = {
    name: 'Test User',
    email: '<EMAIL>',
    password: 'password123',
    confirmPassword: 'password123'
  };

  try {
    // Try to register the test user
    console.log('📝 Registering test user...');
    
    const response = await axios.post(`${baseURL}/auth/register`, testUser);
    
    if (response.data.token) {
      console.log('✅ Test user created successfully!');
      console.log(`📧 Email: ${testUser.email}`);
      console.log(`🔑 Password: ${testUser.password}`);
      console.log(`🎫 Token: ${response.data.token.substring(0, 20)}...`);
    } else {
      console.log('❌ Registration failed - no token received');
    }
    
  } catch (error) {
    if (error.response?.status === 409) {
      console.log('ℹ️ Test user already exists, trying to login...');
      
      try {
        const loginResponse = await axios.post(`${baseURL}/auth/login`, {
          email: testUser.email,
          password: testUser.password
        });
        
        if (loginResponse.data.token) {
          console.log('✅ Test user login successful!');
          console.log(`📧 Email: ${testUser.email}`);
          console.log(`🔑 Password: ${testUser.password}`);
          console.log(`🎫 Token: ${loginResponse.data.token.substring(0, 20)}...`);
        }
      } catch (loginError) {
        console.log('❌ Login failed:', loginError.response?.data?.message || loginError.message);
      }
    } else {
      console.log('❌ Registration failed:', error.response?.data?.message || error.message);
    }
  }
}

createTestUser();
