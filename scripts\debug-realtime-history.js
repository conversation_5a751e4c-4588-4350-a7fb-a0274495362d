// Debug script for real-time sync history issues
// Run this to diagnose why history and dashboard don't show real-time sync data

const axios = require('axios');
const io = require('socket.io-client');

const baseURL = 'http://localhost:5002/api'; // Desktop API
const socketURL = 'http://localhost:5002';

class RealtimeHistoryDebugger {
  constructor() {
    this.token = null;
    this.socket = null;
    this.testTaskId = null;
    this.events = [];
  }

  async runDiagnostics() {
    console.log('🔍 Real-time History Diagnostics\n');
    
    try {
      await this.step1_Login();
      await this.step2_ConnectSocket();
      await this.step3_CheckExistingHistory();
      await this.step4_CreateTestTask();
      await this.step5_MonitorRealtimeSync();
      await this.step6_CheckHistoryAfterSync();
      await this.step7_AnalyzeResults();
    } catch (error) {
      console.error('❌ Diagnostics failed:', error);
    } finally {
      if (this.socket) {
        this.socket.disconnect();
      }
    }
  }

  async step1_Login() {
    console.log('1. 🔐 Logging in...');
    
    const response = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    this.token = response.data.token;
    console.log('✅ Login successful');
  }

  async step2_ConnectSocket() {
    console.log('\n2. 🔌 Connecting to Socket.IO...');
    
    this.socket = io(socketURL, {
      auth: { token: this.token },
      transports: ['websocket']
    });

    return new Promise((resolve) => {
      this.socket.on('connect', () => {
        console.log('✅ Socket connected:', this.socket.id);
        this.setupEventListeners();
        resolve();
      });
    });
  }

  setupEventListeners() {
    const events = [
      'sync-started',
      'sync-progress', 
      'sync-completed',
      'sync-error',
      'realtime-sync-started',
      'realtime-sync-processing',
      'realtime-sync-completed',
      'realtime-sync-stopped',
      'file-change-detected'
    ];

    events.forEach(event => {
      this.socket.on(event, (data) => {
        const timestamp = new Date().toISOString();
        this.events.push({ event, data, timestamp });
        console.log(`📡 Event: ${event}`, data);
      });
    });
  }

  async step3_CheckExistingHistory() {
    console.log('\n3. 📋 Checking existing history...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };
    const response = await axios.get(`${baseURL}/sync/history?limit=10`, { headers });
    
    const history = response.data.history || [];
    console.log(`📊 Found ${history.length} existing history entries`);
    
    if (history.length > 0) {
      console.log('Recent entries:');
      history.slice(0, 3).forEach((entry, index) => {
        console.log(`   ${index + 1}. Task ${entry.task_id}: ${entry.status} at ${entry.started_at}`);
      });
    }
  }

  async step4_CreateTestTask() {
    console.log('\n4. ➕ Creating test task with real-time...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };
    const taskData = {
      name: 'Debug Real-time History Test',
      sourcePath: 'C:\\Debug\\Source',
      destinationPath: 'C:\\Debug\\Dest',
      syncType: 'bidirectional',
      options: {
        enableRealtime: true,
        deleteExtraFiles: false,
        preserveTimestamps: true
      }
    };

    const response = await axios.post(`${baseURL}/sync/tasks`, taskData, { headers });
    this.testTaskId = response.data.task.id;
    
    console.log(`✅ Test task created with ID: ${this.testTaskId}`);
    console.log(`   Real-time enabled: ${response.data.task.options?.enableRealtime}`);
  }

  async step5_MonitorRealtimeSync() {
    console.log('\n5. ⚡ Starting real-time sync and monitoring...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };
    
    // Start real-time sync
    const startResponse = await axios.post(
      `${baseURL}/sync/tasks/${this.testTaskId}/realtime/start`, 
      {}, 
      { headers }
    );
    
    console.log('🔄 Real-time sync started:', startResponse.data);
    
    // Wait for events
    console.log('⏳ Waiting for real-time events (10 seconds)...');
    await this.delay(10000);
    
    // Stop real-time sync
    const stopResponse = await axios.post(
      `${baseURL}/sync/tasks/${this.testTaskId}/realtime/stop`, 
      {}, 
      { headers }
    );
    
    console.log('🛑 Real-time sync stopped:', stopResponse.data);
  }

  async step6_CheckHistoryAfterSync() {
    console.log('\n6. 📊 Checking history after real-time sync...');
    
    // Wait a bit for database updates
    await this.delay(2000);
    
    const headers = { 'Authorization': `Bearer ${this.token}` };
    
    // Check task-specific history
    const taskHistoryResponse = await axios.get(
      `${baseURL}/sync/history?taskId=${this.testTaskId}&limit=10`, 
      { headers }
    );
    
    const taskHistory = taskHistoryResponse.data.history || [];
    console.log(`📋 Task-specific history: ${taskHistory.length} entries`);
    
    // Check all recent history
    const allHistoryResponse = await axios.get(`${baseURL}/sync/history?limit=10`, { headers });
    const allHistory = allHistoryResponse.data.history || [];
    console.log(`📋 All recent history: ${allHistory.length} entries`);
    
    // Check if our task appears in history
    const ourEntries = allHistory.filter(entry => entry.task_id === this.testTaskId);
    console.log(`🎯 Our task entries: ${ourEntries.length}`);
    
    if (ourEntries.length > 0) {
      console.log('✅ Found history entries for our task:');
      ourEntries.forEach((entry, index) => {
        console.log(`   ${index + 1}. Status: ${entry.status}, Files: ${entry.files_processed}, Started: ${entry.started_at}`);
      });
    } else {
      console.log('❌ No history entries found for our task!');
    }
  }

  async step7_AnalyzeResults() {
    console.log('\n7. 🔍 Analyzing results...');
    
    console.log(`📡 Total events captured: ${this.events.length}`);
    
    // Group events by type
    const eventCounts = {};
    this.events.forEach(({ event }) => {
      eventCounts[event] = (eventCounts[event] || 0) + 1;
    });
    
    console.log('📊 Event breakdown:');
    Object.entries(eventCounts).forEach(([event, count]) => {
      console.log(`   ${event}: ${count}`);
    });
    
    // Check for completion events
    const completionEvents = this.events.filter(e => 
      e.event === 'sync-completed' || 
      e.event === 'realtime-sync-completed'
    );
    
    console.log(`✅ Completion events: ${completionEvents.length}`);
    
    if (completionEvents.length === 0) {
      console.log('❌ ISSUE: No completion events detected!');
      console.log('   This means real-time sync is not emitting completion events');
      console.log('   History won\'t be created without completion events');
    }
    
    // Check for history creation events
    const historyEvents = this.events.filter(e => 
      e.data && (e.data.filesProcessed > 0 || e.data.stats?.filesProcessed > 0)
    );
    
    console.log(`📋 Events with file processing data: ${historyEvents.length}`);
    
    // Recommendations
    console.log('\n💡 Recommendations:');
    
    if (completionEvents.length === 0) {
      console.log('   1. ❌ Real-time sync is not emitting completion events');
      console.log('      - Check FileSyncEngine.js emit statements');
      console.log('      - Verify real-time sync completion logic');
    }
    
    if (historyEvents.length === 0) {
      console.log('   2. ❌ No file processing data in events');
      console.log('      - Real-time sync may not be processing files');
      console.log('      - Check if source/destination paths exist');
    }
    
    console.log('   3. 🔧 Potential fixes:');
    console.log('      - Add explicit history logging in real-time sync');
    console.log('      - Ensure completion events include stats data');
    console.log('      - Add database triggers for history creation');
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Manual testing functions
const debugger = new RealtimeHistoryDebugger();

// Export for manual use
module.exports = {
  // Run full diagnostics
  runDiagnostics: () => debugger.runDiagnostics(),
  
  // Quick history check
  checkHistory: async () => {
    console.log('📋 Quick History Check...');
    
    try {
      const loginResponse = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      const token = loginResponse.data.token;
      const headers = { 'Authorization': `Bearer ${token}` };
      
      const response = await axios.get(`${baseURL}/sync/history?limit=20`, { headers });
      const history = response.data.history || [];
      
      console.log(`📊 Total history entries: ${history.length}`);
      
      if (history.length > 0) {
        console.log('\n📋 Recent entries:');
        history.slice(0, 10).forEach((entry, index) => {
          console.log(`   ${index + 1}. Task ${entry.task_id}: ${entry.status} - ${entry.files_processed} files - ${entry.started_at}`);
        });
        
        // Check for real-time entries
        const realtimeEntries = history.filter(entry => 
          entry.details && entry.details.includes('realtime')
        );
        console.log(`⚡ Real-time entries: ${realtimeEntries.length}`);
      } else {
        console.log('❌ No history entries found!');
      }
      
    } catch (error) {
      console.error('❌ Failed to check history:', error.message);
    }
  },
  
  // Test database connection
  testDatabase: async () => {
    console.log('🗄️ Testing database connection...');
    
    try {
      const loginResponse = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      const token = loginResponse.data.token;
      const headers = { 'Authorization': `Bearer ${token}` };
      
      // Test tasks endpoint
      const tasksResponse = await axios.get(`${baseURL}/sync/tasks`, { headers });
      console.log(`✅ Tasks: ${tasksResponse.data.tasks.length} found`);
      
      // Test history endpoint
      const historyResponse = await axios.get(`${baseURL}/sync/history`, { headers });
      console.log(`✅ History: ${historyResponse.data.history.length} entries found`);
      
      console.log('✅ Database connection working');
      
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
    }
  }
};

// Auto-run if called directly
if (require.main === module) {
  debugger.runDiagnostics();
}

console.log('🎯 Real-time History Debugger loaded!');
console.log('📝 Available commands:');
console.log('   - node scripts/debug-realtime-history.js');
console.log('   - require("./scripts/debug-realtime-history").checkHistory()');
console.log('   - require("./scripts/debug-realtime-history").testDatabase()');
console.log('');
