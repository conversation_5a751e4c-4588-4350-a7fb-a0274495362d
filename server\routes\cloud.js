const express = require('express');
const router = express.Router();
const CloudStorageAdapter = require('../services/CloudStorageAdapter');
const authenticateToken = require('../middleware/auth');
const { getDatabase } = require('../database/init');

// Store active cloud connections per user
const userConnections = new Map();

// Get supported cloud storage types
router.get('/types', authenticateToken, async (req, res) => {
  try {
    const supportedTypes = [
      {
        id: 'local',
        name: 'Local Storage',
        description: 'Local file system storage',
        available: true,
        requiresAuth: false
      },
      {
        id: 'gdrive',
        name: 'Google Drive',
        description: 'Google Drive cloud storage',
        available: false,
        requiresAuth: true,
        note: 'Requires Google Drive API setup'
      },
      {
        id: 'dropbox',
        name: 'Dropbox',
        description: 'Dropbox cloud storage',
        available: false,
        requiresAuth: true,
        note: 'Requires Dropbox API setup'
      },
      {
        id: 'onedrive',
        name: 'OneDrive',
        description: 'Microsoft OneDrive storage',
        available: false,
        requiresAuth: true,
        note: 'Requires Microsoft Graph API setup'
      },
      {
        id: 's3',
        name: 'Amazon S3',
        description: 'Amazon S3 compatible storage',
        available: false,
        requiresAuth: true,
        note: 'Requires AWS credentials'
      }
    ];
    
    res.json({
      success: true,
      types: supportedTypes
    });
  } catch (error) {
    console.error('Failed to get cloud storage types:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get cloud storage types'
    });
  }
});

// Connect to cloud storage
router.post('/connect', authenticateToken, async (req, res) => {
  try {
    const { type, config = {} } = req.body;
    
    if (!type) {
      return res.status(400).json({
        success: false,
        error: 'Storage type is required'
      });
    }
    
    // Create cloud storage adapter
    const adapter = new CloudStorageAdapter({
      type,
      ...config
    });
    
    // Connect to storage
    await adapter.connect();
    
    // Store connection for this user
    userConnections.set(req.userId, adapter);
    
    // Save connection config to database (without sensitive credentials)
    const db = getDatabase();
    const safeConfig = { ...config };
    delete safeConfig.credentials;
    delete safeConfig.apiKey;
    delete safeConfig.secret;
    
    await db.query(
      `INSERT OR REPLACE INTO cloud_connections 
       (user_id, type, config, connected_at)
       VALUES (?, ?, ?, datetime('now'))`,
      [req.userId, type, JSON.stringify(safeConfig)]
    );
    
    res.json({
      success: true,
      message: `Connected to ${type} storage successfully`,
      connection: {
        type,
        connected: true,
        connectedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Failed to connect to cloud storage:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to connect to cloud storage'
    });
  }
});

// Disconnect from cloud storage
router.post('/disconnect', authenticateToken, async (req, res) => {
  try {
    const adapter = userConnections.get(req.userId);
    
    if (adapter) {
      await adapter.disconnect();
      userConnections.delete(req.userId);
    }
    
    // Update database
    const db = getDatabase();
    await db.query(
      'DELETE FROM cloud_connections WHERE user_id = ?',
      [req.userId]
    );
    
    res.json({
      success: true,
      message: 'Disconnected from cloud storage successfully'
    });
  } catch (error) {
    console.error('Failed to disconnect from cloud storage:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to disconnect from cloud storage'
    });
  }
});

// Get connection status
router.get('/status', authenticateToken, async (req, res) => {
  try {
    const adapter = userConnections.get(req.userId);
    const db = getDatabase();
    
    let connectionInfo = null;
    
    if (adapter && adapter.isConnected) {
      connectionInfo = {
        type: adapter.type,
        connected: true,
        connectedAt: new Date().toISOString()
      };
    } else {
      // Check database for saved connection
      const result = await db.query(
        'SELECT * FROM cloud_connections WHERE user_id = ?',
        [req.userId]
      );
      
      if (result.rows.length > 0) {
        const saved = result.rows[0];
        connectionInfo = {
          type: saved.type,
          connected: false,
          lastConnected: saved.connected_at,
          config: JSON.parse(saved.config)
        };
      }
    }
    
    res.json({
      success: true,
      connection: connectionInfo
    });
  } catch (error) {
    console.error('Failed to get connection status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get connection status'
    });
  }
});

// List files in cloud storage
router.get('/files', authenticateToken, async (req, res) => {
  try {
    const { path = '/' } = req.query;
    const adapter = userConnections.get(req.userId);
    
    if (!adapter || !adapter.isConnected) {
      return res.status(400).json({
        success: false,
        error: 'Not connected to cloud storage'
      });
    }
    
    const files = await adapter.listFiles(path);
    
    res.json({
      success: true,
      files,
      path,
      count: files.length
    });
  } catch (error) {
    console.error('Failed to list cloud files:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to list cloud files'
    });
  }
});

// Upload file to cloud storage
router.post('/upload', authenticateToken, async (req, res) => {
  try {
    const { localPath, remotePath } = req.body;
    const adapter = userConnections.get(req.userId);
    
    if (!adapter || !adapter.isConnected) {
      return res.status(400).json({
        success: false,
        error: 'Not connected to cloud storage'
      });
    }
    
    if (!localPath || !remotePath) {
      return res.status(400).json({
        success: false,
        error: 'Local path and remote path are required'
      });
    }
    
    const result = await adapter.uploadFile(localPath, remotePath);
    
    res.json({
      success: true,
      message: 'File uploaded successfully',
      result
    });
  } catch (error) {
    console.error('Failed to upload file:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to upload file'
    });
  }
});

// Download file from cloud storage
router.post('/download', authenticateToken, async (req, res) => {
  try {
    const { remotePath, localPath } = req.body;
    const adapter = userConnections.get(req.userId);
    
    if (!adapter || !adapter.isConnected) {
      return res.status(400).json({
        success: false,
        error: 'Not connected to cloud storage'
      });
    }
    
    if (!remotePath || !localPath) {
      return res.status(400).json({
        success: false,
        error: 'Remote path and local path are required'
      });
    }
    
    const result = await adapter.downloadFile(remotePath, localPath);
    
    res.json({
      success: true,
      message: 'File downloaded successfully',
      result
    });
  } catch (error) {
    console.error('Failed to download file:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to download file'
    });
  }
});

// Sync directory with cloud storage
router.post('/sync', authenticateToken, async (req, res) => {
  try {
    const { localDir, remoteDir, options = {} } = req.body;
    const adapter = userConnections.get(req.userId);
    
    if (!adapter || !adapter.isConnected) {
      return res.status(400).json({
        success: false,
        error: 'Not connected to cloud storage'
      });
    }
    
    if (!localDir || !remoteDir) {
      return res.status(400).json({
        success: false,
        error: 'Local directory and remote directory are required'
      });
    }
    
    const result = await adapter.syncDirectory(localDir, remoteDir, options);
    
    res.json({
      success: true,
      message: 'Directory sync completed',
      result: {
        ...result,
        summary: {
          uploaded: result.uploaded.length,
          downloaded: result.downloaded.length,
          deleted: result.deleted.length,
          errors: result.errors.length
        }
      }
    });
  } catch (error) {
    console.error('Failed to sync directory:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to sync directory'
    });
  }
});

// Get file info from cloud storage
router.get('/files/info', authenticateToken, async (req, res) => {
  try {
    const { path } = req.query;
    const adapter = userConnections.get(req.userId);
    
    if (!adapter || !adapter.isConnected) {
      return res.status(400).json({
        success: false,
        error: 'Not connected to cloud storage'
      });
    }
    
    if (!path) {
      return res.status(400).json({
        success: false,
        error: 'File path is required'
      });
    }
    
    const fileInfo = await adapter.getFileInfo(path);
    
    res.json({
      success: true,
      fileInfo: {
        ...fileInfo,
        sizeMB: fileInfo.size ? (fileInfo.size / (1024 * 1024)).toFixed(2) : '0.00'
      }
    });
  } catch (error) {
    console.error('Failed to get file info:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get file info'
    });
  }
});

// Delete file from cloud storage
router.delete('/files', authenticateToken, async (req, res) => {
  try {
    const { path } = req.query;
    const adapter = userConnections.get(req.userId);
    
    if (!adapter || !adapter.isConnected) {
      return res.status(400).json({
        success: false,
        error: 'Not connected to cloud storage'
      });
    }
    
    if (!path) {
      return res.status(400).json({
        success: false,
        error: 'File path is required'
      });
    }
    
    const result = await adapter.deleteFile(path);
    
    res.json({
      success: true,
      message: 'File deleted successfully',
      result
    });
  } catch (error) {
    console.error('Failed to delete file:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete file'
    });
  }
});

// Get storage statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const adapter = userConnections.get(req.userId);
    
    if (!adapter || !adapter.isConnected) {
      return res.status(400).json({
        success: false,
        error: 'Not connected to cloud storage'
      });
    }
    
    const stats = await adapter.getStorageStats();
    
    res.json({
      success: true,
      stats: {
        ...stats,
        totalSpaceGB: (stats.totalSpace / (1024 * 1024 * 1024)).toFixed(2),
        usedSpaceGB: (stats.usedSpace / (1024 * 1024 * 1024)).toFixed(2),
        freeSpaceGB: (stats.freeSpace / (1024 * 1024 * 1024)).toFixed(2),
        usagePercentage: stats.totalSpace > 0 
          ? ((stats.usedSpace / stats.totalSpace) * 100).toFixed(1)
          : '0.0'
      }
    });
  } catch (error) {
    console.error('Failed to get storage stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get storage stats'
    });
  }
});

module.exports = router;
