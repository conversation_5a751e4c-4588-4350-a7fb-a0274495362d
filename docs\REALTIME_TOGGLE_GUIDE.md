# ⚡ Hướng dẫn sử dụng Real-time Toggle

## 🎯 Tổng quan

Real-time sync giờ đây là một **Advanced Option** thay vì một sync type riêng biệt. Người dùng có thể bật/tắt real-time sync cho bất kỳ loại sync nào thông qua toggle switch hiện đại.

## 🔄 Thay đổi so với trước

### ❌ Trước đây
- Real-time là một **Sync Type** riêng biệt
- Phải chọn "Real-time" trong dropdown
- Không linh hoạt kết hợp với các sync type khác

### ✅ Bây giờ  
- Real-time là một **Advanced Option**
- Toggle switch hiện đại thay vì checkbox
- C<PERSON> thể kết hợp với bất kỳ sync type nào
- Linh hoạt và trực quan hơn

## 🎮 Cách sử dụng

### 1. Tạo Task với Real-time

```
1. Click "Create New Task"
2. <PERSON><PERSON><PERSON><PERSON> thông tin cơ bản (name, paths)
3. Chọn Sync Type (bidirectional, source-to-dest, etc.)
4. Scroll xuống "Advanced Options"
5. Tìm "⚡ Enable Real-time Sync" toggle
6. Click toggle để bật ON (màu xanh)
7. Click "Create Task"
```

### 2. Nhận diện Task Real-time

**Task Card sẽ hiển thị:**
- **Badge**: `⚡ Real-time` (màu tím)
- **Status**: `Monitoring` khi đang chạy (màu tím)
- **Behavior**: Tự động sync khi có file changes

### 3. Quản lý Real-time Sync

```
Start Task:
- Click "Start" → Status: "Monitoring"
- File watcher bắt đầu theo dõi
- Tự động sync khi có thay đổi

Stop Task:
- Click "Stop" → Status: "Idle"  
- File watcher dừng theo dõi
- Không còn real-time sync
```

## 🎨 UI Components

### Toggle Switch
```
OFF State: ⚪ (Gray background, switch left)
ON State:  🔵 (Blue background, switch right)
```

**Features:**
- Smooth animation
- Accessible (keyboard + screen reader)
- Modern design
- Clear visual feedback

### Task Card Indicators

**Real-time Badge:**
```html
⚡ Real-time (Purple badge)
```

**Status Colors:**
- `Monitoring`: Purple (đang theo dõi)
- `Syncing`: Blue (đang đồng bộ)
- `Idle`: Gray (không hoạt động)

## 🔧 Technical Implementation

### Form Data Structure
```javascript
{
  name: "My Sync Task",
  syncType: "bidirectional",  // Sync type riêng biệt
  options: {
    enableRealtime: true,     // Real-time option
    deleteExtraFiles: false,
    preserveTimestamps: true
  }
}
```

### API Calls
```javascript
// Regular sync
POST /api/sync/tasks/{id}/start

// Real-time sync (when enableRealtime: true)
POST /api/sync/tasks/{id}/realtime/start
POST /api/sync/tasks/{id}/realtime/stop
```

### Socket.IO Events
```javascript
// Real-time specific events
'realtime-sync-started'
'file-change-detected'
'realtime-sync-processing'
'realtime-sync-completed'
'realtime-sync-stopped'
'realtime-sync-error'
```

## 🧪 Testing

### 1. Chạy Test Script
```javascript
// Trong browser console
window.realtimeToggleTester.runTests();
```

### 2. Manual Testing
```
1. Tạo task với real-time toggle ON
2. Start task → Status: "Monitoring"
3. Tạo file trong source folder
4. Kiểm tra file được sync tự động
5. Status: Monitoring → Syncing → Monitoring
```

### 3. UI Testing
```
Toggle Switch:
✅ OFF: Gray, switch left
✅ ON: Blue, switch right  
✅ Smooth animation
✅ Keyboard accessible

Task Card:
✅ Purple "⚡ Real-time" badge
✅ Purple "Monitoring" status
✅ Proper color coding
```

## 🎯 Use Cases

### 1. Bidirectional + Real-time
```
Sync Type: Bidirectional
Real-time: ON
→ Two-way real-time sync
```

### 2. Source-to-Destination + Real-time  
```
Sync Type: Source-to-Destination
Real-time: ON
→ One-way real-time backup
```

### 3. Incremental + Real-time
```
Sync Type: Incremental
Real-time: ON
→ Real-time incremental sync
```

### 4. Scheduled + Real-time
```
Sync Type: Scheduled
Real-time: ON
→ Scheduled sync + real-time monitoring
```

## 🔍 Troubleshooting

### Toggle không hoạt động
```
Nguyên nhân: JavaScript error hoặc component không render
Giải pháp: Check console logs, refresh page
```

### Real-time sync không start
```
Nguyên nhân: Server không hỗ trợ hoặc Socket.IO lỗi
Giải pháp: Check server logs, restart server
```

### Task card không hiển thị badge
```
Nguyên nhân: Data không sync hoặc CSS issue
Giải pháp: Refresh page, check task data
```

### File changes không được detect
```
Nguyên nhân: File watcher chưa start hoặc path không đúng
Giải pháp: Check task status, verify paths
```

## 📊 Performance

### Toggle Switch
- **Render time**: < 1ms
- **Animation**: 200ms smooth transition
- **Memory**: Minimal impact

### Real-time Sync
- **File watcher**: Chokidar (efficient)
- **Debounce**: 2 seconds (configurable)
- **Memory**: Depends on folder size

## 🎉 Benefits

### 1. User Experience
- ✅ More intuitive interface
- ✅ Flexible combinations
- ✅ Modern toggle design
- ✅ Clear visual feedback

### 2. Technical
- ✅ Better code organization
- ✅ Reusable toggle component
- ✅ Consistent data structure
- ✅ Maintainable architecture

### 3. Functionality
- ✅ Any sync type + real-time
- ✅ Granular control
- ✅ Easy to enable/disable
- ✅ Future-proof design

## 🚀 Future Enhancements

- [ ] **Real-time Settings** - Debounce time, file size limits
- [ ] **Smart Toggle** - Auto-enable for certain scenarios
- [ ] **Batch Toggle** - Enable real-time for multiple tasks
- [ ] **Performance Monitor** - Real-time sync statistics
- [ ] **Conditional Real-time** - Based on file types or size

Real-time sync toggle giờ đây linh hoạt và dễ sử dụng hơn! 🎯
