import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

const SessionManager = () => {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { token } = useAuth();

  useEffect(() => {
    loadSessions();
    // Refresh every 30 seconds
    const interval = setInterval(loadSessions, 30000);
    return () => clearInterval(interval);
  }, [token]);

  const loadSessions = async () => {
    if (!token) return;

    try {
      setError(null);
      const response = await fetch('http://localhost:5001/api/auth/sessions', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to load sessions: ${response.status}`);
      }

      const data = await response.json();
      setSessions(data.sessions || []);
    } catch (error) {
      console.error('Failed to load sessions:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const revokeOtherSessions = async () => {
    if (!token) return;

    try {
      const response = await fetch('http://localhost:5001/api/auth/sessions/revoke-others', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to revoke sessions: ${response.status}`);
      }

      const data = await response.json();
      alert(`Successfully revoked ${data.revokedCount} other sessions`);
      loadSessions(); // Refresh list
    } catch (error) {
      console.error('Failed to revoke other sessions:', error);
      alert(`Failed to revoke sessions: ${error.message}`);
    }
  };

  const revokeSession = async (sessionId) => {
    if (!token) return;

    try {
      const response = await fetch(`http://localhost:5001/api/auth/sessions/${sessionId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to revoke session: ${response.status}`);
      }

      alert('Session revoked successfully');
      loadSessions(); // Refresh list
    } catch (error) {
      console.error('Failed to revoke session:', error);
      alert(`Failed to revoke session: ${error.message}`);
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Sessions</h3>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading sessions...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Active Sessions</h3>
        <div className="flex space-x-2">
          <button
            onClick={loadSessions}
            className="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200"
          >
            Refresh
          </button>
          {sessions.length > 1 && (
            <button
              onClick={revokeOtherSessions}
              className="px-3 py-1 bg-red-100 text-red-700 rounded-md text-sm hover:bg-red-200"
            >
              Revoke Others
            </button>
          )}
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      <div className="space-y-3">
        {sessions.length === 0 ? (
          <p className="text-gray-500 text-center py-4">No active sessions found</p>
        ) : (
          sessions.map((session, index) => (
            <div
              key={session.id}
              className={`p-4 rounded-lg border ${
                session.isCurrent
                  ? 'bg-blue-50 border-blue-200'
                  : 'bg-gray-50 border-gray-200'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center">
                    <span className={`w-2 h-2 rounded-full mr-2 ${
                      session.isCurrent ? 'bg-blue-500' : 'bg-gray-400'
                    }`}></span>
                    <span className="font-medium text-gray-900">
                      Session {session.id}
                      {session.isCurrent && (
                        <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                          Current
                        </span>
                      )}
                    </span>
                  </div>
                  <div className="mt-1 text-sm text-gray-600">
                    <p>Created: {new Date(session.createdAt).toLocaleString()}</p>
                    <p>Expires: {new Date(session.expiresAt).toLocaleString()}</p>
                    <p className="font-mono text-xs mt-1">Token: {session.tokenPreview}</p>
                  </div>
                </div>
                
                {!session.isCurrent && (
                  <button
                    onClick={() => revokeSession(session.id.replace('...', ''))}
                    className="px-3 py-1 bg-red-100 text-red-700 rounded-md text-sm hover:bg-red-200"
                  >
                    Revoke
                  </button>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-600">
          <strong>Total Sessions:</strong> {sessions.length}
        </p>
        <p className="text-xs text-gray-500 mt-1">
          You can be logged in on multiple devices simultaneously. 
          Each session expires after 7 days of inactivity.
        </p>
      </div>
    </div>
  );
};

export default SessionManager;
