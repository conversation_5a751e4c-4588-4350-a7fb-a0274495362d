const Database = require('better-sqlite3');
const path = require('path');

function checkUsers() {
  console.log('📊 Checking users in database...');

  const dbPath = path.join(__dirname, '../data/syncmasterpro.db');
  console.log('Database path:', dbPath);

  try {
    const db = new Database(dbPath);

    // Check if users table exists
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='users'").all();
    if (tables.length === 0) {
      console.log('❌ Users table does not exist');
      return;
    }

    console.log('✅ Users table exists');

    // Get all users
    const users = db.prepare('SELECT id, email, name, created_at FROM users').all();
    
    if (users.length === 0) {
      console.log('ℹ️ No users found in database');
    } else {
      console.log(`👥 Found ${users.length} users:`);
      users.forEach(user => {
        console.log(`  - ID: ${user.id}, Email: ${user.email}, Name: ${user.name}`);
      });
    }

    db.close();

  } catch (error) {
    console.log('❌ Error checking database:', error.message);
  }
}

checkUsers();
