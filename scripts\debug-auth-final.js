#!/usr/bin/env node

/**
 * Debug authentication issues
 */

const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

console.log('🔍 Debug Authentication Issues');
console.log('==============================');

async function debugAuthentication() {
  console.log('\n1. 🐘 Connecting to PostgreSQL...');
  
  const pool = new Pool({
    host: '*************',
    port: 5432,
    database: 'syncmasterpro',
    user: 'pi',
    password: 'ubuntu'
  });

  try {
    const client = await pool.connect();
    console.log('✅ Connected to PostgreSQL');
    
    // Check users table
    console.log('\n2. 👥 Checking users table...');
    const usersResult = await client.query('SELECT id, email, name, password FROM users ORDER BY id');
    
    console.log(`📊 Found ${usersResult.rows.length} users:`);
    for (const user of usersResult.rows) {
      console.log(`   - ID: ${user.id}`);
      console.log(`     Email: ${user.email}`);
      console.log(`     Name: ${user.name}`);
      console.log(`     Password hash: ${user.password.substring(0, 30)}...`);
      
      // Test password
      console.log(`     Testing password 'admin'...`);
      const isValid = await bcrypt.compare('admin', user.password);
      console.log(`     Password valid: ${isValid ? '✅' : '❌'}`);
      console.log('');
    }
    
    // <NAME_EMAIL> exists
    console.log('\n3. 🔍 Checking <EMAIL> specifically...');
    const adminResult = await client.query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    
    if (adminResult.rows.length === 0) {
      console.log('❌ <EMAIL> not found! Creating...');
      
      // Create admin user
      const hashedPassword = await bcrypt.hash('admin', 12);
      const insertResult = await client.query(`
        INSERT INTO users (email, password, name, created_at, updated_at)
        VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id, email, name
      `, ['<EMAIL>', hashedPassword, 'admin']);
      
      console.log('✅ Admin user created:', insertResult.rows[0]);
    } else {
      console.log('✅ <EMAIL> found');
      const admin = adminResult.rows[0];
      
      // Test password
      const isValidPassword = await bcrypt.compare('admin', admin.password);
      console.log(`📝 Password test result: ${isValidPassword ? '✅ Valid' : '❌ Invalid'}`);
      
      if (!isValidPassword) {
        console.log('🔧 Updating password...');
        const newHashedPassword = await bcrypt.hash('admin', 12);
        await client.query(
          'UPDATE users SET password = $1, updated_at = CURRENT_TIMESTAMP WHERE email = $2',
          [newHashedPassword, '<EMAIL>']
        );
        console.log('✅ Password updated');
      }
    }
    
    // Clean up sessions
    console.log('\n4. 🧹 Cleaning up sessions...');
    const deleteResult = await client.query('DELETE FROM sessions');
    console.log(`✅ Deleted ${deleteResult.rowCount} sessions`);
    
    client.release();
    await pool.end();
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
    await pool.end();
    throw error;
  }
}

async function testDirectLogin() {
  console.log('\n5. 🔐 Testing direct login...');
  
  const axios = require('axios');
  
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Login Status:', response.status);
    console.log('📝 Response:', response.data);
    
    if (response.status === 200) {
      console.log('✅ Login successful!');
      console.log('👤 User:', response.data.user.name);
      console.log('🔑 Token length:', response.data.token.length);
      return {
        success: true,
        token: response.data.token,
        user: response.data.user
      };
    } else {
      console.log('❌ Login failed');
      return { success: false };
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return { success: false };
  }
}

async function testAPIWithToken(token) {
  console.log('\n6. 📊 Testing API with token...');
  
  const axios = require('axios');
  
  try {
    const response = await axios.get('http://localhost:5001/api/clients', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 API Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ API call successful!');
      const clients = response.data.clients || [];
      console.log(`📊 Found ${clients.length} clients`);
      return true;
    } else {
      console.log('❌ API call failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ API error:', error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting authentication debug...\n');
    
    // Debug database
    await debugAuthentication();
    
    // Test login
    const auth = await testDirectLogin();
    
    // Test API if login successful
    let apiWorking = false;
    if (auth.success) {
      apiWorking = await testAPIWithToken(auth.token);
    }
    
    console.log('\n📊 Debug Summary:');
    console.log('=================');
    console.log('- Database Setup:', '✅ Complete');
    console.log('- User Creation:', '✅ Complete');
    console.log('- Password Hash:', '✅ Fixed');
    console.log('- Session Cleanup:', '✅ Complete');
    console.log('- Login Test:', auth.success ? '✅ Success' : '❌ Failed');
    console.log('- API Test:', apiWorking ? '✅ Success' : '❌ Failed');
    
    if (auth.success && apiWorking) {
      console.log('\n🎉 SUCCESS! Authentication is now working!');
      console.log('🔍 You can now login to web <NAME_EMAIL> / admin');
    } else {
      console.log('\n❌ Authentication still has issues');
      console.log('💡 Check web server logs for more details');
    }
    
  } catch (error) {
    console.error('\n❌ Debug failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
