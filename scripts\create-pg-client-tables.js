// Create client management tables in PostgreSQL
const { Pool } = require('pg');

async function createClientTables() {
  console.log('🐘 Creating Client Management Tables in PostgreSQL\n');
  
  const pool = new Pool({
    user: 'pi',
    host: '*************',
    database: 'syncmasterpro',
    password: 'ubuntu',
    port: 5432,
  });
  
  try {
    // Test connection
    console.log('1. 🔌 Testing PostgreSQL connection...');
    const client = await pool.connect();
    console.log('✅ Connected to PostgreSQL');
    
    // Create desktop_clients table
    console.log('\n2. 📋 Creating desktop_clients table...');
    
    const createDesktopClientsTable = `
      CREATE TABLE IF NOT EXISTS desktop_clients (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        client_id VARCHAR(255) UNIQUE NOT NULL,
        hostname VARCHAR(255) NOT NULL,
        platform VARCHAR(50),
        arch VARCHAR(50),
        version VARCHAR(50),
        status VARCHAR(20) DEFAULT 'offline',
        last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    
    await client.query(createDesktopClientsTable);
    console.log('✅ desktop_clients table created');
    
    // Create client_commands table
    console.log('\n3. 📋 Creating client_commands table...');
    
    const createClientCommandsTable = `
      CREATE TABLE IF NOT EXISTS client_commands (
        id SERIAL PRIMARY KEY,
        client_id VARCHAR(255) NOT NULL REFERENCES desktop_clients(client_id) ON DELETE CASCADE,
        command_type VARCHAR(100) NOT NULL,
        command_data JSONB,
        status VARCHAR(20) DEFAULT 'pending',
        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        executed_at TIMESTAMP,
        result JSONB
      )
    `;
    
    await client.query(createClientCommandsTable);
    console.log('✅ client_commands table created');
    
    // Add client_id column to sync_tasks if not exists
    console.log('\n4. 📋 Adding client_id to sync_tasks...');
    
    try {
      const addClientIdColumn = `
        ALTER TABLE sync_tasks 
        ADD COLUMN IF NOT EXISTS client_id VARCHAR(255)
      `;
      
      await client.query(addClientIdColumn);
      console.log('✅ client_id column added to sync_tasks');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️ client_id column already exists in sync_tasks');
      } else {
        throw error;
      }
    }
    
    // Create indexes
    console.log('\n5. 📊 Creating indexes...');
    
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_desktop_clients_user_id ON desktop_clients (user_id)',
      'CREATE INDEX IF NOT EXISTS idx_desktop_clients_client_id ON desktop_clients (client_id)',
      'CREATE INDEX IF NOT EXISTS idx_desktop_clients_status ON desktop_clients (status)',
      'CREATE INDEX IF NOT EXISTS idx_client_commands_client_id ON client_commands (client_id)',
      'CREATE INDEX IF NOT EXISTS idx_client_commands_status ON client_commands (status)',
      'CREATE INDEX IF NOT EXISTS idx_sync_tasks_client_id ON sync_tasks (client_id)'
    ];
    
    for (const index of indexes) {
      try {
        await client.query(index);
      } catch (error) {
        if (!error.message.includes('already exists')) {
          console.log(`⚠️ Index creation warning: ${error.message}`);
        }
      }
    }
    
    console.log('✅ Indexes created');
    
    // Test queries
    console.log('\n6. 🧪 Testing queries...');
    
    // Test desktop_clients table
    const clientsCount = await client.query('SELECT COUNT(*) as count FROM desktop_clients');
    console.log(`✅ desktop_clients table: ${clientsCount.rows[0].count} clients`);
    
    // Test client_commands table
    const commandsCount = await client.query('SELECT COUNT(*) as count FROM client_commands');
    console.log(`✅ client_commands table: ${commandsCount.rows[0].count} commands`);
    
    // Test sync_tasks table
    const tasksResult = await client.query(`
      SELECT COUNT(*) as count, 
             COUNT(client_id) as with_client_id 
      FROM sync_tasks
    `);
    console.log(`✅ sync_tasks table: ${tasksResult.rows[0].count} tasks, ${tasksResult.rows[0].with_client_id} with client_id`);
    
    client.release();
    
    console.log('\n📊 POSTGRESQL CLIENT TABLES SUMMARY:');
    console.log('   Connection: ✅ SUCCESS');
    console.log('   desktop_clients table: ✅ CREATED');
    console.log('   client_commands table: ✅ CREATED');
    console.log('   sync_tasks.client_id: ✅ ADDED');
    console.log('   Indexes: ✅ CREATED');
    console.log('   Test queries: ✅ WORKING');
    
    console.log('\n🎯 NEXT STEPS:');
    console.log('   1. Start web server: npm run start-web');
    console.log('   2. Test web API: npm run test-web');
    console.log('   3. Start desktop app: npm run start-desktop');
    console.log('   4. Test integration: npm run test-web');
    
  } catch (error) {
    console.error('❌ Failed to create client tables:', error.message);
    console.error('Stack:', error.stack);
    
    console.log('\n🔧 TROUBLESHOOTING:');
    console.log('   1. Check PostgreSQL is running on *************:5432');
    console.log('   2. Verify credentials: user=pi, password=ubuntu');
    console.log('   3. Check database exists: syncmasterpro');
    console.log('   4. Test connection: npm run test-pg');
    
  } finally {
    await pool.end();
  }
}

// Auto-run if called directly
if (require.main === module) {
  createClientTables().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { createClientTables };

console.log('🐘 PostgreSQL Client Tables Creator loaded!');
console.log('📝 Run: node scripts/create-pg-client-tables.js');
