// Debug script for today-only sync
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const baseURL = 'http://localhost:5002/api';

async function debugTodayOnly() {
  console.log('🔍 Debug Today-Only Sync\n');
  
  try {
    // Login
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    const headers = { 'Authorization': `Bearer ${token}` };
    
    // Create simple test
    const testDir = path.join(process.cwd(), 'debug-today');
    const sourceDir = path.join(testDir, 'source');
    const destDir = path.join(testDir, 'dest');
    
    // Create directories
    [testDir, sourceDir, destDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
    
    // Create test file
    const testFile = path.join(sourceDir, 'test-today.txt');
    fs.writeFileSync(testFile, `Test file created at ${new Date().toISOString()}`);
    
    // Check file stats
    const stats = fs.statSync(testFile);
    console.log('📊 Test file stats:');
    console.log(`   📅 birthtime: ${stats.birthtime.toISOString()}`);
    console.log(`   📅 mtime: ${stats.mtime.toISOString()}`);
    console.log(`   📅 ctime: ${stats.ctime.toISOString()}`);
    
    // Check if it's today
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);
    
    const createdToday = stats.birthtime >= todayStart && stats.birthtime < todayEnd;
    const modifiedToday = stats.mtime >= todayStart && stats.mtime < todayEnd;
    
    console.log('\n📊 Today check:');
    console.log(`   📅 Today start: ${todayStart.toISOString()}`);
    console.log(`   📅 Today end: ${todayEnd.toISOString()}`);
    console.log(`   ✅ Created today: ${createdToday}`);
    console.log(`   ✅ Modified today: ${modifiedToday}`);
    
    // Create task
    const taskData = {
      name: 'Debug Today Only',
      sourcePath: sourceDir,
      destinationPath: destDir,
      syncType: 'today-only',
      options: {
        deleteExtraFiles: false,
        preserveTimestamps: true
      }
    };
    
    const createResponse = await axios.post(`${baseURL}/sync/tasks`, taskData, { headers });
    const taskId = createResponse.data.task.id;
    console.log(`\n✅ Created task ID: ${taskId}`);
    
    // Start sync
    console.log('\n🔄 Starting sync...');
    console.log('💡 Check server console for today-only filtering logs');
    
    await axios.post(`${baseURL}/sync/tasks/${taskId}/start`, {}, { headers });
    
    // Wait
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check result
    const destFile = path.join(destDir, 'test-today.txt');
    const synced = fs.existsSync(destFile);
    
    console.log(`\n📊 Result: File synced = ${synced ? '✅ YES' : '❌ NO'}`);
    
    if (synced) {
      console.log('🎉 Today-only sync is working!');
    } else {
      console.log('🔧 Today-only sync needs debugging');
      console.log('💡 Check server logs for:');
      console.log('   - "📅 Today-only sync: filtering files"');
      console.log('   - "📅 Including today\'s file" or "⏭️ Skipping file"');
      console.log('   - "📊 Today-only sync: X out of Y files qualify"');
    }
    
    // Cleanup
    await axios.delete(`${baseURL}/sync/tasks/${taskId}`, { headers });
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugTodayOnly();
