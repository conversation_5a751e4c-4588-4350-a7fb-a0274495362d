const axios = require('axios');

async function testLogoutProcess() {
  console.log('🧪 Testing Logout Process Debug\n');

  try {
    // Test 1: Check if desktop server is running
    console.log('1. 🔍 Checking Desktop Server Status...');
    try {
      const healthResponse = await axios.get('http://localhost:5002/api/health', { timeout: 5000 });
      console.log('   ✅ Desktop Server Running:', healthResponse.data.status);
    } catch (error) {
      console.log('   ❌ Desktop Server Not Running:', error.message);
      return;
    }

    // Test 2: Check environment variables
    console.log('\n2. 🔍 Checking Environment Variables...');
    console.log('   ELECTRON_ENV:', process.env.ELECTRON_ENV);
    console.log('   NODE_ENV:', process.env.NODE_ENV);
    console.log('   PORT:', process.env.PORT);

    // Test 3: Check if we can get client list
    console.log('\n3. 🔍 Checking Client List...');
    try {
      // First login to get token
      const loginResponse = await axios.post('http://localhost:5002/api/auth/login', {
        email: '<EMAIL>',
        password: 'password123'
      });

      const token = loginResponse.data.token;
      console.log('   ✅ Login successful, token received');

      // Get client list
      const clientsResponse = await axios.get('http://localhost:5002/api/clients', {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log('   📋 Current Clients:');
      clientsResponse.data.clients.forEach(client => {
        console.log(`      - ${client.client_id}: ${client.status} (${client.hostname})`);
      });

      // Test 4: Perform logout and check logs
      console.log('\n4. 🚪 Testing Logout Process...');
      console.log('   📝 Watch server console for debug logs...');
      
      const logoutResponse = await axios.post('http://localhost:5002/api/auth/logout', {}, {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log('   ✅ Logout API Response:', logoutResponse.data.message);

      // Wait a moment for async operations
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Test 5: Check client status after logout
      console.log('\n5. 🔍 Checking Client Status After Logout...');
      
      // Login again to check status
      const loginResponse2 = await axios.post('http://localhost:5002/api/auth/login', {
        email: '<EMAIL>',
        password: 'password123'
      });

      const token2 = loginResponse2.data.token;
      
      const clientsResponse2 = await axios.get('http://localhost:5002/api/clients', {
        headers: { Authorization: `Bearer ${token2}` }
      });

      console.log('   📋 Clients After Logout:');
      clientsResponse2.data.clients.forEach(client => {
        console.log(`      - ${client.client_id}: ${client.status} (${client.hostname})`);
        if (client.status === 'offline') {
          console.log('      ✅ Client correctly marked as offline');
        } else {
          console.log('      ❌ Client still showing as online - ISSUE FOUND!');
        }
      });

    } catch (error) {
      console.log('   ❌ API Test Failed:', error.message);
      if (error.response) {
        console.log('   📄 Response:', error.response.status, error.response.data);
      }
    }

    console.log('\n📋 SUMMARY:');
    console.log('   🔍 Check server console logs for detailed debug information');
    console.log('   📊 Look for these debug messages:');
    console.log('      - "🚪 Processing logout request for user:"');
    console.log('      - "🔍 Environment check - ELECTRON_ENV:"');
    console.log('      - "🔍 ExtendedSyncEngine available:"');
    console.log('      - "🔍 ClientManager available:"');
    console.log('      - "🚪 Calling client manager logout for user:"');
    console.log('      - "🚪 Logging out client..."');
    console.log('      - "📴 Client marked as offline on web server"');

    console.log('\n💡 TROUBLESHOOTING:');
    console.log('   1. If ExtendedSyncEngine not available → Check server initialization');
    console.log('   2. If ClientManager not available → Check user login process');
    console.log('   3. If logout API fails → Check client offline endpoint');
    console.log('   4. If socket disconnect not working → Check socket handlers');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run test
testLogoutProcess();
