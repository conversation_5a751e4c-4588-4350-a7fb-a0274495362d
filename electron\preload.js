const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File system operations
  selectFolder: () => ipcRenderer.invoke('select-folder'),
  
  // App information
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // Store operations
  store: {
    get: (key) => ipcRenderer.invoke('store-get', key),
    set: (key, value) => ipcRenderer.invoke('store-set', key, value),
    delete: (key) => ipcRenderer.invoke('store-delete', key)
  },
  
  // Startup settings
  startup: {
    getSettings: () => ipcRenderer.invoke('get-startup-settings'),
    setStartupWithWindows: (enable) => ipcRenderer.invoke('set-startup-with-windows', enable),
    setMinimizeToTray: (enable) => ipcRenderer.invoke('set-minimize-to-tray', enable),
    setCloseToTray: (enable) => ipcRenderer.invoke('set-close-to-tray', enable),
    setStartMinimized: (enable) => ipcRenderer.invoke('set-start-minimized', enable)
  },

  // Tray controls
  tray: {
    showMainWindow: () => ipcRenderer.invoke('show-main-window'),
    hideToTray: () => ipcRenderer.invoke('hide-to-tray')
  },

  // Menu events
  onMenuEvent: (callback) => {
    ipcRenderer.on('menu-new-sync', callback);
    ipcRenderer.on('menu-settings', callback);
    ipcRenderer.on('menu-start-all-sync', callback);
    ipcRenderer.on('menu-stop-all-sync', callback);
    ipcRenderer.on('menu-sync-history', callback);
    // Tray events
    ipcRenderer.on('tray-start-all-sync', callback);
    ipcRenderer.on('tray-stop-all-sync', callback);
    ipcRenderer.on('tray-open-settings', callback);
  },

  // Remove listeners
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
});

// Platform information
contextBridge.exposeInMainWorld('platform', {
  isWindows: process.platform === 'win32',
  isMac: process.platform === 'darwin',
  isLinux: process.platform === 'linux'
});
