# ⚡ Hướng dẫn sử dụng Real-time Sync

## 🎯 Tổng quan

**Real-time Sync** trong SyncMasterPro là tính năng đồng bộ tức thời theo dõi thay đổi file/folder và tự động sync ngay khi có thay đổi, <PERSON>h<PERSON><PERSON> cần can thiệp thủ công.

## 🔧 Cách hoạt động

### Tr<PERSON><PERSON><PERSON> đâ<PERSON> (Vấn đề)
- ❌ Chỉ có scheduled sync (đồng bộ theo khoảng thời gian)
- ❌ Không theo dõi thay đổi file real-time
- ❌ Phải chờ đến chu kỳ sync tiếp theo

### Bây giờ (Giải pháp)
- ✅ **File Watcher** (Chokidar) theo dõi thay đổi file
- ✅ **Instant Sync** khi có thay đổi
- ✅ **Real-time UI Updates** qua Socket.IO
- ✅ **Persistent Monitoring** liên tục

## 🚀 Cách sử dụng

### 1. Tạo Real-time Sync Task

```
1. Mở SyncMasterPro
2. Click "Create New Task"
3. Chọn Sync Type: "Real-time Sync"
4. Nhập Source và Destination paths
5. Cấu hình filters (optional)
6. Click "Create Task"
```

### 2. Bắt đầu Real-time Monitoring

```
1. Tìm task vừa tạo trong danh sách
2. Click nút "Start" (▶️)
3. Task status sẽ chuyển thành "Monitoring"
4. File watcher bắt đầu theo dõi source folder
```

### 3. Test Real-time Sync

```
1. Mở source folder trong File Explorer
2. Tạo file mới hoặc sửa file existing
3. Quan sát UI - sẽ thấy:
   - Task status chuyển "Syncing" khi có thay đổi
   - Progress updates real-time
   - Notification khi sync complete
   - Task status về "Monitoring"
4. Kiểm tra destination folder - file đã được sync
```

## 🎮 Các trạng thái Task

### 📊 Task Status
- **`idle`** - Task đã tạo nhưng chưa start
- **`monitoring`** - Đang theo dõi file changes
- **`syncing`** - Đang xử lý sync files
- **`error`** - Có lỗi xảy ra

### 🔄 Workflow Status
```
idle → monitoring → syncing → monitoring → syncing → ...
```

## 📡 Real-time Events

### Socket.IO Events
- `realtime-sync-started` - Bắt đầu monitoring
- `file-change-detected` - Phát hiện file thay đổi
- `realtime-sync-processing` - Đang xử lý sync
- `realtime-sync-completed` - Hoàn thành sync batch
- `realtime-sync-stopped` - Dừng monitoring
- `realtime-sync-error` - Lỗi sync

### File System Events
- `add` - File/folder mới được tạo
- `change` - File được sửa đổi
- `unlink` - File bị xóa
- `addDir` - Folder mới được tạo
- `unlinkDir` - Folder bị xóa

## ⚙️ Cấu hình Advanced

### Debounce Time
- **Mặc định**: 2000ms (2 giây)
- **Mục đích**: Tránh sync quá nhiều lần khi có nhiều file changes liên tiếp

### File Filters
```javascript
// Include patterns
["*.txt", "*.jpg", "*.pdf"]

// Exclude patterns  
["!*.tmp", "!*.log", "!node_modules/**"]
```

### Ignore Patterns (Tự động)
- `**/node_modules/**`
- `**/.git/**`
- `**/.DS_Store`
- `**/Thumbs.db`
- `**/*.tmp`
- `**/*.temp`

## 🧪 Testing & Debugging

### 1. Chạy Test Script
```javascript
// Trong browser console (F12)
window.realtimeSyncTester.runTests();
```

### 2. Monitor Events
```javascript
// Monitor real-time events
window.realtimeSyncTester.monitorEvents();
```

### 3. Check Status
```javascript
// Kiểm tra trạng thái real-time sync
window.realtimeSyncTester.checkStatus();
```

### 4. Manual File Test
```
1. Tạo folder test: C:\Test\Source và C:\Test\Destination
2. Tạo real-time sync task
3. Start monitoring
4. Tạo/sửa file trong Source folder
5. Kiểm tra Destination folder
```

## 🔍 Troubleshooting

### Vấn đề thường gặp

#### 1. Real-time sync không hoạt động
```
Nguyên nhân: Server chưa khởi động hoặc Socket.IO không kết nối
Giải pháp: 
- Kiểm tra server đang chạy
- Refresh browser để reconnect Socket.IO
- Check console logs
```

#### 2. File changes không được detect
```
Nguyên nhân: Path không tồn tại hoặc không có quyền access
Giải pháp:
- Kiểm tra source path có tồn tại
- Đảm bảo có quyền read/write
- Kiểm tra file không bị lock
```

#### 3. Sync chậm hoặc lag
```
Nguyên nhân: Quá nhiều files hoặc files quá lớn
Giải pháp:
- Sử dụng filters để exclude files không cần thiết
- Tăng debounce time
- Chia nhỏ sync tasks
```

#### 4. UI không update real-time
```
Nguyên nhân: Socket.IO connection bị mất
Giải pháp:
- Check network connection
- Refresh page để reconnect
- Kiểm tra server logs
```

## 📊 Performance Tips

### 1. Optimize File Watching
- Chỉ watch folders cần thiết
- Sử dụng ignore patterns hiệu quả
- Tránh watch folders có quá nhiều files

### 2. Network Optimization
- Đảm bảo connection ổn định
- Sử dụng local network khi có thể
- Monitor bandwidth usage

### 3. System Resources
- Real-time sync sử dụng CPU và RAM
- Monitor system performance
- Tạm dừng khi cần thiết

## 🎯 Best Practices

### 1. Folder Structure
```
✅ Good:
- C:\Documents\Projects
- D:\Backup\Documents

❌ Avoid:
- C:\ (root drive)
- System folders
- Network drives (có thể chậm)
```

### 2. File Types
```
✅ Suitable:
- Documents (.txt, .pdf, .docx)
- Images (.jpg, .png, .gif)
- Code files (.js, .py, .html)

⚠️ Be careful:
- Large files (>100MB)
- Database files
- Video files
```

### 3. Sync Patterns
```
✅ Recommended:
- One-way sync for backups
- Bidirectional for shared folders
- Real-time for critical documents

❌ Avoid:
- Circular sync paths
- Overlapping sync tasks
- System file sync
```

## 🔮 Future Enhancements

- [ ] **Conflict Resolution** - Auto-resolve file conflicts
- [ ] **Bandwidth Throttling** - Limit sync speed
- [ ] **Priority Queuing** - Prioritize certain file types
- [ ] **Smart Batching** - Intelligent grouping of changes
- [ ] **Offline Queue** - Queue changes when offline
- [ ] **Sync Analytics** - Detailed sync statistics

## 📞 Support

Nếu gặp vấn đề với Real-time Sync:

1. **Check Console Logs** - Mở F12 và xem console
2. **Run Test Script** - Sử dụng test script để diagnose
3. **Check Server Logs** - Xem server console logs
4. **Restart Services** - Restart server và refresh browser

Real-time Sync giờ đây hoạt động đúng như mong đợi! 🚀
