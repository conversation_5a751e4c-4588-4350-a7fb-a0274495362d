const Database = require('better-sqlite3');
const bcrypt = require('bcryptjs');
const path = require('path');

async function createSQLiteAdmin() {
  console.log('📁 Creating Admin User in SQLite...');

  const dbPath = path.join(__dirname, '../data/desktop-syncmasterpro.db');
  console.log('📍 Database path:', dbPath);

  try {
    const db = new Database(dbPath);
    console.log('✅ Connected to SQLite database');

    // Check if admin user exists
    const existingUser = db.prepare('SELECT id, email FROM users WHERE email = ?').get('<EMAIL>');

    if (existingUser) {
      console.log('ℹ️ Admin user already exists:', existingUser);
    } else {
      // Hash password
      const hashedPassword = await bcrypt.hash('admin123', 12);
      
      // Create admin user
      const insertUser = db.prepare(`
        INSERT INTO users (email, password, name, avatar, settings, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
      `);
      
      const result = insertUser.run(
        '<EMAIL>',
        hashedPassword,
        'Admin User',
        null,
        '{}'
      );

      console.log('✅ Admin user created successfully:');
      console.log('📊 User ID:', result.lastInsertRowid);
      console.log('📧 Email: <EMAIL>');
      console.log('👤 Name: Admin User');
    }

    // Verify user exists
    const verifyUser = db.prepare('SELECT id, email, name, created_at FROM users WHERE email = ?').get('<EMAIL>');

    if (verifyUser) {
      console.log('\n✅ Verification successful:');
      console.log('📊 User found:', verifyUser);
    } else {
      console.log('\n❌ Verification failed: User not found');
    }

    // Check all users
    const allUsers = db.prepare('SELECT id, email, name FROM users').all();
    console.log('\n📋 All users in SQLite database:');
    allUsers.forEach(user => {
      console.log(`  - ID: ${user.id}, Email: ${user.email}, Name: ${user.name}`);
    });

    db.close();

    console.log('\n🎉 SQLite admin setup complete!');
    console.log('📋 Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    console.log('🖥️ Desktop app should now be able to login');

  } catch (error) {
    console.log('\n❌ SQLite error:');
    console.log('Error type:', error.constructor.name);
    console.log('Error message:', error.message);
  }
}

createSQLiteAdmin();
