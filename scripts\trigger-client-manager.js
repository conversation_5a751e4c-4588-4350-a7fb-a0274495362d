#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to trigger ClientManager manually
 * This will simulate a login event to force ClientManager initialization
 */

const axios = require('axios');

console.log('🔧 Triggering ClientManager Initialization');
console.log('==========================================');

async function simulateLogin() {
  console.log('\n1. Simulating login to trigger ClientManager...');
  
  try {
    // Simulate login request to desktop server
    const loginData = {
      email: '<EMAIL>',
      password: 'admin'
    };
    
    const response = await axios.post('http://localhost:5002/api/auth/login', loginData, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Login Response Status:', response.status);
    console.log('📝 Login Response:', response.data);
    
    if (response.status === 200 && response.data.token) {
      console.log('✅ Login successful! This should trigger ClientManager.');
      return response.data.token;
    } else {
      console.log('⚠️ Login response indicates potential issue');
      return null;
    }
  } catch (error) {
    console.log('❌ Login Error:', error.message);
    return null;
  }
}

async function checkClientManagerStatus() {
  console.log('\n2. Checking if ClientManager is active...');
  
  try {
    // Check if there are any client registration attempts
    const response = await axios.get('http://localhost:5001/api/clients', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📊 Clients API Status:', response.status);
    
    if (response.status === 200) {
      console.log('📊 Current Clients:', response.data);
      console.log('📊 Total Clients:', response.data.length);
      
      if (response.data.length > 0) {
        console.log('✅ Found registered clients! ClientManager is working.');
        return true;
      } else {
        console.log('⚠️ No clients registered yet.');
        return false;
      }
    } else {
      console.log('📊 Clients API Response:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Clients Check Error:', error.message);
    return false;
  }
}

async function checkDesktopServerHealth() {
  console.log('\n3. Checking Desktop Server Health...');
  
  try {
    // Try to access any endpoint to see if server is responsive
    const response = await axios.get('http://localhost:5002/api/sync-tasks', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('🏥 Desktop Server Status:', response.status);
    
    if (response.status === 401) {
      console.log('✅ Desktop server is responsive (requires auth)');
      return true;
    } else if (response.status === 200) {
      console.log('✅ Desktop server is responsive');
      console.log('📋 Response:', response.data);
      return true;
    } else {
      console.log('⚠️ Desktop server returned:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Desktop Server Health Error:', error.message);
    return false;
  }
}

async function checkWebServerHealth() {
  console.log('\n4. Checking Web Server Health...');
  
  try {
    const response = await axios.get('http://localhost:5001/api/clients', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('🌐 Web Server Status:', response.status);
    
    if (response.status === 401 || response.status === 200) {
      console.log('✅ Web server is responsive');
      return true;
    } else {
      console.log('⚠️ Web server returned:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Web Server Health Error:', error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting ClientManager trigger test...\n');
    
    // Check server health first
    const desktopHealthy = await checkDesktopServerHealth();
    const webHealthy = await checkWebServerHealth();
    
    if (!desktopHealthy) {
      console.log('\n❌ Desktop server is not responding. Please check if it\'s running.');
      return;
    }
    
    if (!webHealthy) {
      console.log('\n❌ Web server is not responding. Please check if it\'s running.');
      return;
    }
    
    // Try to trigger ClientManager via login
    const token = await simulateLogin();
    
    // Wait a moment for ClientManager to initialize
    if (token) {
      console.log('\n⏳ Waiting 5 seconds for ClientManager to initialize...');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    // Check if ClientManager is now active
    const clientManagerActive = await checkClientManagerStatus();
    
    console.log('\n📊 Test Summary:');
    console.log('================');
    console.log('- Desktop Server:', desktopHealthy ? '✅ Healthy' : '❌ Not responding');
    console.log('- Web Server:', webHealthy ? '✅ Healthy' : '❌ Not responding');
    console.log('- Login Attempt:', token ? '✅ Successful' : '❌ Failed');
    console.log('- ClientManager:', clientManagerActive ? '✅ Active' : '⚠️ Not detected');
    
    if (clientManagerActive) {
      console.log('\n🎉 ClientManager is working! Web management should show real data.');
    } else {
      console.log('\n💡 ClientManager may not be triggered yet. Try:');
      console.log('1. Login to the desktop app UI');
      console.log('2. Check if React dev server is running on port 3000');
      console.log('3. Look for ClientManager logs in the desktop server console');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { simulateLogin, checkClientManagerStatus };
