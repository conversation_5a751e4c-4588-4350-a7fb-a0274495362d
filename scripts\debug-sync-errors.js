// Debug script to analyze sync errors
const Database = require('better-sqlite3');
const { Pool } = require('pg');
const path = require('path');

async function debugSyncErrors() {
  console.log('🔍 Debugging Database Sync Errors\n');
  
  let sqlite = null;
  let pgPool = null;
  
  try {
    // Initialize connections
    console.log('1. 🔌 Connecting to databases...');
    
    // SQLite connection
    const sqlitePath = path.join(__dirname, '../data/syncmasterpro.db');
    sqlite = new Database(sqlitePath);
    console.log('✅ SQLite connected');
    
    // PostgreSQL connection
    pgPool = new Pool({
      user: process.env.PG_USER || 'pi',
      host: process.env.PG_HOST || '*************',
      database: process.env.PG_DATABASE || 'syncmasterpro',
      password: process.env.PG_PASSWORD || 'ubuntu',
      port: process.env.PG_PORT || 5432,
    });
    
    await pgPool.query('SELECT NOW()');
    console.log('✅ PostgreSQL connected');
    
    // 2. Compare schemas
    console.log('\n2. 📋 Comparing table schemas...');
    
    const tables = ['users', 'sync_history', 'sync_tasks'];
    
    for (const table of tables) {
      console.log(`\n📊 Table: ${table}`);
      console.log('=' .repeat(50));
      
      // Get SQLite schema
      console.log('\n🗄️ SQLite Schema:');
      const sqliteSchema = sqlite.prepare(`PRAGMA table_info(${table})`).all();
      sqliteSchema.forEach(col => {
        console.log(`   ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : 'NULL'} ${col.pk ? 'PRIMARY KEY' : ''}`);
      });
      
      // Get PostgreSQL schema
      console.log('\n🐘 PostgreSQL Schema:');
      try {
        const pgSchema = await pgPool.query(`
          SELECT column_name, data_type, is_nullable, column_default
          FROM information_schema.columns 
          WHERE table_name = $1 
          ORDER BY ordinal_position
        `, [table]);
        
        pgSchema.rows.forEach(col => {
          console.log(`   ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
        });
      } catch (error) {
        console.log(`   ❌ Table ${table} not found in PostgreSQL`);
      }
      
      // Compare column differences
      console.log('\n🔍 Schema Differences:');
      const sqliteColumns = sqliteSchema.map(col => ({
        name: col.name,
        type: col.type.toLowerCase(),
        nullable: !col.notnull
      }));
      
      try {
        const pgSchema = await pgPool.query(`
          SELECT column_name, data_type, is_nullable
          FROM information_schema.columns 
          WHERE table_name = $1
        `, [table]);
        
        const pgColumns = pgSchema.rows.map(col => ({
          name: col.column_name,
          type: col.data_type.toLowerCase(),
          nullable: col.is_nullable === 'YES'
        }));
        
        // Find missing columns
        const missingInPg = sqliteColumns.filter(sqlCol => 
          !pgColumns.find(pgCol => pgCol.name === sqlCol.name)
        );
        
        const missingInSqlite = pgColumns.filter(pgCol => 
          !sqliteColumns.find(sqlCol => sqlCol.name === pgCol.name)
        );
        
        if (missingInPg.length > 0) {
          console.log('   ❌ Missing in PostgreSQL:');
          missingInPg.forEach(col => console.log(`      - ${col.name} (${col.type})`));
        }
        
        if (missingInSqlite.length > 0) {
          console.log('   ❌ Missing in SQLite:');
          missingInSqlite.forEach(col => console.log(`      - ${col.name} (${col.type})`));
        }
        
        // Find type differences
        const typeDiffs = sqliteColumns.filter(sqlCol => {
          const pgCol = pgColumns.find(pgCol => pgCol.name === sqlCol.name);
          return pgCol && !isCompatibleType(sqlCol.type, pgCol.type);
        });
        
        if (typeDiffs.length > 0) {
          console.log('   ⚠️ Type differences:');
          typeDiffs.forEach(sqlCol => {
            const pgCol = pgColumns.find(pgCol => pgCol.name === sqlCol.name);
            console.log(`      - ${sqlCol.name}: SQLite(${sqlCol.type}) vs PostgreSQL(${pgCol.type})`);
          });
        }
        
        if (missingInPg.length === 0 && missingInSqlite.length === 0 && typeDiffs.length === 0) {
          console.log('   ✅ Schemas are compatible');
        }
        
      } catch (error) {
        console.log(`   ❌ Could not compare schemas: ${error.message}`);
      }
    }
    
    // 3. Check actual data and constraints
    console.log('\n3. 📊 Checking data and constraints...');
    
    // Check users table specifically
    console.log('\n👥 Users Table Analysis:');
    
    // Get SQLite users (with all columns for proper sync testing)
    const sqliteUsers = sqlite.prepare('SELECT * FROM users').all();
    console.log(`📊 SQLite users count: ${sqliteUsers.length}`);

    if (sqliteUsers.length > 0) {
      console.log('📋 SQLite users:');
      sqliteUsers.forEach(user => {
        console.log(`   ID: ${user.id}, Email: ${user.email}, Name: ${user.name}`);
        console.log(`   Has password: ${user.password ? 'YES' : 'NO'}, Has settings: ${user.settings ? 'YES' : 'NO'}`);
      });
    }

    // Get PostgreSQL users
    try {
      const pgUsers = await pgPool.query('SELECT * FROM users');
      console.log(`📊 PostgreSQL users count: ${pgUsers.rows.length}`);

      if (pgUsers.rows.length > 0) {
        console.log('📋 PostgreSQL users:');
        pgUsers.rows.forEach(user => {
          console.log(`   ID: ${user.id}, Email: ${user.email}, Name: ${user.name}`);
          console.log(`   Has password: ${user.password ? 'YES' : 'NO'}, Has settings: ${user.settings ? 'YES' : 'NO'}`);
        });
      }
      
      // Check for email conflicts
      const emailConflicts = sqliteUsers.filter(sqliteUser => 
        pgUsers.rows.find(pgUser => pgUser.email === sqliteUser.email && pgUser.id !== sqliteUser.id)
      );
      
      if (emailConflicts.length > 0) {
        console.log('❌ Email conflicts found:');
        emailConflicts.forEach(user => {
          console.log(`   - Email: ${user.email} exists with different IDs`);
        });
      } else {
        console.log('✅ No email conflicts detected');
      }
      
    } catch (error) {
      console.log(`❌ Could not query PostgreSQL users: ${error.message}`);
    }
    
    // 4. Check sync_history table
    console.log('\n📜 Sync History Analysis:');
    
    const sqliteHistory = sqlite.prepare('SELECT COUNT(*) as count FROM sync_history').get();
    console.log(`📊 SQLite sync_history count: ${sqliteHistory.count}`);
    
    if (sqliteHistory.count > 0) {
      const sampleHistory = sqlite.prepare('SELECT * FROM sync_history LIMIT 3').all();
      console.log('📋 Sample SQLite sync_history records:');
      sampleHistory.forEach(record => {
        console.log(`   ID: ${record.id}, Task: ${record.task_id}, Status: ${record.status}`);
        console.log(`   Columns: ${Object.keys(record).join(', ')}`);
      });
    }
    
    try {
      const pgHistory = await pgPool.query('SELECT COUNT(*) as count FROM sync_history');
      console.log(`📊 PostgreSQL sync_history count: ${pgHistory.rows[0].count}`);
      
      if (pgHistory.rows[0].count > 0) {
        const samplePgHistory = await pgPool.query('SELECT * FROM sync_history LIMIT 3');
        console.log('📋 Sample PostgreSQL sync_history records:');
        samplePgHistory.rows.forEach(record => {
          console.log(`   ID: ${record.id}, Task: ${record.task_id}, Status: ${record.status}`);
          console.log(`   Columns: ${Object.keys(record).join(', ')}`);
        });
      }
    } catch (error) {
      console.log(`❌ Could not query PostgreSQL sync_history: ${error.message}`);
    }
    
    // 5. Test manual sync with detailed error logging
    console.log('\n5. 🧪 Testing sync with detailed error logging...');
    
    try {
      // Test syncing a single user record
      if (sqliteUsers.length > 0) {
        const testUser = sqliteUsers[0];
        console.log(`🧪 Testing sync for user: ${testUser.email}`);
        
        try {
          const columns = Object.keys(testUser);
          const values = Object.values(testUser);
          const placeholders = values.map((_, i) => `$${i + 1}`).join(', ');
          
          const query = `
            INSERT INTO users (${columns.join(', ')})
            VALUES (${placeholders})
            ON CONFLICT (email) DO UPDATE SET
              name = EXCLUDED.name,
              updated_at = CURRENT_TIMESTAMP
          `;
          
          await pgPool.query(query, values);
          console.log('✅ User sync test successful');
          
        } catch (syncError) {
          console.log('❌ User sync test failed:', syncError.message);
          console.log('🔍 Error details:', {
            code: syncError.code,
            detail: syncError.detail,
            constraint: syncError.constraint
          });
        }
      }
      
    } catch (error) {
      console.log('❌ Sync test failed:', error.message);
    }
    
    console.log('\n📊 SYNC ERROR ANALYSIS COMPLETE');
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    // Close connections
    if (sqlite) {
      sqlite.close();
      console.log('🔌 SQLite connection closed');
    }
    
    if (pgPool) {
      await pgPool.end();
      console.log('🔌 PostgreSQL connection closed');
    }
  }
}

// Helper function to check type compatibility
function isCompatibleType(sqliteType, pgType) {
  const typeMap = {
    'integer': ['integer', 'bigint', 'serial', 'bigserial'],
    'text': ['text', 'varchar', 'character varying'],
    'real': ['real', 'double precision', 'numeric'],
    'blob': ['bytea'],
    'datetime': ['timestamp', 'timestamp without time zone', 'timestamp with time zone']
  };
  
  const sqliteLower = sqliteType.toLowerCase();
  const pgLower = pgType.toLowerCase();
  
  // Direct match
  if (sqliteLower === pgLower) return true;
  
  // Check compatibility map
  for (const [sqliteBase, pgTypes] of Object.entries(typeMap)) {
    if (sqliteLower.includes(sqliteBase) && pgTypes.some(pgT => pgLower.includes(pgT))) {
      return true;
    }
  }
  
  return false;
}

// Export for manual use
module.exports = { debugSyncErrors };

// Auto-run if called directly
if (require.main === module) {
  debugSyncErrors();
}

console.log('🔍 Sync Error Debugger loaded!');
console.log('📝 Run: node scripts/debug-sync-errors.js');
