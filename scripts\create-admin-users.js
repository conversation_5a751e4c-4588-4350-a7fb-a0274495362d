const axios = require('axios');

async function createAdminUsers() {
  console.log('👤 Creating Admin Users for Both Databases...');

  const webURL = 'http://localhost:5000/api';
  const desktopURL = 'http://localhost:5002/api';
  
  const adminData = {
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Admin User'
  };

  try {
    // Create admin user for Web Server (PostgreSQL)
    console.log('\n🌐 Creating admin user for Web Server (PostgreSQL):');
    try {
      const webRegisterResponse = await axios.post(`${webURL}/auth/register`, adminData);
      console.log('✅ Web admin user created successfully');
      console.log('📊 User ID:', webRegisterResponse.data.user.id);
      console.log('📧 Email:', webRegisterResponse.data.user.email);
    } catch (error) {
      if (error.response?.status === 409) {
        console.log('ℹ️ Web admin user already exists');
      } else {
        console.log('❌ Web admin creation error:', error.response?.data?.error || error.message);
      }
    }

    // Create admin user for Desktop Server (SQLite)
    console.log('\n🖥️ Creating admin user for Desktop Server (SQLite):');
    try {
      const desktopRegisterResponse = await axios.post(`${desktopURL}/auth/register`, adminData);
      console.log('✅ Desktop admin user created successfully');
      console.log('📊 User ID:', desktopRegisterResponse.data.user.id);
      console.log('📧 Email:', desktopRegisterResponse.data.user.email);
    } catch (error) {
      if (error.response?.status === 409) {
        console.log('ℹ️ Desktop admin user already exists');
      } else {
        console.log('❌ Desktop admin creation error:', error.response?.data?.error || error.message);
      }
    }

    // Test login for both servers
    console.log('\n🔐 Testing Login for Both Servers:');
    
    // Test Web login
    try {
      const webLoginResponse = await axios.post(`${webURL}/auth/login`, {
        email: adminData.email,
        password: adminData.password
      });
      console.log('✅ Web login successful');
      console.log('🎫 Web token length:', webLoginResponse.data.token.length);
    } catch (error) {
      console.log('❌ Web login failed:', error.response?.data?.error || error.message);
    }

    // Test Desktop login
    try {
      const desktopLoginResponse = await axios.post(`${desktopURL}/auth/login`, {
        email: adminData.email,
        password: adminData.password
      });
      console.log('✅ Desktop login successful');
      console.log('🎫 Desktop token length:', desktopLoginResponse.data.token.length);
    } catch (error) {
      console.log('❌ Desktop login failed:', error.response?.data?.error || error.message);
    }

    console.log('\n🎉 Admin User Setup Complete!');
    console.log('📋 Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    console.log('🌐 Web Server: http://localhost:5000 (PostgreSQL)');
    console.log('🖥️ Desktop Server: http://localhost:5002 (SQLite)');

  } catch (error) {
    console.log('\n❌ Error occurred:');
    console.log('Error type:', error.constructor.name);
    console.log('Error message:', error.message);
  }
}

createAdminUsers();
