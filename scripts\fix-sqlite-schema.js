#!/usr/bin/env node

/**
 * Fix SQLite schema to match PostgreSQL
 */

const Database = require('better-sqlite3');
const path = require('path');

console.log('🔧 Fixing SQLite Schema');
console.log('=======================');

async function fixSQLiteSchema() {
  console.log('\n1. 📊 Connecting to SQLite...');
  
  const sqlitePath = path.join(__dirname, '../data/syncmasterpro.db');
  const sqlite = new Database(sqlitePath);
  console.log('✅ SQLite connected:', sqlitePath);
  
  try {
    // Check existing tables
    console.log('\n2. 🔍 Checking existing tables...');
    const tables = sqlite.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `).all();
    
    console.log('📋 Existing tables:');
    tables.forEach(table => {
      console.log(`   - ${table.name}`);
    });
    
    // Check if clients table exists
    const clientsTableExists = tables.some(table => table.name === 'clients');
    
    if (!clientsTableExists) {
      console.log('\n3. 🔧 Creating clients table...');
      
      sqlite.exec(`
        CREATE TABLE IF NOT EXISTS clients (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          client_id TEXT UNIQUE NOT NULL,
          user_id INTEGER NOT NULL,
          hostname TEXT NOT NULL,
          platform TEXT NOT NULL,
          version TEXT,
          status TEXT DEFAULT 'offline',
          last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
          total_tasks INTEGER DEFAULT 0,
          active_tasks INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      console.log('✅ Clients table created');
    } else {
      console.log('\n3. ✅ Clients table already exists');
    }
    
    // Check if sessions table exists
    const sessionsTableExists = tables.some(table => table.name === 'sessions');
    
    if (!sessionsTableExists) {
      console.log('\n4. 🔧 Creating sessions table...');
      
      sqlite.exec(`
        CREATE TABLE IF NOT EXISTS sessions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          token TEXT UNIQUE NOT NULL,
          expires_at DATETIME NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      console.log('✅ Sessions table created');
    } else {
      console.log('\n4. ✅ Sessions table already exists');
    }
    
    // Update tables list
    const updatedTables = sqlite.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `).all();
    
    console.log('\n5. 📋 Updated tables:');
    updatedTables.forEach(table => {
      console.log(`   - ${table.name}`);
    });
    
    // Check table counts
    console.log('\n6. 📊 Table record counts:');
    for (const table of updatedTables) {
      try {
        const count = sqlite.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
        console.log(`   - ${table.name}: ${count.count} records`);
      } catch (error) {
        console.log(`   - ${table.name}: Error reading (${error.message})`);
      }
    }
    
    return true;
    
  } finally {
    sqlite.close();
  }
}

async function main() {
  try {
    console.log('🚀 Starting SQLite schema fix...\n');
    
    const success = await fixSQLiteSchema();
    
    if (success) {
      console.log('\n🎉 SUCCESS! SQLite schema updated!');
      console.log('📋 Changes made:');
      console.log('   ✅ Clients table created (if missing)');
      console.log('   ✅ Sessions table created (if missing)');
      console.log('   ✅ Schema now matches PostgreSQL');
      console.log('');
      console.log('💡 Next steps:');
      console.log('   1. Run database sync to sync data');
      console.log('   2. Desktop app will now track clients properly');
      console.log('   3. Web manager will show consistent data');
    } else {
      console.log('\n❌ Schema fix failed');
    }
    
  } catch (error) {
    console.error('\n❌ Fix failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
