import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../contexts/NotificationContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useSettings } from '../../contexts/SettingsContext';
import {
  CogIcon,
  PaintBrushIcon,
  ArrowPathIcon,
  ShieldCheckIcon,
  InformationCircleIcon,
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
  CheckIcon,
  XMarkIcon,
  ArrowPathRoundedSquareIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import Toggle from '../UI/Toggle';

// API Base URL - Auto-detect environment (consistent with other contexts)
const isElectron = window.electronAPI !== undefined ||
                   window.platform !== undefined ||
                   navigator.userAgent.toLowerCase().indexOf('electron') > -1 ||
                   (window.process && window.process.versions && window.process.versions.electron);

const API_BASE_URL = isElectron
  ? 'http://localhost:5002/api'  // Desktop uses port 5002
  : (process.env.REACT_APP_API_URL || 'http://localhost:5000/api'); // Web uses port 5000

const Settings = () => {
  const { user, updateProfile } = useAuth();
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState('general');

  const tabs = [
    { id: 'general', name: t('general'), icon: CogIcon },
    { id: 'appearance', name: t('appearance'), icon: PaintBrushIcon },
    { id: 'startup', name: t('startup'), icon: RocketLaunchIcon },
    { id: 'sync', name: t('syncSettings'), icon: ArrowPathIcon },
    { id: 'notifications', name: t('notifications'), icon: BellIcon },
    { id: 'security', name: t('security'), icon: ShieldCheckIcon },
    { id: 'about', name: t('about'), icon: InformationCircleIcon },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return <GeneralSettings user={user} updateProfile={updateProfile} />;
      case 'appearance':
        return <AppearanceSettings />;
      case 'startup':
        return <StartupSettings />;
      case 'sync':
        return <SyncSettings />;
      case 'notifications':
        return <NotificationSettings />;
      case 'security':
        return <SecuritySettings />;
      case 'about':
        return <AboutSettings />;
      default:
        return <GeneralSettings user={user} updateProfile={updateProfile} />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('settingsTitle')}</h1>
        <p className="text-gray-600 dark:text-gray-300 mt-1">
          {t('settingsDescription')}
        </p>
      </div>

      {/* Settings Layout */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700">
            <nav className="p-4 space-y-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300 border-r-2 border-blue-700 dark:border-blue-400'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  <tab.icon className="w-5 h-5 mr-3" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 p-6">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

const GeneralSettings = ({ user, updateProfile }) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    avatar: user?.avatar || ''
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  // Update form data when user changes
  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        avatar: user.avatar || ''
      });
    }
  }, [user]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const result = await updateProfile(formData);
      if (result.success) {
        setMessage(t('profileUpdatedSuccessfully'));
      } else {
        setMessage(result.error);
      }
    } catch (error) {
      setMessage('Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t('generalSettingsTitle')}</h2>
        <p className="text-gray-600 dark:text-gray-300 mt-1">{t('generalSettingsDescription')}</p>
      </div>

      {message && (
        <div className={`p-3 rounded-md ${message.includes('success') || message.includes('thành công') ? 'bg-green-50 dark:bg-green-900 text-green-700 dark:text-green-300' : 'bg-red-50 dark:bg-red-900 text-red-700 dark:text-red-300'}`}>
          {message}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {t('fullName')}
          </label>
          <input
            type="text"
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {t('emailAddress')}
          </label>
          <input
            type="email"
            id="email"
            value={formData.email}
            disabled
            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{t('emailCannotBeChanged')}</p>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 dark:focus:ring-offset-gray-800"
          >
            {loading ? t('saving') : t('saveChanges')}
          </button>
        </div>
      </form>
    </div>
  );
};

const AppearanceSettings = () => {
  const { theme, setTheme, actualTheme } = useTheme();
  const { language, setLanguage, t } = useLanguage();
  const { addNotification } = useNotification();

  const handleThemeChange = async (newTheme) => {
    try {
      await setTheme(newTheme);
      addNotification(t('settingsSavedSuccessfully'), 'success');
    } catch (error) {
      console.error('Failed to change theme:', error);
      addNotification(t('failedToSaveSettings'), 'error');
    }
  };

  const handleLanguageChange = async (newLanguage) => {
    try {
      await setLanguage(newLanguage);
      addNotification(t('settingsSavedSuccessfully'), 'success');
    } catch (error) {
      console.error('Failed to change language:', error);
      addNotification(t('failedToSaveSettings'), 'error');
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t('appearanceSettingsTitle')}</h2>
        <p className="text-gray-600 dark:text-gray-300 mt-1">{t('appearanceSettingsDescription')}</p>
      </div>

      <div className="space-y-6">
        {/* Theme Selection */}
        <div>
          <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">{t('themeMode')}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">{t('themeDescription')}</p>

          <div className="grid grid-cols-3 gap-3">
            <button
              onClick={() => handleThemeChange('light')}
              className={`p-4 border-2 rounded-lg transition-all ${
                theme === 'light'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
            >
              <div className="flex flex-col items-center space-y-2">
                <SunIcon className="w-6 h-6 text-yellow-500" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">{t('lightTheme')}</span>
              </div>
            </button>

            <button
              onClick={() => handleThemeChange('dark')}
              className={`p-4 border-2 rounded-lg transition-all ${
                theme === 'dark'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
            >
              <div className="flex flex-col items-center space-y-2">
                <MoonIcon className="w-6 h-6 text-blue-500" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">{t('darkTheme')}</span>
              </div>
            </button>

            <button
              onClick={() => handleThemeChange('system')}
              className={`p-4 border-2 rounded-lg transition-all ${
                theme === 'system'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
            >
              <div className="flex flex-col items-center space-y-2">
                <ComputerDesktopIcon className="w-6 h-6 text-gray-500" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">{t('systemTheme')}</span>
              </div>
            </button>
          </div>

          {theme === 'system' && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              Currently using: {actualTheme === 'dark' ? t('darkTheme') : t('lightTheme')}
            </p>
          )}
        </div>

        {/* Language Selection */}
        <div>
          <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">{t('languageSelection')}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">{t('languageDescription')}</p>

          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={() => handleLanguageChange('en')}
              className={`p-4 border-2 rounded-lg transition-all ${
                language === 'en'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
            >
              <div className="flex items-center space-x-3">
                <span className="text-2xl">🇺🇸</span>
                <div className="text-left">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">English</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">United States</div>
                </div>
              </div>
            </button>

            <button
              onClick={() => handleLanguageChange('vi')}
              className={`p-4 border-2 rounded-lg transition-all ${
                language === 'vi'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
            >
              <div className="flex items-center space-x-3">
                <span className="text-2xl">🇻🇳</span>
                <div className="text-left">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">Tiếng Việt</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Việt Nam</div>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const SyncSettings = () => {
  const { token } = useAuth();
  const { addNotification } = useNotification();
  const { t } = useLanguage();
  const [settings, setSettings] = useState({
    realTimeSync: true,
    conflictResolution: 'ask',
    bandwidthLimit: false,
    maxBandwidth: 1000
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/users/settings`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSettings(prev => ({ ...prev, ...data.settings }));
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  };

  const saveSettings = async (newSettings) => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/users/settings`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ settings: newSettings })
      });

      if (response.ok) {
        setSettings(newSettings);
        addNotification('Settings saved successfully', 'success');
      } else {
        throw new Error('Failed to save settings');
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      addNotification('Failed to save settings', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = (key, value) => {
    const newSettings = { ...settings, [key]: value };
    saveSettings(newSettings);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t('syncSettingsTitle')}</h2>
        <p className="text-gray-600 dark:text-gray-300 mt-1">{t('syncSettingsDescription')}</p>
      </div>

      <div className="space-y-4">
        <Toggle
          label={t('realTimeSync')}
          description={t('realTimeSyncDescription')}
          checked={settings.realTimeSync}
          onChange={(checked) => handleSettingChange('realTimeSync', checked)}
          disabled={loading}
        />

        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">{t('conflictResolution')}</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">{t('conflictResolutionDescription')}</p>
          </div>
          <select
            value={settings.conflictResolution}
            onChange={(e) => handleSettingChange('conflictResolution', e.target.value)}
            disabled={loading}
            className="block w-32 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:text-white"
          >
            <option value="ask">{t('askMe')}</option>
            <option value="newer">{t('keepNewer')}</option>
            <option value="larger">{t('keepLarger')}</option>
          </select>
        </div>

        <Toggle
          label={t('bandwidthLimit')}
          description={t('bandwidthLimitDescription')}
          checked={settings.bandwidthLimit}
          onChange={(checked) => handleSettingChange('bandwidthLimit', checked)}
          disabled={loading}
        />

        {settings.bandwidthLimit && (
          <div className="flex items-center justify-between pl-6">
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">{t('maxBandwidth')} (KB/s)</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">{t('maxBandwidthDescription')}</p>
            </div>
            <input
              type="number"
              value={settings.maxBandwidth}
              onChange={(e) => handleSettingChange('maxBandwidth', parseInt(e.target.value))}
              disabled={loading}
              min="100"
              max="10000"
              className="block w-24 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:text-white"
            />
          </div>
        )}
      </div>
    </div>
  );
};

const NotificationSettings = () => {
  const { token } = useAuth();
  const { addNotification } = useNotification();
  const { t } = useLanguage();
  const [settings, setSettings] = useState({
    syncCompletion: true,
    syncErrors: true,
    systemNotifications: true
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadNotificationSettings();
  }, []);

  const loadNotificationSettings = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/users/settings`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.settings.notifications) {
          setSettings(prev => ({ ...prev, ...data.settings.notifications }));
        }
      }
    } catch (error) {
      console.error('Failed to load notification settings:', error);
    }
  };

  const saveNotificationSettings = async (newSettings) => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/users/settings`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          settings: {
            notifications: newSettings
          }
        })
      });

      if (response.ok) {
        setSettings(newSettings);
        addNotification('Notification settings saved successfully', 'success');
      } else {
        throw new Error('Failed to save notification settings');
      }
    } catch (error) {
      console.error('Failed to save notification settings:', error);
      addNotification('Failed to save notification settings', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = (key, value) => {
    const newSettings = { ...settings, [key]: value };
    saveNotificationSettings(newSettings);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t('notificationSettingsTitle')}</h2>
        <p className="text-gray-600 dark:text-gray-300 mt-1">{t('notificationSettingsDescription')}</p>
      </div>

      <div className="space-y-4">
        <Toggle
          label={t('syncCompletion')}
          description={t('syncCompletionDescription')}
          checked={settings.syncCompletion}
          onChange={(checked) => handleSettingChange('syncCompletion', checked)}
          disabled={loading}
        />

        <Toggle
          label={t('syncErrors')}
          description={t('syncErrorsDescription')}
          checked={settings.syncErrors}
          onChange={(checked) => handleSettingChange('syncErrors', checked)}
          disabled={loading}
        />

        <Toggle
          label={t('systemNotifications')}
          description={t('systemNotificationsDescription')}
          checked={settings.systemNotifications}
          onChange={(checked) => handleSettingChange('systemNotifications', checked)}
          disabled={loading}
        />
      </div>
    </div>
  );
};

const SecuritySettings = () => {
  const { token } = useAuth();
  const { addNotification } = useNotification();
  const { t } = useLanguage();
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);

  const handlePasswordChange = async (e) => {
    e.preventDefault();

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      addNotification('New passwords do not match', 'error');
      return;
    }

    if (passwordForm.newPassword.length < 6) {
      addNotification('Password must be at least 6 characters', 'error');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/auth/password`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          currentPassword: passwordForm.currentPassword,
          newPassword: passwordForm.newPassword
        })
      });

      if (response.ok) {
        addNotification('Password changed successfully', 'success');
        setShowPasswordModal(false);
        setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
      } else {
        const data = await response.json();
        addNotification(data.message || 'Failed to change password', 'error');
      }
    } catch (error) {
      console.error('Failed to change password:', error);
      addNotification('Failed to change password', 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t('securitySettingsTitle')}</h2>
        <p className="text-gray-600 dark:text-gray-300 mt-1">{t('securitySettingsDescription')}</p>
      </div>

      <div className="space-y-4">
        <button
          onClick={() => setShowPasswordModal(true)}
          className="w-full text-left p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">{t('changePassword')}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{t('changePasswordDescription')}</p>
        </button>

        <button className="w-full text-left p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 opacity-50 cursor-not-allowed">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">{t('twoFactorAuth')}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{t('twoFactorAuthDescription')}</p>
        </button>

        <button className="w-full text-left p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 opacity-50 cursor-not-allowed">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">{t('activeSessions')}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{t('activeSessionsDescription')}</p>
        </button>
      </div>

      {/* Change Password Modal */}
      {showPasswordModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">{t('changePassword')}</h3>

            <form onSubmit={handlePasswordChange} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('currentPassword')}
                </label>
                <input
                  type="password"
                  value={passwordForm.currentPassword}
                  onChange={(e) => setPasswordForm(prev => ({ ...prev, currentPassword: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('newPassword')}
                </label>
                <input
                  type="password"
                  value={passwordForm.newPassword}
                  onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  required
                  minLength="6"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('confirmNewPassword')}
                </label>
                <input
                  type="password"
                  value={passwordForm.confirmPassword}
                  onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  required
                  minLength="6"
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowPasswordModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                  disabled={loading}
                >
                  {t('cancel')}
                </button>
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                  disabled={loading}
                >
                  {loading ? t('changing') : t('changePassword')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

const AboutSettings = () => {
  const { t } = useLanguage();
  const [systemInfo, setSystemInfo] = useState({
    platform: 'Unknown',
    version: '1.0.0',
    environment: isElectron ? 'Desktop' : 'Web',
    apiUrl: API_BASE_URL
  });

  useEffect(() => {
    // Get system information
    const getSystemInfo = async () => {
      try {
        if (window.electronAPI) {
          // Get system info from Electron
          const info = await window.electronAPI.getSystemInfo?.() || {};
          setSystemInfo(prev => ({
            ...prev,
            platform: info.platform || navigator.platform,
            version: info.version || '1.0.0'
          }));
        } else {
          setSystemInfo(prev => ({
            ...prev,
            platform: navigator.platform,
            userAgent: navigator.userAgent
          }));
        }
      } catch (error) {
        console.error('Failed to get system info:', error);
      }
    };

    getSystemInfo();
  }, []);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t('aboutTitle')}</h2>
        <p className="text-gray-600 dark:text-gray-300 mt-1">{t('aboutDescription')}</p>
      </div>

      <div className="space-y-4">
        <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">{t('version')}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{systemInfo.version}</p>
        </div>

        <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">{t('environment')}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{systemInfo.environment}</p>
        </div>

        <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">{t('platform')}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{systemInfo.platform}</p>
        </div>

        <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">{t('apiServer')}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{systemInfo.apiUrl}</p>
        </div>

        <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">{t('buildDate')}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{new Date().toLocaleDateString()}</p>
        </div>

        <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">{t('support')}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1"><EMAIL></p>
        </div>

        <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">{t('license')}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">MIT License</p>
        </div>
      </div>
    </div>
  );
};

export default Settings;
