import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';

const FolderPicker = ({ isOpen, onClose, onSelect, title }) => {
  const { token } = useAuth();
  const { t } = useLanguage();
  const [currentPath, setCurrentPath] = useState('');
  const [directories, setDirectories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (isOpen) {
      loadDirectories('');
    }
  }, [isOpen]);

  const loadDirectories = async (path) => {
    setLoading(true);
    setError(null);
    try {
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
      const url = path ? `${apiUrl}/sync/directories?path=${encodeURIComponent(path)}` : `${apiUrl}/sync/directories`;
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCurrentPath(data.currentPath);
        setDirectories(data.directories);
      } else {
        throw new Error('Failed to load directories');
      }
    } catch (error) {
      console.error('Failed to load directories:', error);
      setError('Failed to load directories. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDirectoryClick = (directory) => {
    if (directory.isDrive || directory.path === 'drives') {
      loadDirectories(directory.path);
    } else {
      loadDirectories(directory.path);
    }
  };

  const handleSelectCurrent = () => {
    onSelect(currentPath);
    onClose();
  };

  const goUp = () => {
    if (currentPath && currentPath !== 'Computer') {
      const parentPath = currentPath.split('\\').slice(0, -1).join('\\');
      if (parentPath) {
        loadDirectories(parentPath);
      } else {
        loadDirectories('drives');
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl h-96 flex flex-col">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{title || t('selectFolder')}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Current Path */}
        <div className="px-6 py-3 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-300">Current:</span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">{currentPath || 'Home'}</span>
            {currentPath && currentPath !== 'Computer' && (
              <button
                onClick={goUp}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 ml-2"
              >
                ↑ Up
              </button>
            )}
          </div>
        </div>

        {/* Directory List */}
        <div className="flex-1 overflow-y-auto p-6">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-gray-500 dark:text-gray-400">Loading directories...</div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-red-500 dark:text-red-400">{error}</div>
            </div>
          ) : directories.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-gray-500 dark:text-gray-400">No directories found</div>
            </div>
          ) : (
            <div className="space-y-2">
              {directories.map((directory, index) => (
                <button
                  key={index}
                  onClick={() => handleDirectoryClick(directory)}
                  className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-blue-50 dark:hover:bg-blue-900 hover:border-blue-300 dark:hover:border-blue-600 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">
                      {directory.isDrive ? '💿' : directory.isSpecial ? directory.name.charAt(0) : '📁'}
                    </span>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {directory.isSpecial ? directory.name : directory.name}
                      </div>
                      {directory.path && !directory.isSpecial && (
                        <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                          {directory.path}
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-between">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            {t('cancel')}
          </button>
          <div className="flex space-x-3">
            <button
              onClick={handleSelectCurrent}
              disabled={!currentPath || currentPath === 'Computer'}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {t('selectCurrentFolder')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Icon component
const XMarkIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
  </svg>
);

export default FolderPicker;
