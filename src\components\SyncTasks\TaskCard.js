import React from 'react';
import { useSocket } from '../../contexts/SocketContext';
import { useLanguage } from '../../contexts/LanguageContext';

const TaskCard = ({ task, isActive, onStart, onStop, onEdit, onDelete }) => {
  const { getSyncProgress } = useSocket();
  const { t } = useLanguage();
  const progress = getSyncProgress(task.id);

  const getStatusColor = (status) => {
    switch (status) {
      case 'running':
      case 'syncing':
        return 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20';
      case 'monitoring':
        return 'text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/20';
      case 'completed':
        return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20';
      case 'error':
        return 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20';
      case 'paused':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'running':
      case 'syncing':
        return t('syncing');
      case 'monitoring':
        return t('monitoring');
      case 'completed':
        return t('completed');
      case 'error':
        return t('error');
      case 'paused':
        return t('paused');
      case 'idle':
        return t('idle');
      default:
        return t('unknown');
    }
  };

  const getSyncTypeIcon = (syncType) => {
    switch (syncType) {
      case 'bidirectional':
        return '🔄';
      case 'source-to-destination':
        return '➡️';
      case 'destination-to-source':
        return '⬅️';
      case 'mirror':
        return '🪞';
      case 'incremental':
        return '📈';
      default:
        return '🔄';
    }
  };

  const getSyncTypeText = (syncType) => {
    switch (syncType) {
      case 'bidirectional':
        return t('bidirectional');
      case 'source-to-destination':
        return t('sourceToDestination');
      case 'destination-to-source':
        return t('destinationToSource');
      case 'mirror':
        return t('mirror');
      case 'incremental':
        return t('incremental');
      default:
        return t('bidirectional');
    }
  };

  const formatPath = (path) => {
    if (!path) return '';
    return path.length > 50 ? `...${path.slice(-47)}` : path;
  };

  const formatLastSync = (lastSync) => {
    if (!lastSync) return t('never');
    const date = new Date(lastSync);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return t('justNow');
    if (diffMins < 60) return `${diffMins} ${t('minutesAgo')}`;
    if (diffHours < 24) return `${diffHours} ${t('hoursAgo')}`;
    return `${diffDays} ${t('daysAgo')}`;
  };

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {task.name}
            </h3>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
              {getStatusText(task.status)}
            </span>
          </div>
          
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
            <span>{getSyncTypeIcon(task.syncType)}</span>
            <span>{getSyncTypeText(task.syncType)}</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2">
          {isActive ? (
            <button
              onClick={onStop}
              className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <StopIcon className="w-4 h-4 mr-1" />
              {t('stop')}
            </button>
          ) : (
            <button
              onClick={onStart}
              disabled={task.status === 'running'}
              className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <PlayIcon className="w-4 h-4 mr-1" />
              {t('start')}
            </button>
          )}

          <button
            onClick={() => onEdit(task)}
            disabled={isActive || task.status === 'running'}
            className="text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 disabled:text-gray-300 dark:disabled:text-gray-600"
            title={t('editTask')}
          >
            <EditIcon className="w-5 h-5" />
          </button>

          <button
            onClick={onDelete}
            disabled={isActive || task.status === 'running'}
            className="text-gray-400 dark:text-gray-500 hover:text-red-600 dark:hover:text-red-400 disabled:text-gray-300 dark:disabled:text-gray-600"
            title={t('deleteTask')}
          >
            <TrashIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      {isActive && progress && (
        <div className="mb-4">
          <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-300 mb-1">
            <span>{t('progress')}</span>
            <span>{Math.round(progress.percentage || 0)}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress.percentage || 0}%` }}
            ></div>
          </div>
          {progress.currentFile && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
              {t('processing')}: {progress.currentFile}
            </p>
          )}
        </div>
      )}

      {/* Paths */}
      <div className="space-y-2 mb-4">
        <div className="flex items-center space-x-2 text-sm">
          <span className="text-gray-500 dark:text-gray-400 w-16">{t('source')}:</span>
          <span className="text-gray-900 dark:text-white font-mono text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
            {formatPath(task.sourcePath)}
          </span>
        </div>
        <div className="flex items-center space-x-2 text-sm">
          <span className="text-gray-500 dark:text-gray-400 w-16">{t('destination')}:</span>
          <span className="text-gray-900 dark:text-white font-mono text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
            {formatPath(task.destinationPath)}
          </span>
        </div>
      </div>

      {/* Stats */}
      <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
        <div className="flex items-center space-x-4">
          <span>{t('lastSync')}: {formatLastSync(task.lastSync)}</span>
          {task.filesCount && (
            <span>{task.filesCount} {t('files')}</span>
          )}
          {task.createdAt && (
            <span>Created: {new Date(task.createdAt).toLocaleDateString()}</span>
          )}
        </div>
        <div className="flex items-center space-x-4">
          {task.totalSize && (
            <span>{formatFileSize(task.totalSize)}</span>
          )}
          {task.schedule && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
              ⏰ Scheduled
            </span>
          )}
          {task.options?.enableRealtime && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200">
              ⚡ Real-time
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

// Helper function
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Icon components
const PlayIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z" />
  </svg>
);

const StopIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M5.25 7.5A2.25 2.25 0 0 1 7.5 5.25h9a2.25 2.25 0 0 1 2.25 2.25v9a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-9Z" />
  </svg>
);

const EditIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10" />
  </svg>
);

const TrashIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
  </svg>
);

export default TaskCard;
