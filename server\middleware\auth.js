const jwt = require('jsonwebtoken');
const { getDatabase } = require('../database/init');

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

const authMiddleware = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'No token provided'
      });
    }
    
    const token = authHeader.replace('Bearer ', '');
    
    // Verify JWT token
    let decoded;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid or expired token'
      });
    }
    
    // Check if session exists in database
    const db = getDatabase();
    const sessionResult = await db.query(
      'SELECT user_id, expires_at FROM sessions WHERE token = ?',
      [token]
    );
    
    if (sessionResult.rows.length === 0) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Session not found'
      });
    }
    
    const session = sessionResult.rows[0];
    
    // Check if session has expired
    if (new Date() > new Date(session.expires_at)) {
      // Clean up expired session
      await db.query('DELETE FROM sessions WHERE token = ?', [token]);
      
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Session expired'
      });
    }
    
    // Verify user still exists
    const userResult = await db.query(
      'SELECT id, email FROM users WHERE id = ?',
      [session.user_id]
    );
    
    if (userResult.rows.length === 0) {
      // Clean up session for non-existent user
      await db.query('DELETE FROM sessions WHERE user_id = ?', [session.user_id]);
      
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'User not found'
      });
    }
    
    // Add user info to request
    req.userId = session.user_id;
    req.userEmail = userResult.rows[0].email;
    req.token = token;
    req.user = {
      id: session.user_id,
      email: userResult.rows[0].email
    };
    
    next();
    
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication failed'
    });
  }
};

module.exports = authMiddleware;
