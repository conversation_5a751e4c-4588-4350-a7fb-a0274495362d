import React, { useState, useEffect } from 'react';
import { useSync } from '../../contexts/SyncContext';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useSocket } from '../../contexts/SocketContext';
import StatsCard from './StatsCard';
import RecentActivity from './RecentActivity';
import QuickActions from './QuickActions';
import SyncTasksList from './SyncTasksList';

const Dashboard = () => {
  const { syncTasks, activeSyncs, syncHistory, isOnline, loadSyncTasks, loadSyncHistory } = useSync();
  const { user } = useAuth();
  const { t } = useLanguage();
  const { socket } = useSocket();
  const [stats, setStats] = useState({
    totalTasks: 0,
    activeTasks: 0,
    totalFiles: 0,
    totalSize: 0,
    lastSync: null
  });
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    calculateStats();
  }, [syncTasks, activeSyncs, syncHistory]);

  // Listen for real-time sync events to refresh dashboard
  useEffect(() => {
    if (socket) {
      const handleSyncEvent = (data) => {
        console.log('Dashboard: Sync event received:', data);
        // Refresh dashboard data when sync events occur
        handleRefresh();
      };

      // Listen for all sync-related events
      socket.on('sync-completed', handleSyncEvent);
      socket.on('sync-error', handleSyncEvent);
      socket.on('realtime-sync-completed', handleSyncEvent);
      socket.on('realtime-sync-processing', handleSyncEvent);
      socket.on('sync-progress', (data) => {
        // For progress events, just update stats without full refresh
        calculateStats();
      });

      return () => {
        socket.off('sync-completed', handleSyncEvent);
        socket.off('sync-error', handleSyncEvent);
        socket.off('realtime-sync-completed', handleSyncEvent);
        socket.off('realtime-sync-processing', handleSyncEvent);
        socket.off('sync-progress');
      };
    }
  }, [socket]);

  const calculateStats = () => {
    const totalTasks = syncTasks.length;
    const activeTasks = activeSyncs.size;
    const totalFiles = syncTasks.reduce((sum, task) => sum + (task.filesCount || 0), 0);
    const totalSize = syncTasks.reduce((sum, task) => sum + (task.totalSize || 0), 0);
    
    // Get last sync from history
    const lastSyncEntry = syncHistory.find(entry => entry.status === 'completed');
    const lastSync = lastSyncEntry ? new Date(lastSyncEntry.timestamp) : null;

    setStats({
      totalTasks,
      activeTasks,
      totalFiles,
      totalSize,
      lastSync
    });
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatLastSync = (date) => {
    if (!date) return t('never');
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        loadSyncTasks(),
        loadSyncHistory()
      ]);
    } catch (error) {
      console.error('Failed to refresh dashboard:', error);
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Welcome section */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h1 className="text-2xl font-bold mb-2">
              {t('welcomeBack')}, {user?.name || 'User'}!
            </h1>
            <p className="text-blue-100">
              Monitor and manage your file synchronization tasks from this dashboard.
            </p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="ml-4 p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors disabled:opacity-50"
            title="Refresh Dashboard"
          >
            <RefreshIcon className={`w-5 h-5 ${refreshing ? 'animate-spin' : ''}`} />
          </button>
        </div>
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <div className={`w-2 h-2 rounded-full mr-2 ${isOnline ? 'bg-green-300' : 'bg-red-300'}`}></div>
              <span className="text-sm">{isOnline ? t('online') : t('offline')}</span>
            </div>
            {activeSyncs.size > 0 && (
              <div className="flex items-center">
                <div className="w-2 h-2 bg-yellow-300 rounded-full mr-2 animate-pulse"></div>
                <span className="text-sm">{activeSyncs.size} tasks {t('syncing')}</span>
              </div>
            )}
            {refreshing && (
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-300 rounded-full mr-2 animate-pulse"></div>
                <span className="text-sm">{t('loading')}...</span>
              </div>
            )}
          </div>
          <div className="text-right">
            <p className="text-xs text-blue-200">Last updated</p>
            <p className="text-sm">{new Date().toLocaleTimeString()}</p>
          </div>
        </div>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title={t('totalSyncTasks')}
          value={stats.totalTasks}
          icon={<FolderIcon className="w-6 h-6" />}
          color="blue"
          subtitle="Configured sync tasks"
          trend={stats.totalTasks > 0 ? {
            direction: 'up',
            value: `${stats.totalTasks} tasks`,
            period: 'configured'
          } : null}
        />
        <StatsCard
          title={t('activeSyncs')}
          value={stats.activeTasks}
          icon={<SyncIcon className="w-6 h-6" />}
          color="green"
          isAnimated={stats.activeTasks > 0}
          subtitle={stats.activeTasks > 0 ? "Currently syncing" : "No active syncs"}
          trend={stats.activeTasks > 0 ? {
            direction: 'up',
            value: `${stats.activeTasks} running`,
            period: 'now'
          } : null}
        />
        <StatsCard
          title="Total Files"
          value={stats.totalFiles.toLocaleString()}
          icon={<DocumentIcon className="w-6 h-6" />}
          color="purple"
          subtitle="Files synchronized"
          trend={stats.totalFiles > 0 ? {
            direction: 'neutral',
            value: `${stats.totalFiles.toLocaleString()} files`,
            period: 'total'
          } : null}
        />
        <StatsCard
          title="Total Size"
          value={formatFileSize(stats.totalSize)}
          icon={<ServerIcon className="w-6 h-6" />}
          color="orange"
          subtitle="Data synchronized"
          trend={stats.totalSize > 0 ? {
            direction: 'neutral',
            value: formatFileSize(stats.totalSize),
            period: 'total'
          } : null}
        />
      </div>

      {/* Quick actions and recent activity */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <QuickActions />
        </div>
        <div className="lg:col-span-2">
          <RecentActivity />
        </div>
      </div>

      {/* Sync tasks overview */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Sync Tasks Overview</h2>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {t('lastSync')}: {formatLastSync(stats.lastSync)}
            </span>
          </div>
        </div>
        <SyncTasksList />
      </div>
    </div>
  );
};

// Icon components
const FolderIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 9.776c.112-.017.227-.026.344-.026h15.812c.117 0 .232.009.344.026m-16.5 0a2.25 2.25 0 0 0-1.883 2.542l.857 6a2.25 2.25 0 0 0 2.227 1.932H19.05a2.25 2.25 0 0 0 2.227-1.932l.857-6a2.25 2.25 0 0 0-1.883-2.542m-16.5 0V6A2.25 2.25 0 0 1 6 3.75h3.879a1.5 1.5 0 0 1 1.06.44l2.122 2.12a1.5 1.5 0 0 0 1.06.44H18A2.25 2.25 0 0 1 20.25 9v.776" />
  </svg>
);

const SyncIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
  </svg>
);

const DocumentIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
  </svg>
);

const ServerIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M21.75 17.25v-.228a4.5 4.5 0 0 0-.12-1.03l-2.268-9.64a3.375 3.375 0 0 0-3.285-2.602H7.923a3.375 3.375 0 0 0-3.285 2.602l-2.268 9.64a4.5 4.5 0 0 0-.12 1.03v.228m19.5 0a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3m19.5 0a3 3 0 0 0-3-3H5.25a3 3 0 0 0-3 3" />
  </svg>
);

const RefreshIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
  </svg>
);

export default Dashboard;
