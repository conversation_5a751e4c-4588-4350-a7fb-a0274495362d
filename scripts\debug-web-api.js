// Debug Web API step by step
const axios = require('axios');

const webBaseURL = 'http://localhost:5001/api';

async function debugWebAPI() {
  console.log('🔍 Debugging Web API Step by Step\n');
  
  try {
    // 1. Test basic health check
    console.log('1. 🏥 Testing health check...');
    
    try {
      const healthResponse = await axios.get(`${webBaseURL}/health`);
      console.log('✅ Health check passed');
      console.log('📊 Response:', healthResponse.data);
    } catch (error) {
      console.log('❌ Health check failed:', error.message);
      return;
    }
    
    // 2. Test login
    console.log('\n2. 🔐 Testing login...');
    
    let token, headers;
    try {
      const loginResponse = await axios.post(`${webBaseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      token = loginResponse.data.token;
      headers = { 'Authorization': `Bearer ${token}` };
      
      console.log('✅ Login successful');
      console.log('📊 User:', loginResponse.data.user.name);
      
    } catch (error) {
      console.log('❌ Login failed:', error.message);
      if (error.response) {
        console.log('📊 Response:', error.response.data);
      }
      return;
    }
    
    // 3. Test auth verification
    console.log('\n3. 🔒 Testing auth verification...');
    
    try {
      const verifyResponse = await axios.get(`${webBaseURL}/auth/verify`, { headers });
      console.log('✅ Auth verification passed');
      console.log('📊 User:', verifyResponse.data.user.name);
      
    } catch (error) {
      console.log('❌ Auth verification failed:', error.message);
      if (error.response) {
        console.log('📊 Response:', error.response.data);
      }
      return;
    }
    
    // 4. Test database connection via sync tasks
    console.log('\n4. 🗄️ Testing database via sync tasks...');
    
    try {
      const tasksResponse = await axios.get(`${webBaseURL}/sync/tasks`, { headers });
      console.log('✅ Database connection working');
      console.log('📊 Tasks count:', tasksResponse.data.tasks.length);
      
    } catch (error) {
      console.log('❌ Database connection failed:', error.message);
      if (error.response) {
        console.log('📊 Response:', error.response.data);
      }
      return;
    }
    
    // 5. Test clients endpoint specifically
    console.log('\n5. 🖥️ Testing clients endpoint...');
    
    try {
      const clientsResponse = await axios.get(`${webBaseURL}/clients`, { headers });
      console.log('✅ Clients endpoint working');
      console.log('📊 Clients count:', clientsResponse.data.total);
      
    } catch (error) {
      console.log('❌ Clients endpoint failed:', error.message);
      if (error.response) {
        console.log('📊 Response:', error.response.data);
        console.log('📊 Status:', error.response.status);
      }
      
      // Try to get more details
      console.log('\n🔍 Debugging clients endpoint error...');
      
      if (error.response && error.response.status === 500) {
        console.log('💡 500 Error suggests:');
        console.log('   - Database query failed');
        console.log('   - Missing table: desktop_clients');
        console.log('   - SQL syntax error');
        console.log('   - Database connection issue');
      }
      
      return;
    }
    
    // 6. Test client registration
    console.log('\n6. 📝 Testing client registration...');
    
    try {
      const testClientId = `debug-client-${Date.now()}`;
      const registerResponse = await axios.post(`${webBaseURL}/clients/register`, {
        clientId: testClientId,
        hostname: 'debug-hostname',
        platform: 'win32',
        arch: 'x64',
        version: '1.0.0',
        nodeVersion: process.version,
        totalMemory: 8589934592,
        cpuCount: 8
      }, { headers });
      
      console.log('✅ Client registration working');
      console.log('📊 Registered client:', testClientId);
      
      // Clean up
      try {
        await axios.delete(`${webBaseURL}/clients/${testClientId}`, { headers });
        console.log('✅ Test client cleaned up');
      } catch (cleanupError) {
        console.log('⚠️ Cleanup failed (not critical)');
      }
      
    } catch (error) {
      console.log('❌ Client registration failed:', error.message);
      if (error.response) {
        console.log('📊 Response:', error.response.data);
      }
    }
    
    console.log('\n📊 WEB API DEBUG SUMMARY:');
    console.log('   Health Check: ✅ PASSED');
    console.log('   Login: ✅ PASSED');
    console.log('   Auth: ✅ PASSED');
    console.log('   Database: ✅ PASSED');
    console.log('   Clients Endpoint: Check above results');
    
    console.log('\n💡 If clients endpoint failed:');
    console.log('   1. Check web server logs for SQL errors');
    console.log('   2. Verify desktop_clients table exists in PostgreSQL');
    console.log('   3. Check database connection');
    console.log('   4. Verify client routes are properly loaded');
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

async function checkWebServerStatus() {
  console.log('\n🌐 Checking Web Server Status\n');
  
  try {
    // Check if server is running
    const response = await axios.get(`${webBaseURL}/health`, { timeout: 5000 });
    console.log('✅ Web server is running on port 5001');
    console.log('📊 Server status:', response.data);
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Web server is not running');
      console.log('💡 Start it with: npm run start-web');
    } else {
      console.log('❌ Web server error:', error.message);
    }
  }
}

// Auto-run if called directly
if (require.main === module) {
  checkWebServerStatus().then(() => {
    console.log('\n' + '='.repeat(60));
    return debugWebAPI();
  });
}

module.exports = { debugWebAPI, checkWebServerStatus };

console.log('🔍 Web API Debugger loaded!');
console.log('📝 Run: node scripts/debug-web-api.js');
