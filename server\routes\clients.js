// Client Management API Routes
const express = require('express');
const { getDatabase } = require('../database/init');
const authMiddleware = require('../middleware/auth');

const router = express.Router();

// GET /api/clients - List user's desktop clients
router.get('/', authMiddleware, async (req, res) => {
  try {
    const db = getDatabase();
    
    const clients = await db.query(`
      SELECT 
        dc.*,
        COUNT(st.id) as total_tasks,
        COUNT(CASE WHEN st.status = 'running' THEN 1 END) as active_tasks,
        MAX(sh.started_at) as last_sync_time
      FROM desktop_clients dc
      LEFT JOIN sync_tasks st ON dc.client_id = st.client_id
      LEFT JOIN sync_history sh ON st.id = sh.task_id
      WHERE dc.user_id = ?
      GROUP BY dc.id
      ORDER BY dc.last_seen DESC
    `, [req.user.id]);
    
    res.json({
      message: 'Desktop clients retrieved successfully',
      clients: clients.rows || clients,
      total: (clients.rows || clients).length
    });
    
  } catch (error) {
    console.error('Get clients error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve desktop clients'
    });
  }
});

// GET /api/clients/:clientId - Get specific client details
router.get('/:clientId', authMiddleware, async (req, res) => {
  try {
    const { clientId } = req.params;
    const db = getDatabase();
    
    // Get client info
    const clientResult = await db.query(`
      SELECT * FROM desktop_clients 
      WHERE client_id = ? AND user_id = ?
    `, [clientId, req.user.id]);
    
    if (!clientResult.rows?.length && !clientResult.length) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Desktop client not found'
      });
    }
    
    const client = clientResult.rows?.[0] || clientResult[0];
    
    // Get client's sync tasks
    const tasksResult = await db.query(`
      SELECT * FROM sync_tasks 
      WHERE client_id = ? AND user_id = ?
      ORDER BY created_at DESC
    `, [clientId, req.user.id]);
    
    // Get recent sync history
    const historyResult = await db.query(`
      SELECT sh.*, st.name as task_name
      FROM sync_history sh
      JOIN sync_tasks st ON sh.task_id = st.id
      WHERE st.client_id = ? AND st.user_id = ?
      ORDER BY sh.started_at DESC
      LIMIT 10
    `, [clientId, req.user.id]);
    
    res.json({
      message: 'Client details retrieved successfully',
      client: {
        ...client,
        tasks: tasksResult.rows || tasksResult,
        recentHistory: historyResult.rows || historyResult
      }
    });
    
  } catch (error) {
    console.error('Get client details error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve client details'
    });
  }
});

// POST /api/clients/register - Register new desktop client (no auth required for cross-server registration)
router.post('/register', async (req, res) => {
  try {
    const {
      clientId,
      hostname,
      platform,
      arch,
      version,
      nodeVersion,
      totalMemory,
      cpuCount,
      userId
    } = req.body;
    
    if (!clientId || !hostname || !userId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'clientId, hostname, and userId are required'
      });
    }
    
    const db = getDatabase();
    
    // Check if client already exists
    const existingClient = await db.query(`
      SELECT id, status, user_id FROM desktop_clients
      WHERE client_id = ?
    `, [clientId]);

    if (existingClient.rows?.length || existingClient.length) {
      const client = existingClient.rows?.[0] || existingClient[0];

      // Update existing client - ALWAYS update user_id and set to online
      const updateResult = await db.query(`
        UPDATE desktop_clients SET
          user_id = ?,
          hostname = ?,
          platform = ?,
          arch = ?,
          version = ?,
          status = 'online',
          last_seen = CURRENT_TIMESTAMP,
          metadata = ?
        WHERE client_id = ?
      `, [
        userId, // Always update user_id to current user
        hostname,
        platform,
        arch,
        version,
        JSON.stringify({
          nodeVersion,
          totalMemory,
          cpuCount,
          lastUpdate: new Date(),
          previousStatus: client.status,
          previousUserId: client.user_id
        }),
        clientId
      ]);

      console.log(`🔄 Updated existing client: ${clientId} (${hostname})`);
      console.log(`   Status: ${client.status} → online`);
      console.log(`   User: ${client.user_id} → ${userId}`);
      console.log(`   Rows affected: ${updateResult.affectedRows || updateResult.rowCount || 'unknown'}`);

      // Notify change listener for real-time sync
      const changeListener = req.app.get('changeListener');
      if (changeListener) {
        changeListener.notifyClientChange('updated', clientId, {
          status: 'online',
          previousStatus: client.status,
          userId: userId
        });
      }
    } else {
      // Insert new client
      await db.query(`
        INSERT INTO desktop_clients (
          user_id, client_id, hostname, platform, arch, version,
          status, last_seen, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, 'online', CURRENT_TIMESTAMP, ?)
      `, [
        userId,
        clientId,
        hostname,
        platform,
        arch,
        version,
        JSON.stringify({
          nodeVersion,
          totalMemory,
          cpuCount,
          registeredAt: new Date()
        })
      ]);
      
      console.log(`✅ Registered new client: ${clientId} (${hostname})`);
    }
    
    res.json({
      message: 'Client registered successfully',
      clientId,
      status: 'online'
    });
    
  } catch (error) {
    console.error('Client registration error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to register client'
    });
  }
});

// POST /api/clients/:clientId/command - Send command to specific client
router.post('/:clientId/command', authMiddleware, async (req, res) => {
  try {
    const { clientId } = req.params;
    const { type, data } = req.body;
    
    if (!type) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Command type is required'
      });
    }
    
    const db = getDatabase();
    
    // Verify client belongs to user
    const clientResult = await db.query(`
      SELECT id FROM desktop_clients 
      WHERE client_id = ? AND user_id = ?
    `, [clientId, req.user.id]);
    
    if (!clientResult.rows?.length && !clientResult.length) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Desktop client not found'
      });
    }
    
    // Generate command ID
    const commandId = `cmd-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
    
    // Store command in database
    await db.query(`
      INSERT INTO client_commands (
        client_id, command_type, command_data, status, sent_at
      ) VALUES (?, ?, ?, 'pending', CURRENT_TIMESTAMP)
    `, [clientId, type, JSON.stringify({ ...data, commandId })]);
    
    // Send command via Socket.IO
    const io = req.app.get('io');
    if (io) {
      io.to(`client-${clientId}`).emit('remote-command', {
        id: commandId,
        type,
        data: { ...data, userId: req.user.id },
        timestamp: new Date()
      });
      
      console.log(`📨 Sent command ${type} to client ${clientId}`);
    }
    
    res.json({
      message: 'Command sent successfully',
      commandId,
      type,
      clientId
    });
    
  } catch (error) {
    console.error('Send command error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to send command'
    });
  }
});

// GET /api/clients/:clientId/status - Get real-time client status
router.get('/:clientId/status', authMiddleware, async (req, res) => {
  try {
    const { clientId } = req.params;
    const db = getDatabase();
    
    // Verify client belongs to user
    const clientResult = await db.query(`
      SELECT * FROM desktop_clients 
      WHERE client_id = ? AND user_id = ?
    `, [clientId, req.user.id]);
    
    if (!clientResult.rows?.length && !clientResult.length) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Desktop client not found'
      });
    }
    
    const client = clientResult.rows?.[0] || clientResult[0];
    
    // Check if client is currently connected via Socket.IO
    const io = req.app.get('io');
    let isConnected = false;
    let socketInfo = null;
    
    if (io) {
      const clientSockets = await io.in(`client-${clientId}`).fetchSockets();
      isConnected = clientSockets.length > 0;
      
      if (isConnected) {
        socketInfo = {
          connectedSockets: clientSockets.length,
          lastActivity: new Date()
        };
      }
    }
    
    res.json({
      message: 'Client status retrieved successfully',
      status: {
        ...client,
        isConnected,
        socketInfo,
        lastSeenFormatted: new Date(client.last_seen).toLocaleString()
      }
    });
    
  } catch (error) {
    console.error('Get client status error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve client status'
    });
  }
});

// POST /api/clients/:clientId/offline - Mark client as offline
router.post('/:clientId/offline', authMiddleware, async (req, res) => {
  try {
    const { clientId } = req.params;
    const db = getDatabase();

    // Verify client belongs to user
    const clientResult = await db.query(`
      SELECT id FROM desktop_clients
      WHERE client_id = ? AND user_id = ?
    `, [clientId, req.user.id]);

    if (!clientResult.rows?.length && !clientResult.length) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Desktop client not found'
      });
    }

    // Update client status to offline
    await db.query(`
      UPDATE desktop_clients
      SET status = 'offline', last_seen = CURRENT_TIMESTAMP
      WHERE client_id = ? AND user_id = ?
    `, [clientId, req.user.id]);

    console.log(`📴 Client marked as offline: ${clientId}`);

    res.json({
      message: 'Client marked as offline successfully',
      clientId,
      status: 'offline'
    });

  } catch (error) {
    console.error('Client offline error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to mark client as offline'
    });
  }
});

// DELETE /api/clients/:clientId - Remove client registration
router.delete('/:clientId', authMiddleware, async (req, res) => {
  try {
    const { clientId } = req.params;
    const db = getDatabase();
    
    // Verify client belongs to user
    const clientResult = await db.query(`
      SELECT id FROM desktop_clients 
      WHERE client_id = ? AND user_id = ?
    `, [clientId, req.user.id]);
    
    if (!clientResult.rows?.length && !clientResult.length) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Desktop client not found'
      });
    }
    
    // Delete client and related data
    await db.query(`
      DELETE FROM client_commands WHERE client_id = ?
    `, [clientId]);
    
    await db.query(`
      DELETE FROM desktop_clients WHERE client_id = ? AND user_id = ?
    `, [clientId, req.user.id]);
    
    console.log(`🗑️ Removed client: ${clientId}`);
    
    res.json({
      message: 'Client removed successfully',
      clientId
    });
    
  } catch (error) {
    console.error('Remove client error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to remove client'
    });
  }
});

module.exports = router;
