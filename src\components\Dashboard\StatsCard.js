import React from 'react';

const StatsCard = ({ title, value, icon, color = 'blue', isAnimated = false, subtitle, trend }) => {
  const colorClasses = {
    blue: 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-400',
    green: 'bg-green-50 dark:bg-green-900 text-green-600 dark:text-green-400',
    purple: 'bg-purple-50 dark:bg-purple-900 text-purple-600 dark:text-purple-400',
    orange: 'bg-orange-50 dark:bg-orange-900 text-orange-600 dark:text-orange-400',
    red: 'bg-red-50 dark:bg-red-900 text-red-600 dark:text-red-400'
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${colorClasses[color]} ${isAnimated ? 'animate-pulse' : ''}`}>
          {icon}
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-600 dark:text-gray-300">{title}</p>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">{value}</p>
          {subtitle && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{subtitle}</p>
          )}
          {trend && (
            <div className="flex items-center mt-1">
              <span className={`text-xs font-medium ${
                trend.direction === 'up' ? 'text-green-600 dark:text-green-400' :
                trend.direction === 'down' ? 'text-red-600 dark:text-red-400' : 'text-gray-500 dark:text-gray-400'
              }`}>
                {trend.direction === 'up' && '↗ '}
                {trend.direction === 'down' && '↘ '}
                {trend.value}
              </span>
              <span className="text-xs text-gray-400 dark:text-gray-500 ml-1">{trend.period}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatsCard;
