-- SyncMasterPro PostgreSQL Tables Creation
-- Run this on PostgreSQL server: psql -U pi -d syncmasterpro -f create-tables.sql

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    avatar TEXT,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create sync_tasks table
CREATE TABLE IF NOT EXISTS sync_tasks (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    source_path TEXT NOT NULL,
    destination_path TEXT NOT NULL,
    sync_type VARCHAR(50) DEFAULT 'bidirectional',
    schedule TEXT,
    filters JSONB DEFAULT '[]',
    options JSONB DEFAULT '{}',
    status VARCHAR(50) DEFAULT 'idle',
    last_sync TIMESTAMP,
    files_count INTEGER DEFAULT 0,
    total_size BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create sync_history table
CREATE TABLE IF NOT EXISTS sync_history (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL REFERENCES sync_tasks(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(50) NOT NULL,
    started_at TIMESTAMP NOT NULL,
    completed_at TIMESTAMP,
    files_processed INTEGER DEFAULT 0,
    files_added INTEGER DEFAULT 0,
    files_updated INTEGER DEFAULT 0,
    files_deleted INTEGER DEFAULT 0,
    total_size BIGINT DEFAULT 0,
    error_message TEXT,
    details JSONB DEFAULT '{}'
);

-- Create file_changes table
CREATE TABLE IF NOT EXISTS file_changes (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL REFERENCES sync_tasks(id) ON DELETE CASCADE,
    file_path TEXT NOT NULL,
    change_type VARCHAR(50) NOT NULL,
    file_size BIGINT,
    checksum VARCHAR(255),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create sessions table
CREATE TABLE IF NOT EXISTS sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_sync_tasks_user_id ON sync_tasks (user_id);
CREATE INDEX IF NOT EXISTS idx_sync_history_task_id ON sync_history (task_id);
CREATE INDEX IF NOT EXISTS idx_sync_history_user_id ON sync_history (user_id);
CREATE INDEX IF NOT EXISTS idx_file_changes_task_id ON file_changes (task_id);
CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions (token);
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions (user_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users (email);
CREATE INDEX IF NOT EXISTS idx_sync_tasks_status ON sync_tasks (status);
CREATE INDEX IF NOT EXISTS idx_sync_history_status ON sync_history (status);

-- Display created tables
SELECT 'Tables created successfully!' as message;
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;
