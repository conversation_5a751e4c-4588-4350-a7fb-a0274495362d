-- SyncMasterPro PostgreSQL Database Setup
-- Run this script on your PostgreSQL server

-- Create database
CREATE DATABASE syncmasterpro;

-- Grant privileges to existing user 'pi'
GRANT ALL PRIVILEGES ON DATABASE syncmasterpro TO pi;

-- Connect to the database
\c syncmasterpro;

-- <PERSON> schema privileges
GRANT ALL ON SCHEMA public TO pi;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO pi;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO pi;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO pi;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO pi;
