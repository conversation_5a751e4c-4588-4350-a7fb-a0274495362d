// Quick debug script to test real-time sync right now
// Run this while your Electron app is running

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const baseURL = 'http://localhost:5002/api';

async function quickDebugRealtime() {
  console.log('🔍 Quick Real-time Debug - Testing NOW\n');
  
  try {
    // 1. Login
    console.log('1. 🔐 Logging in...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    const headers = { 'Authorization': `Bearer ${token}` };
    console.log('✅ Login successful');
    
    // 2. Get real-time tasks
    console.log('\n2. 📋 Finding real-time tasks...');
    const tasksResponse = await axios.get(`${baseURL}/sync/tasks`, { headers });
    const tasks = tasksResponse.data.tasks || [];
    
    const realtimeTasks = tasks.filter(task => {
      const options = typeof task.options === 'string' ? JSON.parse(task.options) : task.options;
      return options?.enableRealtime;
    });
    
    console.log(`📊 Total tasks: ${tasks.length}`);
    console.log(`⚡ Real-time tasks: ${realtimeTasks.length}`);
    
    if (realtimeTasks.length === 0) {
      console.log('❌ No real-time tasks found!');
      console.log('💡 Create a task with real-time enabled first');
      return;
    }
    
    const task = realtimeTasks[0];
    console.log(`🎯 Using task: ${task.name} (ID: ${task.id})`);
    console.log(`📊 Full task object:`, task);

    // Try different possible field names (API returns camelCase)
    const sourcePath = task.sourcePath || task.source_path || task.source;
    const destPath = task.destinationPath || task.destination_path || task.destination;

    console.log(`📂 Source: ${sourcePath}`);
    console.log(`📂 Destination: ${destPath}`);

    if (!sourcePath || !destPath) {
      console.log('❌ CRITICAL: Task missing source/destination paths!');
      console.log('🔍 Available task fields:', Object.keys(task));

      // Try to get task details directly
      console.log('\n🔍 Getting task details...');
      const taskDetailResponse = await axios.get(`${baseURL}/sync/tasks/${task.id}`, { headers });
      const taskDetail = taskDetailResponse.data.task || taskDetailResponse.data;
      console.log(`📊 Task detail:`, taskDetail);

      return;
    }
    
    // 3. Check current history
    console.log('\n3. 📊 Checking current history...');
    const historyBefore = await axios.get(`${baseURL}/sync/history?taskId=${task.id}&limit=5`, { headers });
    const beforeCount = historyBefore.data.history?.length || 0;
    console.log(`📋 History entries before: ${beforeCount}`);
    
    // 4. Start real-time sync
    console.log('\n4. ⚡ Starting real-time sync...');
    try {
      const startResponse = await axios.post(
        `${baseURL}/sync/tasks/${task.id}/realtime/start`, 
        {}, 
        { headers }
      );
      console.log('✅ Real-time sync started:', startResponse.data);
    } catch (startError) {
      console.log('❌ Failed to start real-time sync:', startError.response?.data || startError.message);
      return;
    }
    
    // 5. Create test file
    console.log('\n5. 📄 Creating test file...');
    const testFileName = `debug-test-${Date.now()}.txt`;
    const testFilePath = path.join(sourcePath, testFileName);
    const testContent = `Debug test file\nCreated at: ${new Date().toISOString()}\nTask ID: ${task.id}`;

    try {
      // Ensure source directory exists
      if (!fs.existsSync(sourcePath)) {
        console.log(`📁 Creating source directory: ${sourcePath}`);
        fs.mkdirSync(sourcePath, { recursive: true });
      }

      fs.writeFileSync(testFilePath, testContent);
      console.log(`✅ Test file created: ${testFileName}`);
      console.log(`📍 File path: ${testFilePath}`);
    } catch (fileError) {
      console.log('❌ Failed to create test file:', fileError.message);
      return;
    }

    // 6. Wait and check results
    console.log('\n6. ⏳ Waiting for real-time sync (10 seconds)...');
    console.log('💡 Check server console for these logs:');
    console.log('   - "🔍 File change detected for task 14"');
    console.log('   - "📡 Emitting \'syncCompleted\' event"');
    console.log('   - "📡 Real-time sync event received"');
    console.log('   - "💾 Inserting history entry"');
    console.log('   - "✅ Real-time sync history created"');

    await delay(10000);

    // Check if file was synced
    const destFilePath = path.join(destPath, testFileName);
    const fileSynced = fs.existsSync(destFilePath);
    console.log(`📊 File synced to destination: ${fileSynced ? '✅ YES' : '❌ NO'}`);
    
    // 7. Check history after
    console.log('\n7. 📊 Checking history after...');
    const historyAfter = await axios.get(`${baseURL}/sync/history?taskId=${task.id}&limit=5`, { headers });
    const afterCount = historyAfter.data.history?.length || 0;
    const newEntries = afterCount - beforeCount;
    
    console.log(`📋 History entries after: ${afterCount}`);
    console.log(`📈 New entries: ${newEntries}`);
    
    if (newEntries > 0) {
      console.log('✅ SUCCESS: Real-time sync created history entries!');
      const latestEntry = historyAfter.data.history[0];
      console.log(`📄 Latest entry: ${latestEntry.status} - ${latestEntry.filesProcessed || latestEntry.files_processed || 0} files`);

      if (latestEntry.details) {
        try {
          const details = typeof latestEntry.details === 'string' ? JSON.parse(latestEntry.details) : latestEntry.details;
          console.log(`🏷️ Entry type: ${details.type || 'unknown'}`);
          console.log(`📊 Entry source: ${details.source || 'unknown'}`);
        } catch (error) {
          console.log(`⚠️ Could not parse entry details: ${error.message}`);
        }
      }
    } else {
      console.log('❌ ISSUE: Real-time sync did NOT create history entries');
    }
    
    // 8. Check all recent history
    console.log('\n8. 📋 Checking all recent history...');
    const allHistory = await axios.get(`${baseURL}/sync/history?limit=10`, { headers });
    const allEntries = allHistory.data.history || [];
    console.log(`📊 Total recent entries: ${allEntries.length}`);
    
    if (allEntries.length > 0) {
      console.log('📄 Recent entries:');
      allEntries.slice(0, 3).forEach((entry, index) => {
        let details = {};
        try {
          details = entry.details ? (typeof entry.details === 'string' ? JSON.parse(entry.details) : entry.details) : {};
        } catch (error) {
          console.warn(`Failed to parse details for entry ${entry.id}:`, error);
          details = {};
        }
        const type = details.type || 'regular';
        console.log(`   ${index + 1}. Task ${entry.taskId || entry.task_id}: ${entry.status} - ${entry.filesProcessed || entry.files_processed} files - Type: ${type}`);
      });
    }
    
    // 9. Stop real-time sync
    console.log('\n9. 🛑 Stopping real-time sync...');
    try {
      await axios.post(`${baseURL}/sync/tasks/${task.id}/realtime/stop`, {}, { headers });
      console.log('✅ Real-time sync stopped');
    } catch (stopError) {
      console.log('⚠️ Failed to stop real-time sync:', stopError.message);
    }
    
    // 10. Summary
    console.log('\n📊 SUMMARY:');
    console.log(`   Task: ${task.name} (ID: ${task.id})`);
    console.log(`   Test file: ${testFileName}`);
    console.log(`   File synced: ${fileSynced ? 'YES' : 'NO'}`);
    console.log(`   History entries created: ${newEntries}`);
    console.log(`   Status: ${newEntries > 0 ? '✅ SUCCESS' : '❌ FAILED'}`);
    
    if (newEntries === 0) {
      console.log('\n💡 NEXT STEPS:');
      console.log('   1. Check server console logs for:');
      console.log('      - "📡 Emitting \'syncCompleted\' event"');
      console.log('      - "📡 Real-time sync event received"');
      console.log('      - "💾 Inserting history entry"');
      console.log('      - "✅ Real-time sync history created"');
      console.log('   2. If no events are emitted, file watcher is not working');
      console.log('   3. If events are emitted but no history, database issue');
      console.log('   4. Check file paths are correct and accessible');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Export for manual use
module.exports = { quickDebugRealtime };

// Auto-run if called directly
if (require.main === module) {
  quickDebugRealtime();
}

console.log('🎯 Quick Real-time Debugger loaded!');
console.log('📝 Run: node scripts/debug-realtime-now.js');
