// Test script for UI state synchronization after restart
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const baseURL = 'http://localhost:5002/api';

async function testUIStateSync() {
  console.log('🔄 Testing UI State Sync After Restart\n');
  
  try {
    // 1. Login
    console.log('1. 🔐 Logging in...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    const headers = { 'Authorization': `Bearer ${token}` };
    console.log('✅ Login successful');
    
    // 2. Create test directories
    console.log('\n2. 📁 Setting up test directories...');
    const testDir = path.join(process.cwd(), 'test-ui-state');
    const sourceDir = path.join(testDir, 'source');
    const destDir = path.join(testDir, 'dest');
    
    [testDir, sourceDir, destDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
    
    // 3. Create real-time task
    console.log('\n3. ➕ Creating real-time sync task...');
    
    const taskData = {
      name: 'UI State Test Task',
      sourcePath: sourceDir,
      destinationPath: destDir,
      syncType: 'source-to-destination',
      options: {
        deleteExtraFiles: false,
        preserveTimestamps: true,
        enableRealtime: true
      }
    };
    
    const createResponse = await axios.post(`${baseURL}/sync/tasks`, taskData, { headers });
    const taskId = createResponse.data.task.id;
    console.log(`✅ Created task with ID: ${taskId}`);
    
    // 4. Start real-time sync
    console.log('\n4. ⚡ Starting real-time sync...');
    
    const startResponse = await axios.post(
      `${baseURL}/sync/tasks/${taskId}/realtime/start`, 
      {}, 
      { headers }
    );
    
    console.log('✅ Real-time sync started:', startResponse.data);
    
    // 5. Verify server state
    console.log('\n5. 📊 Checking server state...');
    
    const tasksResponse = await axios.get(`${baseURL}/sync/tasks`, { headers });
    const tasks = tasksResponse.data.tasks || [];
    const ourTask = tasks.find(t => t.id === taskId);
    
    if (ourTask) {
      console.log(`📊 Server task status: ${ourTask.status}`);
      console.log(`📊 Real-time enabled: ${ourTask.options?.enableRealtime}`);
      
      if (ourTask.status === 'monitoring') {
        console.log('✅ Server state: Task is monitoring');
      } else {
        console.log(`❌ Server state: Unexpected status ${ourTask.status}`);
      }
    }
    
    // 6. Instructions for UI verification
    console.log('\n6. 🖥️ UI STATE VERIFICATION INSTRUCTIONS:');
    console.log('   📝 Follow these steps to verify UI state sync:');
    console.log('   ');
    console.log('   BEFORE RESTART:');
    console.log('   1. 👀 Open SyncMasterPro UI in browser/Electron');
    console.log('   2. 📋 Go to Sync Tasks page');
    console.log('   3. 🔍 Find "UI State Test Task"');
    console.log('   4. ✅ Verify button shows "Stop" (red button)');
    console.log('   5. ✅ Verify status shows "Monitoring" (purple badge)');
    console.log('   ');
    console.log('   RESTART PROCESS:');
    console.log('   6. 🛑 Stop the SyncMasterPro server (Ctrl+C)');
    console.log('   7. ⏳ Wait 3 seconds');
    console.log('   8. 🚀 Restart the SyncMasterPro server');
    console.log('   9. ⏳ Wait for "Task auto-resume completed" message');
    console.log('   10. 🔄 Refresh the UI page (F5 or Ctrl+R)');
    console.log('   ');
    console.log('   AFTER RESTART:');
    console.log('   11. 🔍 Find "UI State Test Task" again');
    console.log('   12. ✅ Verify button STILL shows "Stop" (red button)');
    console.log('   13. ✅ Verify status STILL shows "Monitoring" (purple badge)');
    console.log('   14. ✅ Verify task is NOT showing "Start" (green button)');
    console.log('   ');
    console.log('   ✅ Press Enter here when you have verified the UI...');
    
    // Wait for user input
    await waitForEnter();
    
    // 7. Verify server state after restart
    console.log('\n7. 🔍 Verifying server state after restart...');

    let postRestartTask = null;
    try {
      const postRestartResponse = await axios.get(`${baseURL}/sync/tasks`, { headers });
      const postRestartTasks = postRestartResponse.data.tasks || [];
      postRestartTask = postRestartTasks.find(t => t.id === taskId);
      
      if (postRestartTask) {
        console.log(`📊 Post-restart server status: ${postRestartTask.status}`);
        console.log(`📊 Real-time enabled: ${postRestartTask.options?.enableRealtime}`);
        
        if (postRestartTask.status === 'monitoring') {
          console.log('✅ Server state: Task correctly resumed to monitoring');
        } else {
          console.log(`❌ Server state: Unexpected status ${postRestartTask.status}`);
        }
      } else {
        console.log('❌ Task not found after restart');
      }
      
    } catch (error) {
      console.log('❌ Failed to check post-restart state:', error.message);
    }
    
    // 8. Test file sync to verify functionality
    console.log('\n8. 📄 Testing sync functionality...');
    
    const testFile = path.join(sourceDir, 'ui-state-test.txt');
    fs.writeFileSync(testFile, `UI state test file\nCreated: ${new Date().toISOString()}`);
    
    console.log('✅ Test file created');
    console.log('⏳ Waiting 5 seconds for sync...');
    
    await delay(5000);
    
    const destFile = path.join(destDir, 'ui-state-test.txt');
    const fileSynced = fs.existsSync(destFile);
    
    console.log(`📊 File synced: ${fileSynced ? '✅ YES' : '❌ NO'}`);
    
    // 9. Summary
    console.log('\n📊 UI STATE SYNC TEST SUMMARY:');
    console.log('   ');
    console.log('   Expected UI behavior after restart:');
    console.log('   ✅ Task status: "Monitoring" (purple badge)');
    console.log('   ✅ Button: "Stop" (red button)');
    console.log('   ✅ Edit/Delete: Disabled (grayed out)');
    console.log('   ✅ Progress bar: Hidden (no active sync)');
    console.log('   ');
    console.log('   Server verification:');
    console.log(`   📊 Task found: ${postRestartTask ? '✅ YES' : '❌ NO'}`);
    console.log(`   📊 Status: ${postRestartTask?.status || 'unknown'}`);
    console.log(`   📊 Functionality: ${fileSynced ? '✅ WORKING' : '❌ BROKEN'}`);
    
    const uiStateWorking = postRestartTask?.status === 'monitoring' && fileSynced;
    
    console.log(`\n🎯 UI STATE SYNC: ${uiStateWorking ? '✅ WORKING' : '❌ NEEDS FIX'}`);
    
    if (uiStateWorking) {
      console.log('🎉 UI state synchronization is working correctly!');
      console.log('💡 Frontend correctly reflects server state after restart');
    } else {
      console.log('🔧 UI state synchronization needs improvement');
      console.log('💡 Check browser console for activeSyncs state updates');
    }
    
    // Cleanup
    console.log('\n🧹 Cleaning up...');
    try {
      await axios.post(`${baseURL}/sync/tasks/${taskId}/realtime/stop`, {}, { headers });
      await axios.delete(`${baseURL}/sync/tasks/${taskId}`, { headers });
      console.log('✅ Test task cleaned up');
    } catch (error) {
      console.log('⚠️ Failed to cleanup test task:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function waitForEnter() {
  return new Promise(resolve => {
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.on('data', () => {
      process.stdin.setRawMode(false);
      process.stdin.pause();
      resolve();
    });
  });
}

// Export for manual use
module.exports = { testUIStateSync };

// Auto-run if called directly
if (require.main === module) {
  testUIStateSync();
}

console.log('🔄 UI State Sync Tester loaded!');
console.log('📝 Run: node scripts/test-ui-state-sync.js');
