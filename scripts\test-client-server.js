#!/usr/bin/env node

/**
 * Script to test client-server connection
 * This will manually trigger ClientManager and test the connection
 */

const axios = require('axios');

console.log('🧪 Testing Client-Server Connection');
console.log('==================================');

async function testWebServerAPI() {
  console.log('\n1. Testing Web Server API...');
  
  try {
    // Test basic server health
    const healthResponse = await axios.get('http://localhost:5001/api/health', {
      timeout: 5000
    });
    console.log('✅ Web Server Health:', healthResponse.data);
  } catch (error) {
    console.log('❌ Web Server Health Check Failed:', error.message);
  }
  
  try {
    // Test clients endpoint (should return empty array or require auth)
    const clientsResponse = await axios.get('http://localhost:5001/api/clients', {
      timeout: 5000,
      validateStatus: () => true // Accept any status code
    });
    console.log('📊 Clients Endpoint Status:', clientsResponse.status);
    console.log('📊 Clients Response:', clientsResponse.data);
  } catch (error) {
    console.log('❌ Clients Endpoint Error:', error.message);
  }
}

async function testDesktopServerAPI() {
  console.log('\n2. Testing Desktop Server API...');
  
  try {
    // Test basic server health
    const healthResponse = await axios.get('http://localhost:5002/api/health', {
      timeout: 5000
    });
    console.log('✅ Desktop Server Health:', healthResponse.data);
  } catch (error) {
    console.log('❌ Desktop Server Health Check Failed:', error.message);
  }
}

async function simulateClientRegistration() {
  console.log('\n3. Simulating Client Registration...');
  
  const mockClient = {
    clientId: 'test-client-' + Date.now(),
    hostname: 'TEST-DESKTOP',
    platform: 'win32',
    version: '1.0.0',
    userId: 1
  };
  
  try {
    const response = await axios.post('http://localhost:5001/api/clients/register', mockClient, {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📝 Registration Status:', response.status);
    console.log('📝 Registration Response:', response.data);
    
    if (response.status === 200 || response.status === 201) {
      console.log('✅ Mock client registered successfully!');
      return mockClient.clientId;
    } else {
      console.log('⚠️ Registration returned non-success status');
      return null;
    }
  } catch (error) {
    console.log('❌ Registration Error:', error.message);
    return null;
  }
}

async function testClientsList() {
  console.log('\n4. Testing Clients List...');
  
  try {
    // Try to get clients list without auth first
    const response = await axios.get('http://localhost:5001/api/clients', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📋 Clients List Status:', response.status);
    
    if (response.status === 200) {
      console.log('📋 Registered Clients:', response.data);
      console.log('📊 Total Clients:', response.data.length);
    } else {
      console.log('📋 Clients List Response:', response.data);
    }
  } catch (error) {
    console.log('❌ Clients List Error:', error.message);
  }
}

async function checkDatabaseSync() {
  console.log('\n5. Checking Database Sync Status...');
  
  try {
    // Check if there are any clients in PostgreSQL
    const response = await axios.get('http://localhost:5001/api/database/sync-status', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('🔄 Database Sync Status:', response.status);
    if (response.data) {
      console.log('🔄 Sync Info:', response.data);
    }
  } catch (error) {
    console.log('❌ Database Sync Check Error:', error.message);
  }
}

async function main() {
  try {
    console.log('🚀 Starting Client-Server Connection Test...\n');
    
    await testWebServerAPI();
    await testDesktopServerAPI();
    
    const clientId = await simulateClientRegistration();
    
    await testClientsList();
    await checkDatabaseSync();
    
    console.log('\n📊 Test Summary:');
    console.log('================');
    console.log('- Web Server: Running on port 5001');
    console.log('- Desktop Server: Running on port 5002');
    console.log('- Mock Client ID:', clientId || 'Failed to register');
    
    console.log('\n💡 Next Steps:');
    console.log('1. Wait for React dev server to finish loading');
    console.log('2. Login to desktop app to trigger real ClientManager');
    console.log('3. Check web management interface for real data');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { testWebServerAPI, testDesktopServerAPI, simulateClientRegistration };
