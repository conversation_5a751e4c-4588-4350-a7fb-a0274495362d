const express = require('express');
const router = express.Router();
const AuditTrailService = require('../services/AuditTrailService');
const authenticateToken = require('../middleware/auth');

// Lazy initialization of service
let auditService = null;
const getAuditService = () => {
  if (!auditService) {
    auditService = new AuditTrailService();
  }
  return auditService;
};

// Get audit trail
router.get('/trail', authenticateToken, async (req, res) => {
  try {
    const {
      userId,
      action,
      resource,
      startDate,
      endDate,
      severity,
      limit = 100,
      offset = 0
    } = req.query;

    const filters = {
      userId: userId || req.userId,
      action,
      resource,
      startDate,
      endDate,
      severity,
      limit: parseInt(limit),
      offset: parseInt(offset)
    };

    const auditTrail = await getAuditService().getAuditTrail(filters);

    res.json({
      success: true,
      auditTrail
    });
  } catch (error) {
    console.error('Failed to get audit trail:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get audit trail'
    });
  }
});

// Get audit statistics
router.get('/statistics', authenticateToken, async (req, res) => {
  try {
    const { timeRange = '30d' } = req.query;
    
    const statistics = await getAuditService().getAuditStatistics(timeRange);
    
    res.json({
      success: true,
      statistics
    });
  } catch (error) {
    console.error('Failed to get audit statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get audit statistics'
    });
  }
});

// Log audit event (internal use)
router.post('/log', authenticateToken, async (req, res) => {
  try {
    const {
      action,
      resource,
      resourceId,
      details = {}
    } = req.body;

    if (!action) {
      return res.status(400).json({
        success: false,
        error: 'Action is required'
      });
    }

    const eventData = {
      userId: req.userId,
      action,
      resource,
      resourceId,
      details,
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      sessionId: req.token
    };

    const auditEntry = await getAuditService().logEvent(eventData);

    res.json({
      success: true,
      message: 'Audit event logged successfully',
      auditEntry: {
        id: auditEntry.id,
        action: auditEntry.action,
        timestamp: auditEntry.timestamp,
        severity: auditEntry.severity
      }
    });
  } catch (error) {
    console.error('Failed to log audit event:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to log audit event'
    });
  }
});

// Get security alerts
router.get('/alerts', authenticateToken, async (req, res) => {
  try {
    const {
      userId,
      type,
      severity,
      status = 'active',
      limit = 50,
      offset = 0
    } = req.query;

    const filters = {
      userId,
      type,
      severity,
      status,
      limit: parseInt(limit),
      offset: parseInt(offset)
    };

    const alerts = await getAuditService().getSecurityAlerts(filters);

    res.json({
      success: true,
      alerts,
      count: alerts.length
    });
  } catch (error) {
    console.error('Failed to get security alerts:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get security alerts'
    });
  }
});

// Resolve security alert
router.post('/alerts/:alertId/resolve', authenticateToken, async (req, res) => {
  try {
    const { alertId } = req.params;
    
    const result = await getAuditService().resolveSecurityAlert(alertId, req.userId);

    // Log the resolution
    await getAuditService().logEvent({
      userId: req.userId,
      action: 'alert_resolved',
      resource: 'security_alert',
      resourceId: alertId,
      details: { resolvedBy: req.userId },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.token
    });

    res.json({
      success: true,
      message: result.message
    });
  } catch (error) {
    console.error('Failed to resolve security alert:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to resolve security alert'
    });
  }
});

// Generate compliance report
router.post('/compliance/report', authenticateToken, async (req, res) => {
  try {
    const { timeRange = '30d' } = req.body;
    
    const report = await getAuditService().generateComplianceReport(timeRange);

    // Log report generation
    await getAuditService().logEvent({
      userId: req.userId,
      action: 'compliance_report_generated',
      resource: 'compliance_report',
      resourceId: report.id,
      details: { timeRange },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.token
    });

    res.json({
      success: true,
      report
    });
  } catch (error) {
    console.error('Failed to generate compliance report:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate compliance report'
    });
  }
});

// Get audit dashboard
router.get('/dashboard', authenticateToken, async (req, res) => {
  try {
    const timeRange = '7d';
    const statistics = await getAuditService().getAuditStatistics(timeRange);
    const alerts = await getAuditService().getSecurityAlerts({ status: 'active', limit: 10 });
    
    const dashboard = {
      timeRange,
      summary: {
        totalEvents: statistics.actionStatistics.reduce((sum, stat) => sum + stat.count, 0),
        criticalEvents: statistics.severityStatistics.find(s => s.severity === 'critical')?.count || 0,
        activeAlerts: alerts.length,
        topAction: statistics.actionStatistics[0]?.action || 'none',
        mostActiveUser: statistics.userActivity[0]?.userEmail || 'none'
      },
      trends: {
        dailyActivity: statistics.dailyActivity.slice(0, 7),
        actionBreakdown: statistics.actionStatistics.slice(0, 5),
        severityDistribution: statistics.severityStatistics
      },
      alerts: {
        recent: alerts.slice(0, 5),
        bySeverity: alerts.reduce((acc, alert) => {
          acc[alert.severity] = (acc[alert.severity] || 0) + 1;
          return acc;
        }, {}),
        byType: alerts.reduce((acc, alert) => {
          acc[alert.type] = (acc[alert.type] || 0) + 1;
          return acc;
        }, {})
      },
      compliance: {
        auditCoverage: statistics.actionStatistics.length > 0 ? 'good' : 'needs_improvement',
        securityScore: alerts.length === 0 ? 'excellent' : alerts.length < 5 ? 'good' : 'needs_attention',
        lastReportGenerated: new Date().toISOString()
      }
    };

    res.json({
      success: true,
      dashboard
    });
  } catch (error) {
    console.error('Failed to get audit dashboard:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get audit dashboard'
    });
  }
});

// Export audit data
router.get('/export', authenticateToken, async (req, res) => {
  try {
    const {
      timeRange = '30d',
      format = 'json',
      includeAlerts = 'true'
    } = req.query;

    const statistics = await getAuditService().getAuditStatistics(timeRange);
    const auditTrail = await getAuditService().getAuditTrail({
      limit: 1000,
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    });

    let alerts = [];
    if (includeAlerts === 'true') {
      alerts = await getAuditService().getSecurityAlerts({ limit: 100 });
    }

    const exportData = {
      exportedAt: new Date().toISOString(),
      timeRange,
      statistics,
      auditTrail: auditTrail.entries,
      alerts
    };

    // Log export action
    await getAuditService().logEvent({
      userId: req.userId,
      action: 'audit_data_exported',
      resource: 'audit_export',
      details: { timeRange, format, includeAlerts },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.token
    });

    if (format === 'csv') {
      const csvData = convertAuditToCSV(auditTrail.entries);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename=audit-trail-${timeRange}.csv`);
      res.send(csvData);
    } else {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename=audit-data-${timeRange}.json`);
      res.json(exportData);
    }
  } catch (error) {
    console.error('Failed to export audit data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to export audit data'
    });
  }
});

// Search audit trail
router.post('/search', authenticateToken, async (req, res) => {
  try {
    const {
      query,
      filters = {},
      limit = 100,
      offset = 0
    } = req.body;

    // Add text search to filters
    const searchFilters = {
      ...filters,
      limit: parseInt(limit),
      offset: parseInt(offset)
    };

    const auditTrail = await getAuditService().getAuditTrail(searchFilters);

    // Filter results by query if provided
    let filteredEntries = auditTrail.entries;
    if (query) {
      const searchTerm = query.toLowerCase();
      filteredEntries = auditTrail.entries.filter(entry => 
        entry.action.toLowerCase().includes(searchTerm) ||
        entry.resource?.toLowerCase().includes(searchTerm) ||
        entry.userEmail?.toLowerCase().includes(searchTerm) ||
        JSON.stringify(entry.details).toLowerCase().includes(searchTerm)
      );
    }

    res.json({
      success: true,
      results: {
        entries: filteredEntries,
        total: filteredEntries.length,
        query,
        filters
      }
    });
  } catch (error) {
    console.error('Failed to search audit trail:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to search audit trail'
    });
  }
});

// Helper function to convert audit trail to CSV
function convertAuditToCSV(entries) {
  const rows = [];
  
  // Headers
  rows.push('Timestamp,User,Action,Resource,Resource ID,Severity,IP Address,Details');
  
  // Data rows
  entries.forEach(entry => {
    rows.push([
      entry.timestamp,
      entry.userEmail || 'System',
      entry.action,
      entry.resource || '',
      entry.resourceId || '',
      entry.severity,
      entry.ipAddress || '',
      JSON.stringify(entry.details).replace(/,/g, ';') // Replace commas to avoid CSV issues
    ].join(','));
  });
  
  return rows.join('\n');
}

module.exports = router;
