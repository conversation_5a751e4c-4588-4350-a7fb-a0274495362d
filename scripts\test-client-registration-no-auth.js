#!/usr/bin/env node

/**
 * Test client registration without authentication
 */

const axios = require('axios');

console.log('🧪 Testing Client Registration (No Auth Required)');
console.log('==================================================');

async function testClientRegistrationNoAuth() {
  console.log('\n1. 📡 Testing client registration without auth...');
  
  const mockClient = {
    clientId: 'test-client-' + Date.now(),
    hostname: 'TEST-DESKTOP',
    platform: 'win32',
    arch: 'x64',
    version: '1.0.0',
    nodeVersion: process.version,
    totalMemory: 8589934592,
    cpuCount: 8,
    userId: 1  // Hardcoded user ID
  };
  
  try {
    const response = await axios.post('http://localhost:5001/api/clients/register', mockClient, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Registration Status:', response.status);
    console.log('📝 Registration Response:', response.data);
    
    if (response.status === 200 || response.status === 201) {
      console.log('✅ Client registration successful!');
      return { success: true, clientId: mockClient.clientId };
    } else {
      console.log('❌ Client registration failed');
      return { success: false, error: response.data };
    }
  } catch (error) {
    console.log('❌ Registration error:', error.message);
    return { success: false, error: error.message };
  }
}

async function checkRegisteredClients() {
  console.log('\n2. 📊 Checking registered clients...');
  
  try {
    const response = await axios.get('http://localhost:5001/api/clients', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📝 Clients API Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Clients API accessible!');
      console.log('📊 Current Clients:', response.data);
      return { success: true, clients: response.data };
    } else {
      console.log('❌ Clients API failed:', response.data);
      return { success: false, error: response.data };
    }
  } catch (error) {
    console.log('❌ Clients API error:', error.message);
    return { success: false, error: error.message };
  }
}

async function testHealthCheck() {
  console.log('\n3. 🏥 Testing health check...');
  
  try {
    const response = await axios.get('http://localhost:5001/api/health', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📝 Health Check Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Health check passed!');
      console.log('📊 Server Info:', response.data);
      return true;
    } else {
      console.log('❌ Health check failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Health check error:', error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting client registration test...\n');
    
    // Test health check first
    const healthOk = await testHealthCheck();
    
    // Test client registration
    const registrationResult = await testClientRegistrationNoAuth();
    
    // Check clients list
    const clientsResult = await checkRegisteredClients();
    
    console.log('\n📊 Test Summary:');
    console.log('================');
    console.log('- Health Check:', healthOk ? '✅ Success' : '❌ Failed');
    console.log('- Client Registration:', registrationResult.success ? '✅ Success' : '❌ Failed');
    console.log('- Clients API:', clientsResult.success ? '✅ Success' : '❌ Failed');
    
    if (healthOk && registrationResult.success && clientsResult.success) {
      console.log('\n🎉 SUCCESS! Client registration is working without auth!');
      console.log('🔍 Now desktop ClientManager should be able to register');
      
      // Check if our test client is in the list
      const ourClient = clientsResult.clients.find(c => c.client_id === registrationResult.clientId);
      if (ourClient) {
        console.log('✅ Test client found in database:', ourClient.client_id);
      } else {
        console.log('⚠️ Test client not found in database list');
      }
      
    } else if (!healthOk) {
      console.log('\n❌ Web server health check failed');
      console.log('💡 Check if web server is running properly');
    } else if (!registrationResult.success) {
      console.log('\n❌ Client registration still requires authentication');
      console.log('💡 Check if code changes were applied correctly');
      console.log('💡 Error:', registrationResult.error);
    } else {
      console.log('\n❌ Clients API still requires authentication');
      console.log('💡 Check clients API endpoint configuration');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
