import React from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useSettings } from '../../../contexts/SettingsContext';
import Toggle from '../../UI/Toggle';

const SecuritySettingsPanel = () => {
  const { t } = useLanguage();
  const { settings, updateSetting } = useSettings();

  const sessionTimeoutOptions = [
    { value: 900, label: t('fifteenMinutes') },
    { value: 1800, label: t('thirtyMinutes') },
    { value: 3600, label: t('oneHour') },
    { value: 7200, label: t('twoHours') },
    { value: 14400, label: t('fourHours') },
    { value: 28800, label: t('eightHours') }
  ];

  const handleSessionTimeoutChange = (timeout) => {
    updateSetting('sessionTimeout', timeout);
  };

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t('securitySettings')}</h2>
        <p className="text-gray-600 dark:text-gray-300 mt-1">{t('securitySettingsDescription')}</p>
      </div>

      {/* Session Management */}
      <div className="space-y-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white">{t('sessionManagement')}</h3>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('sessionTimeout')}
          </label>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">{t('sessionTimeoutDescription')}</p>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {sessionTimeoutOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => handleSessionTimeoutChange(option.value)}
                className={`px-3 py-2 text-sm font-medium rounded-md border transition-colors ${
                  settings.sessionTimeout === option.value
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                    : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Password & Authentication */}
      <div className="space-y-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white">{t('passwordAuthentication')}</h3>
        
        <div className="space-y-4">
          <Toggle
            label={t('requirePasswordChange')}
            description={t('requirePasswordChangeDescription')}
            checked={settings.requirePasswordChange}
            onChange={(checked) => updateSetting('requirePasswordChange', checked)}
          />
        </div>
      </div>

      {/* Privacy Settings */}
      <div className="space-y-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white">{t('privacySettings')}</h3>
        
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-green-500 text-xl">🔒</span>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                {t('dataProtection')}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                {t('dataProtectionDescription')}
              </p>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">{t('encryptionStatus')}</span>
                  <span className="text-sm font-medium text-green-600 dark:text-green-400">
                    {t('enabled')}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">{t('secureTransmission')}</span>
                  <span className="text-sm font-medium text-green-600 dark:text-green-400">
                    HTTPS/TLS
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">{t('currentSessionTimeout')}</span>
                  <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                    {sessionTimeoutOptions.find(opt => opt.value === settings.sessionTimeout)?.label || t('custom')}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Security Recommendations */}
      <div className="space-y-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white">{t('securityRecommendations')}</h3>
        
        <div className="space-y-3">
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-blue-400 text-xl">💡</span>
              </div>
              <div className="ml-3">
                <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  {t('securityTips')}
                </h4>
                <ul className="mt-2 text-sm text-blue-700 dark:text-blue-300 space-y-1">
                  <li>• {t('useStrongPassword')}</li>
                  <li>• {t('enableTwoFactor')}</li>
                  <li>• {t('regularlyUpdatePassword')}</li>
                  <li>• {t('logoutSharedDevices')}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Audit & Monitoring */}
      <div className="space-y-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white">{t('auditMonitoring')}</h3>
        
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-purple-500 text-xl">📊</span>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                {t('activityMonitoring')}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                {t('activityMonitoringDescription')}
              </p>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">{t('loginTracking')}</span>
                  <span className="text-sm font-medium text-green-600 dark:text-green-400">
                    {t('active')}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">{t('syncActivityLog')}</span>
                  <span className="text-sm font-medium text-green-600 dark:text-green-400">
                    {t('active')}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">{t('errorReporting')}</span>
                  <span className="text-sm font-medium text-green-600 dark:text-green-400">
                    {t('active')}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecuritySettingsPanel;
