const EventEmitter = require('events');

class DatabaseChangeListener extends EventEmitter {
  constructor() {
    super();
    this.isListening = false;
    this.databaseSyncService = null;
    this.realTimeDatabaseSync = null;
    this.debounceTimeout = null;
    this.pendingChanges = [];
    this.debounceDelay = 1000; // 1 second debounce
  }

  // Initialize with database sync service
  initialize(databaseSyncService, realTimeDatabaseSync = null) {
    this.databaseSyncService = databaseSyncService;
    this.realTimeDatabaseSync = realTimeDatabaseSync;
    
    console.log('🎧 Database Change Listener initialized');
    
    if (this.realTimeDatabaseSync) {
      console.log('✅ Real-time database sync integration enabled');
    }
  }

  // Start listening for database changes
  startListening() {
    if (this.isListening) {
      console.log('⚠️ Database change listener already running');
      return;
    }

    this.isListening = true;
    
    console.log('🎧 Starting database change listener...');
    
    // Listen for various database change events
    const changeEvents = [
      'user-created',
      'user-updated', 
      'user-deleted',
      'task-created',
      'task-updated',
      'task-deleted',
      // 'sync-completed', // Removed to prevent infinite loops
      'sync-history-added',
      'session-created',
      'session-deleted',
      'client-registered',
      'client-updated',
      'client-disconnected'
    ];

    changeEvents.forEach(eventType => {
      this.on(eventType, (data) => this.handleDatabaseChange(eventType, data));
    });

    console.log(`✅ Listening for ${changeEvents.length} database change events`);
    console.log('🎧 Real-time database sync enabled');
  }

  // Stop listening for database changes
  stopListening() {
    if (!this.isListening) {
      console.log('⚠️ Database change listener not running');
      return;
    }

    this.isListening = false;
    
    // Clear any pending debounced operations
    if (this.debounceTimeout) {
      clearTimeout(this.debounceTimeout);
      this.debounceTimeout = null;
    }
    
    this.pendingChanges = [];
    this.removeAllListeners();
    
    console.log('🔇 Database change listener stopped');
  }

  // Handle database change events
  handleDatabaseChange(changeType, data) {
    if (!this.isListening) return;

    // Prevent infinite loops: Don't sync on sync completion events
    if (changeType === 'sync-completed') {
      console.log(`🔄 Ignoring sync-completed event to prevent infinite loop`);
      return;
    }

    console.log(`📝 Database change detected: ${changeType}`, {
      table: data?.table,
      id: data?.id,
      timestamp: new Date().toISOString()
    });

    // Forward to real-time sync if available
    if (this.realTimeDatabaseSync) {
      this.realTimeDatabaseSync.handleDatabaseChange(changeType, data);
    } else {
      // Fallback to debounced sync
      this.addPendingChange(changeType, data);
      this.debouncedSync();
    }
  }

  // Add change to pending list
  addPendingChange(changeType, data) {
    this.pendingChanges.push({
      type: changeType,
      data,
      timestamp: new Date()
    });

    // Limit pending changes to prevent memory issues
    if (this.pendingChanges.length > 100) {
      this.pendingChanges = this.pendingChanges.slice(-50); // Keep last 50
    }
  }

  // Debounced sync function
  debouncedSync() {
    if (this.debounceTimeout) {
      clearTimeout(this.debounceTimeout);
    }

    this.debounceTimeout = setTimeout(() => {
      this.processPendingChanges();
    }, this.debounceDelay);
  }

  // Process accumulated changes
  async processPendingChanges() {
    if (!this.isListening || this.pendingChanges.length === 0) {
      return;
    }

    const changesToProcess = [...this.pendingChanges];
    this.pendingChanges = [];

    console.log(`🔄 Processing ${changesToProcess.length} database changes...`);

    try {
      if (this.databaseSyncService) {
        // Extract unique tables from changes
        const affectedTables = [...new Set(
          changesToProcess
            .map(change => change.data?.table)
            .filter(table => table)
        )];

        if (affectedTables.length > 0) {
          console.log(`🎯 Syncing affected tables: ${affectedTables.join(', ')}`);
          
          // Use targeted sync if available
          if (this.databaseSyncService.syncSpecificTables) {
            await this.databaseSyncService.syncSpecificTables(affectedTables);
          } else {
            // Fallback to full sync
            await this.databaseSyncService.performSync();
          }
        }
      }
    } catch (error) {
      console.error('❌ Failed to process database changes:', error);
      
      // Retry after a delay
      setTimeout(() => {
        if (this.isListening) {
          console.log('🔄 Retrying database sync...');
          this.processPendingChanges();
        }
      }, 5000);
    }
  }

  // Notify about specific database events
  notifyUserChange(action, userId, userData = {}) {
    this.emit(`user-${action}`, {
      table: 'users',
      id: userId,
      action,
      data: userData,
      timestamp: new Date().toISOString()
    });
  }

  notifyTaskChange(action, taskId, taskData = {}) {
    this.emit(`task-${action}`, {
      table: 'sync_tasks',
      id: taskId,
      action,
      data: taskData,
      timestamp: new Date().toISOString()
    });
  }

  notifySessionChange(action, sessionId, sessionData = {}) {
    this.emit(`session-${action}`, {
      table: 'sessions',
      id: sessionId,
      action,
      data: sessionData,
      timestamp: new Date().toISOString()
    });
  }

  notifyClientChange(action, clientId, clientData = {}) {
    this.emit(`client-${action}`, {
      table: 'desktop_clients',
      id: clientId,
      action,
      data: clientData,
      timestamp: new Date().toISOString()
    });
  }

  notifySyncHistoryAdded(historyId, historyData = {}) {
    this.emit('sync-history-added', {
      table: 'sync_history',
      id: historyId,
      action: 'created',
      data: historyData,
      timestamp: new Date().toISOString()
    });
  }

  // Get current status
  getStatus() {
    return {
      isListening: this.isListening,
      pendingChanges: this.pendingChanges.length,
      hasRealTimeSync: !!this.realTimeDatabaseSync,
      hasDatabaseSync: !!this.databaseSyncService,
      debounceDelay: this.debounceDelay
    };
  }
}

module.exports = DatabaseChangeListener;
