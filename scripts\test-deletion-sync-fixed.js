#!/usr/bin/env node

/**
 * Test the new deletion sync functionality
 */

const axios = require('axios');
const Database = require('better-sqlite3');
const { Pool } = require('pg');
const path = require('path');

console.log('🗑️ Testing NEW Deletion Sync Functionality');
console.log('==========================================');

async function login() {
  console.log('\n1. 🔐 Logging in...');
  
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Login successful');
      return response.data.token;
    } else {
      console.log('❌ Login failed:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return null;
  }
}

async function checkInitialData() {
  console.log('\n2. 📊 Checking initial data...');
  
  // Connect to both databases
  const sqlitePath = path.join(__dirname, '../data/syncmasterpro.db');
  const sqlite = new Database(sqlitePath);
  
  const pgPool = new Pool({
    host: '*************',
    port: 5432,
    database: 'syncmasterpro',
    user: 'pi',
    password: 'ubuntu'
  });
  
  try {
    // Check tasks
    const sqliteTasks = sqlite.prepare('SELECT COUNT(*) as count, GROUP_CONCAT(name) as names FROM sync_tasks').get();
    const pgTasks = await pgPool.query('SELECT COUNT(*) as count, STRING_AGG(name, \', \') as names FROM sync_tasks');
    
    console.log(`🔄 Tasks - SQLite: ${sqliteTasks.count} (${sqliteTasks.names || 'none'})`);
    console.log(`🔄 Tasks - PostgreSQL: ${pgTasks.rows[0].count} (${pgTasks.rows[0].names || 'none'})`);
    
    return {
      sqlite: {
        tasks: sqliteTasks.count,
        taskNames: sqliteTasks.names
      },
      postgresql: {
        tasks: parseInt(pgTasks.rows[0].count),
        taskNames: pgTasks.rows[0].names
      }
    };
    
  } finally {
    sqlite.close();
    await pgPool.end();
  }
}

async function createTestTasks() {
  console.log('\n3. 🔧 Creating test tasks in desktop (SQLite)...');
  
  const sqlitePath = path.join(__dirname, '../data/syncmasterpro.db');
  const sqlite = new Database(sqlitePath);
  
  try {
    const insertTask = sqlite.prepare(`
      INSERT INTO sync_tasks (user_id, name, source_path, destination_path, sync_type, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const now = new Date().toISOString();
    
    // Create multiple test tasks
    const tasks = [
      'DELETION TEST TASK 1',
      'DELETION TEST TASK 2', 
      'DELETION TEST TASK 3'
    ];
    
    const createdIds = [];
    
    for (const taskName of tasks) {
      const result = insertTask.run(
        1, // user_id
        taskName,
        `C:\\temp\\${taskName.toLowerCase().replace(/\s+/g, '-')}-source`,
        `C:\\temp\\${taskName.toLowerCase().replace(/\s+/g, '-')}-dest`,
        'bidirectional',
        'idle',
        now,
        now
      );
      createdIds.push(result.lastInsertRowid);
      console.log(`✅ Created task: ${taskName} (ID: ${result.lastInsertRowid})`);
    }
    
    return createdIds;
    
  } finally {
    sqlite.close();
  }
}

async function syncDesktopToWeb(token) {
  console.log('\n4. 🔄 Syncing Desktop → Web (with new deletion logic)...');
  
  try {
    const response = await axios.post('http://localhost:5001/api/database-sync/sync', {
      direction: 'sqlite-to-pg'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Desktop → Web sync completed');
      console.log('📊 Sync Results:', JSON.stringify(response.data.results, null, 2));
      return response.data;
    } else {
      console.log('❌ Desktop → Web sync failed:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ Desktop → Web sync error:', error.message);
    return null;
  }
}

async function deleteTasksFromDesktop(taskIds) {
  console.log('\n5. 🗑️ Deleting some test tasks from desktop (SQLite)...');
  
  const sqlitePath = path.join(__dirname, '../data/syncmasterpro.db');
  const sqlite = new Database(sqlitePath);
  
  try {
    const deleteTask = sqlite.prepare('DELETE FROM sync_tasks WHERE id = ?');
    
    // Delete first 2 tasks, keep the 3rd one
    const tasksToDelete = taskIds.slice(0, 2);
    let deletedCount = 0;
    
    for (const taskId of tasksToDelete) {
      const result = deleteTask.run(taskId);
      if (result.changes > 0) {
        deletedCount++;
        console.log(`✅ Deleted task ID: ${taskId} from desktop`);
      }
    }
    
    console.log(`📊 Total deleted from desktop: ${deletedCount} tasks`);
    return { deletedCount, remainingTaskId: taskIds[2] };
    
  } finally {
    sqlite.close();
  }
}

async function syncAfterDeletion(token) {
  console.log('\n6. 🔄 Syncing after deletion (Desktop → Web with deletion sync)...');
  
  try {
    const response = await axios.post('http://localhost:5001/api/database-sync/sync', {
      direction: 'sqlite-to-pg'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Post-deletion sync completed');
      console.log('📊 Sync Results:', JSON.stringify(response.data.results, null, 2));
      return response.data;
    } else {
      console.log('❌ Post-deletion sync failed:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ Post-deletion sync error:', error.message);
    return null;
  }
}

async function checkFinalData() {
  console.log('\n7. 📊 Checking final data after deletion sync...');
  
  // Connect to both databases
  const sqlitePath = path.join(__dirname, '../data/syncmasterpro.db');
  const sqlite = new Database(sqlitePath);
  
  const pgPool = new Pool({
    host: '*************',
    port: 5432,
    database: 'syncmasterpro',
    user: 'pi',
    password: 'ubuntu'
  });
  
  try {
    // Check tasks
    const sqliteTasks = sqlite.prepare('SELECT COUNT(*) as count, GROUP_CONCAT(name) as names FROM sync_tasks WHERE name LIKE ?').get('%DELETION TEST%');
    const pgTasks = await pgPool.query('SELECT COUNT(*) as count, STRING_AGG(name, \', \') as names FROM sync_tasks WHERE name LIKE $1', ['%DELETION TEST%']);
    
    console.log(`🔄 Test Tasks - SQLite: ${sqliteTasks.count} (${sqliteTasks.names || 'none'})`);
    console.log(`🔄 Test Tasks - PostgreSQL: ${pgTasks.rows[0].count} (${pgTasks.rows[0].names || 'none'})`);
    
    return {
      sqlite: {
        testTasks: sqliteTasks.count,
        testTaskNames: sqliteTasks.names
      },
      postgresql: {
        testTasks: parseInt(pgTasks.rows[0].count),
        testTaskNames: pgTasks.rows[0].names
      }
    };
    
  } finally {
    sqlite.close();
    await pgPool.end();
  }
}

async function main() {
  try {
    console.log('🚀 Starting NEW deletion sync test...\n');
    
    // Login
    const token = await login();
    if (!token) {
      console.log('❌ Cannot proceed without authentication');
      return;
    }
    
    // Check initial data
    const initialData = await checkInitialData();
    
    // Create test tasks in desktop
    const testTaskIds = await createTestTasks();
    
    // Sync desktop to web (should add the new tasks)
    const syncResult1 = await syncDesktopToWeb(token);
    
    // Check data after first sync
    console.log('\n📊 Data after first sync:');
    const afterFirstSync = await checkInitialData();
    
    // Delete some tasks from desktop
    const deleteResult = await deleteTasksFromDesktop(testTaskIds);
    
    // Sync after deletion (should delete from web too)
    const syncResult2 = await syncAfterDeletion(token);
    
    // Check final data
    const finalData = await checkFinalData();
    
    console.log('\n📊 NEW DELETION SYNC TEST RESULTS:');
    console.log('==================================');
    console.log('Initial Test Tasks:');
    console.log(`  - Created: 3 tasks`);
    console.log(`  - Task IDs: ${testTaskIds.join(', ')}`);
    
    console.log('\nAfter First Sync (Desktop → Web):');
    console.log(`  - SQLite Test Tasks: ${finalData.sqlite.testTasks || 'N/A'}`);
    console.log(`  - PostgreSQL Test Tasks: ${finalData.postgresql.testTasks || 'N/A'}`);
    
    console.log('\nAfter Deletion from Desktop:');
    console.log(`  - Deleted from Desktop: ${deleteResult.deletedCount} tasks`);
    console.log(`  - Remaining in Desktop: 1 task`);
    
    console.log('\nAfter Deletion Sync (Desktop → Web):');
    console.log(`  - SQLite Test Tasks: ${finalData.sqlite.testTasks}`);
    console.log(`  - PostgreSQL Test Tasks: ${finalData.postgresql.testTasks}`);
    
    console.log('\n🎯 DELETION SYNC CONCLUSION:');
    if (finalData.sqlite.testTasks === finalData.postgresql.testTasks) {
      console.log('✅ DELETION SYNC WORKING!');
      console.log('   When you delete data from desktop (SQLite),');
      console.log('   it DOES get deleted from web (PostgreSQL)');
      console.log('   Both databases now have the same number of test tasks');
    } else {
      console.log('❌ DELETION SYNC NOT WORKING');
      console.log('   Databases still have different numbers of test tasks');
      console.log(`   SQLite: ${finalData.sqlite.testTasks}, PostgreSQL: ${finalData.postgresql.testTasks}`);
    }
    
    console.log('\n💡 New Database Sync Features:');
    console.log('   ✅ Deletion sync enabled by default');
    console.log('   ✅ Safety checks prevent deletion of admin users');
    console.log('   ✅ Safety checks prevent deletion of running tasks');
    console.log('   ✅ Bidirectional deletion sync support');
    console.log('   ✅ Detailed logging of deletion operations');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
