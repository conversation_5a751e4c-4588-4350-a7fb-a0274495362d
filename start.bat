@echo off
echo ========================================
echo    SyncMasterPro - Quick Start
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo Node.js and npm are installed ✓
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    echo This may take a few minutes...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        echo.
        pause
        exit /b 1
    )
    echo Dependencies installed ✓
    echo.
)

REM Run setup if needed
if not exist ".env" (
    echo Running initial setup...
    npm run setup
    if %errorlevel% neq 0 (
        echo ERROR: Setup failed
        echo.
        pause
        exit /b 1
    )
    echo Setup completed ✓
    echo.
)

echo Starting SyncMasterPro...
echo.
echo The application will open in a few moments.
echo You can close this window after the app starts.
echo.

REM Start the application
npm start

pause
