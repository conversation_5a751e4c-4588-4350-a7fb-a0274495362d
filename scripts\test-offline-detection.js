const axios = require('axios');

async function testOfflineDetection() {
  console.log('🧪 Testing Offline Detection on Web Dashboard\n');

  const webBaseURL = 'http://localhost:5001/api';
  const testUser = {
    email: '<EMAIL>',
    password: 'password123'
  };

  try {
    // 1. Login to web server to get token
    console.log('1. 🔐 Logging in to web server...');
    
    const loginResponse = await axios.post(`${webBaseURL}/auth/login`, testUser);
    const { token } = loginResponse.data;
    console.log(`✅ Web login successful - Token: ${token.substring(0, 20)}...`);

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. Check current clients status
    console.log('\n2. 📊 Checking current clients status...');
    
    const clientsResponse = await axios.get(`${webBaseURL}/clients`, { headers });
    const clients = clientsResponse.data.clients || [];
    
    console.log(`📋 Total clients in database: ${clients.length}`);
    
    if (clients.length === 0) {
      console.log('💡 No clients registered yet');
      console.log('💡 This is expected if no desktop app has ever connected');
    } else {
      console.log('\n📊 Client Status Details:');
      clients.forEach((client, index) => {
        const statusIcon = client.status === 'online' ? '🟢' : '🔴';
        const lastSeen = new Date(client.last_seen).toLocaleString();
        
        console.log(`   ${statusIcon} Client ${index + 1}: ${client.hostname}`);
        console.log(`      Status: ${client.status}`);
        console.log(`      Platform: ${client.platform}`);
        console.log(`      Last seen: ${lastSeen}`);
        console.log(`      Tasks: ${client.total_tasks || 0} total, ${client.active_tasks || 0} active`);
        console.log('');
      });
    }

    // 3. Check online vs offline counts
    console.log('3. 📈 Analyzing online/offline statistics...');
    
    const totalClients = clients.length;
    const onlineClients = clients.filter(c => c.status === 'online').length;
    const offlineClients = totalClients - onlineClients;
    
    console.log(`   📊 Total Clients: ${totalClients}`);
    console.log(`   🟢 Online Clients: ${onlineClients}`);
    console.log(`   🔴 Offline Clients: ${offlineClients}`);
    
    if (offlineClients > 0) {
      console.log('\n   ✅ SUCCESS: Web dashboard CAN detect offline clients!');
      console.log('   📋 Offline clients are shown with:');
      console.log('      - 🔴 Gray status dot');
      console.log('      - "offline" status badge');
      console.log('      - Last seen timestamp');
    } else if (onlineClients > 0) {
      console.log('\n   ℹ️ All registered clients are currently online');
      console.log('   💡 To test offline detection:');
      console.log('      1. Stop desktop app');
      console.log('      2. Wait 30+ seconds');
      console.log('      3. Refresh web dashboard');
    } else {
      console.log('\n   💡 No clients registered yet');
      console.log('   💡 Start desktop app and login to register a client');
    }

    // 4. Test real-time status check for specific client
    if (clients.length > 0) {
      console.log('\n4. 🔍 Testing real-time status check...');
      
      const testClient = clients[0];
      try {
        const statusResponse = await axios.get(`${webBaseURL}/clients/${testClient.client_id}/status`, { headers });
        const status = statusResponse.data.status;
        
        console.log(`   📊 Real-time status for ${testClient.hostname}:`);
        console.log(`      Database status: ${status.status}`);
        console.log(`      Socket connected: ${status.isConnected}`);
        console.log(`      Last seen: ${status.lastSeenFormatted}`);
        
        if (status.status === 'online' && !status.isConnected) {
          console.log('   ⚠️ WARNING: Database shows online but Socket.IO not connected');
          console.log('   💡 This indicates the client disconnected recently');
        } else if (status.status === 'offline') {
          console.log('   ✅ SUCCESS: Client correctly detected as offline');
        }
        
      } catch (error) {
        console.log(`   ❌ Failed to get real-time status: ${error.response?.data?.message || error.message}`);
      }
    }

    // 5. Simulate what web dashboard shows
    console.log('\n5. 🌐 Web Dashboard Display Simulation:');
    console.log('┌─────────────────────────────────────────────────────────┐');
    console.log('│                 Management Dashboard                    │');
    console.log('├─────────────────────────────────────────────────────────┤');
    console.log(`│ 📊 Stats:                                              │`);
    console.log(`│    Total Clients: ${totalClients.toString().padEnd(2)}                                    │`);
    console.log(`│    Online Clients: ${onlineClients.toString().padEnd(2)}                                   │`);
    console.log('├─────────────────────────────────────────────────────────┤');
    console.log('│ 📋 Connected Clients:                                  │');
    
    if (clients.length === 0) {
      console.log('│                                                         │');
      console.log('│    🖥️  No Desktop Clients Connected                   │');
      console.log('│        Start a desktop application and log in          │');
      console.log('│        to see it here.                                 │');
    } else {
      clients.forEach(client => {
        const statusIcon = client.status === 'online' ? '🟢' : '⚪';
        const statusText = client.status === 'online' ? 'online' : 'offline';
        const hostname = client.hostname.padEnd(15);
        const lastSeen = new Date(client.last_seen).toLocaleString();
        
        console.log('│                                                         │');
        console.log(`│ ${statusIcon} ${hostname} ${statusText.padEnd(7)}                    │`);
        console.log(`│    ${client.platform} • ${client.total_tasks || 0} tasks (${client.active_tasks || 0} active)                     │`);
        console.log(`│    Last seen: ${lastSeen}                │`);
      });
    }
    
    console.log('└─────────────────────────────────────────────────────────┘');

    console.log('\n🎉 Offline Detection Test Completed!');
    
    console.log('\n📋 SUMMARY:');
    console.log(`   ✅ Web API working: Can retrieve client status`);
    console.log(`   ✅ Status tracking: Database tracks online/offline`);
    console.log(`   ✅ Real-time check: Socket.IO connection status`);
    console.log(`   ✅ Visual indicators: Status dots and badges`);
    console.log(`   ✅ Timestamp tracking: Last seen information`);
    
    if (offlineClients > 0) {
      console.log(`   ✅ OFFLINE DETECTION: ${offlineClients} offline clients detected!`);
    } else {
      console.log(`   ℹ️ All clients online or no clients registered`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

// Run test
testOfflineDetection();
