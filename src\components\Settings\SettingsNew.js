import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useSettings } from '../../contexts/SettingsContext';
import {
  CogIcon,
  PaintBrushIcon,
  ArrowPathIcon,
  ShieldCheckIcon,
  CheckIcon,
  XMarkIcon,
  ArrowPathRoundedSquareIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

// Import individual setting components
import GeneralSettingsPanel from './panels/GeneralSettingsPanel';
import AppearanceSettingsPanel from './panels/AppearanceSettingsPanel';
import SyncSettingsPanel from './panels/SyncSettingsPanel';
import SecuritySettingsPanel from './panels/SecuritySettingsPanel';

const Settings = () => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const { 
    hasUnsavedChanges, 
    saving, 
    saveSettings, 
    resetToDefaults, 
    discardChanges,
    isElectron 
  } = useSettings();
  
  const [activeTab, setActiveTab] = useState('general');
  const [showResetConfirm, setShowResetConfirm] = useState(false);

  // Simplified tab structure - group related settings
  const tabs = [
    { 
      id: 'general', 
      name: t('general'), 
      icon: CogIcon, 
      description: t('generalSettingsDesc') 
    },
    { 
      id: 'appearance', 
      name: t('appearance'), 
      icon: PaintBrushIcon, 
      description: t('appearanceSettingsDesc') 
    },
    { 
      id: 'sync', 
      name: t('syncSettings'), 
      icon: ArrowPathIcon, 
      description: t('syncSettingsDesc') 
    },
    { 
      id: 'security', 
      name: t('security'), 
      icon: ShieldCheckIcon, 
      description: t('securitySettingsDesc') 
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return <GeneralSettingsPanel user={user} />;
      case 'appearance':
        return <AppearanceSettingsPanel />;
      case 'sync':
        return <SyncSettingsPanel />;
      case 'security':
        return <SecuritySettingsPanel />;
      default:
        return <GeneralSettingsPanel user={user} />;
    }
  };

  const handleSave = async () => {
    await saveSettings();
  };

  const handleReset = () => {
    setShowResetConfirm(true);
  };

  const confirmReset = () => {
    resetToDefaults();
    setShowResetConfirm(false);
  };

  const handleDiscard = () => {
    discardChanges();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('settingsTitle')}</h1>
          <p className="text-gray-600 dark:text-gray-300 mt-1">
            {t('settingsDescription')}
          </p>
        </div>
        
        {/* Save/Reset Controls */}
        {hasUnsavedChanges && (
          <div className="flex items-center space-x-3">
            <div className="flex items-center text-amber-600 dark:text-amber-400">
              <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
              <span className="text-sm font-medium">{t('unsavedChanges')}</span>
            </div>
            
            <button
              onClick={handleDiscard}
              disabled={saving}
              className="px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
            >
              <XMarkIcon className="w-4 h-4 mr-1 inline" />
              {t('discard')}
            </button>
            
            <button
              onClick={handleSave}
              disabled={saving}
              className="px-3 py-1.5 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md disabled:opacity-50 flex items-center"
            >
              <CheckIcon className="w-4 h-4 mr-1" />
              {saving ? t('saving') : t('saveChanges')}
            </button>
          </div>
        )}
      </div>

      {/* Settings Layout */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700">
            <nav className="p-4 space-y-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-start px-3 py-3 text-sm font-medium rounded-md transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300 border-r-2 border-blue-700 dark:border-blue-400'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  <tab.icon className="w-5 h-5 mr-3 mt-0.5 flex-shrink-0" />
                  <div className="text-left">
                    <div className="font-medium">{tab.name}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                      {tab.description}
                    </div>
                  </div>
                </button>
              ))}
            </nav>
            
            {/* Reset to Defaults Button */}
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={handleReset}
                disabled={saving}
                className="w-full flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
              >
                <ArrowPathRoundedSquareIcon className="w-4 h-4 mr-2" />
                {t('resetToDefaults')}
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 p-6">
            {renderTabContent()}
          </div>
        </div>
      </div>

      {/* Reset Confirmation Modal */}
      {showResetConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <ExclamationTriangleIcon className="w-6 h-6 text-amber-500 mr-3" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {t('confirmReset')}
              </h3>
            </div>
            
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              {t('confirmResetDescription')}
            </p>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowResetConfirm(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                {t('cancel')}
              </button>
              
              <button
                onClick={confirmReset}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md"
              >
                {t('resetToDefaults')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Settings;
