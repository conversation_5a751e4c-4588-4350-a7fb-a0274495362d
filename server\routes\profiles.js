const express = require('express');
const router = express.Router();
const authenticateToken = require('../middleware/auth');
const { getDatabase } = require('../database/init');

// Get all sync profiles for user
router.get('/', authenticateToken, async (req, res) => {
  try {
    const db = getDatabase();
    
    const result = await db.query(
      'SELECT * FROM sync_profiles WHERE user_id = ? ORDER BY is_default DESC, name ASC',
      [req.userId]
    );
    
    const profiles = result.rows.map(profile => ({
      id: profile.id,
      name: profile.name,
      description: profile.description,
      config: JSON.parse(profile.config),
      isDefault: profile.is_default,
      createdAt: profile.created_at,
      updatedAt: profile.updated_at
    }));
    
    res.json({
      success: true,
      profiles,
      count: profiles.length
    });
  } catch (error) {
    console.error('Failed to get sync profiles:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get sync profiles'
    });
  }
});

// Get specific sync profile
router.get('/:profileId', authenticateToken, async (req, res) => {
  try {
    const { profileId } = req.params;
    const db = getDatabase();
    
    const result = await db.query(
      'SELECT * FROM sync_profiles WHERE id = ? AND user_id = ?',
      [profileId, req.userId]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Profile not found'
      });
    }
    
    const profile = result.rows[0];
    
    res.json({
      success: true,
      profile: {
        id: profile.id,
        name: profile.name,
        description: profile.description,
        config: JSON.parse(profile.config),
        isDefault: profile.is_default,
        createdAt: profile.created_at,
        updatedAt: profile.updated_at
      }
    });
  } catch (error) {
    console.error('Failed to get sync profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get sync profile'
    });
  }
});

// Create new sync profile
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { name, description, config, isDefault = false } = req.body;
    
    if (!name || !config) {
      return res.status(400).json({
        success: false,
        error: 'Name and config are required'
      });
    }
    
    // Validate config structure
    const requiredFields = ['syncType', 'conflictStrategy', 'bandwidthLimit'];
    const configObj = typeof config === 'string' ? JSON.parse(config) : config;
    
    for (const field of requiredFields) {
      if (!(field in configObj)) {
        return res.status(400).json({
          success: false,
          error: `Config must include ${field}`
        });
      }
    }
    
    const db = getDatabase();
    
    // If setting as default, unset other defaults
    if (isDefault) {
      await db.query(
        'UPDATE sync_profiles SET is_default = FALSE WHERE user_id = ?',
        [req.userId]
      );
    }
    
    const result = await db.query(
      `INSERT INTO sync_profiles (user_id, name, description, config, is_default)
       VALUES (?, ?, ?, ?, ?)`,
      [req.userId, name, description || '', JSON.stringify(configObj), isDefault ? 1 : 0]
    );
    
    const profileId = result.insertId || result.rows[0]?.id;
    
    res.status(201).json({
      success: true,
      message: 'Sync profile created successfully',
      profileId,
      profile: {
        id: profileId,
        name,
        description: description || '',
        config: configObj,
        isDefault
      }
    });
  } catch (error) {
    console.error('Failed to create sync profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create sync profile'
    });
  }
});

// Update sync profile
router.put('/:profileId', authenticateToken, async (req, res) => {
  try {
    const { profileId } = req.params;
    const { name, description, config, isDefault } = req.body;
    const db = getDatabase();
    
    // Check if profile exists and belongs to user
    const existingResult = await db.query(
      'SELECT * FROM sync_profiles WHERE id = ? AND user_id = ?',
      [profileId, req.userId]
    );
    
    if (existingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Profile not found'
      });
    }
    
    const existing = existingResult.rows[0];
    
    // Prepare update data
    const updateData = {
      name: name || existing.name,
      description: description !== undefined ? description : existing.description,
      config: config ? JSON.stringify(typeof config === 'string' ? JSON.parse(config) : config) : existing.config,
      isDefault: isDefault !== undefined ? isDefault : existing.is_default
    };
    
    // If setting as default, unset other defaults
    if (updateData.isDefault && !existing.is_default) {
      await db.query(
        'UPDATE sync_profiles SET is_default = FALSE WHERE user_id = ? AND id != ?',
        [req.userId, profileId]
      );
    }
    
    await db.query(
      `UPDATE sync_profiles
       SET name = ?, description = ?, config = ?, is_default = ?, updated_at = CURRENT_TIMESTAMP
       WHERE id = ? AND user_id = ?`,
      [updateData.name, updateData.description, updateData.config, updateData.isDefault ? 1 : 0, profileId, req.userId]
    );
    
    res.json({
      success: true,
      message: 'Sync profile updated successfully',
      profile: {
        id: profileId,
        name: updateData.name,
        description: updateData.description,
        config: JSON.parse(updateData.config),
        isDefault: updateData.isDefault
      }
    });
  } catch (error) {
    console.error('Failed to update sync profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update sync profile'
    });
  }
});

// Delete sync profile
router.delete('/:profileId', authenticateToken, async (req, res) => {
  try {
    const { profileId } = req.params;
    const db = getDatabase();
    
    // Check if profile exists and belongs to user
    const result = await db.query(
      'SELECT * FROM sync_profiles WHERE id = ? AND user_id = ?',
      [profileId, req.userId]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Profile not found'
      });
    }
    
    const profile = result.rows[0];
    
    // Don't allow deleting default profile if it's the only one
    if (profile.is_default) {
      const countResult = await db.query(
        'SELECT COUNT(*) as count FROM sync_profiles WHERE user_id = ?',
        [req.userId]
      );
      
      if (countResult.rows[0].count <= 1) {
        return res.status(400).json({
          success: false,
          error: 'Cannot delete the only remaining profile'
        });
      }
      
      // Set another profile as default
      await db.query(
        'UPDATE sync_profiles SET is_default = TRUE WHERE user_id = ? AND id != ? LIMIT 1',
        [req.userId, profileId]
      );
    }
    
    await db.query(
      'DELETE FROM sync_profiles WHERE id = ? AND user_id = ?',
      [profileId, req.userId]
    );
    
    res.json({
      success: true,
      message: 'Sync profile deleted successfully'
    });
  } catch (error) {
    console.error('Failed to delete sync profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete sync profile'
    });
  }
});

// Set profile as default
router.post('/:profileId/set-default', authenticateToken, async (req, res) => {
  try {
    const { profileId } = req.params;
    const db = getDatabase();
    
    // Check if profile exists and belongs to user
    const result = await db.query(
      'SELECT * FROM sync_profiles WHERE id = ? AND user_id = ?',
      [profileId, req.userId]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Profile not found'
      });
    }
    
    // Unset all defaults for this user
    await db.query(
      'UPDATE sync_profiles SET is_default = FALSE WHERE user_id = ?',
      [req.userId]
    );
    
    // Set this profile as default
    await db.query(
      'UPDATE sync_profiles SET is_default = TRUE WHERE id = ? AND user_id = ?',
      [profileId, req.userId]
    );
    
    res.json({
      success: true,
      message: 'Profile set as default successfully'
    });
  } catch (error) {
    console.error('Failed to set default profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to set default profile'
    });
  }
});

// Get default profile
router.get('/default/config', authenticateToken, async (req, res) => {
  try {
    const db = getDatabase();
    
    const result = await db.query(
      'SELECT * FROM sync_profiles WHERE user_id = ? AND is_default = TRUE',
      [req.userId]
    );
    
    if (result.rows.length === 0) {
      // Return default configuration if no profile exists
      return res.json({
        success: true,
        profile: {
          id: null,
          name: 'Default',
          description: 'Default sync configuration',
          config: {
            syncType: 'bidirectional',
            conflictStrategy: 'ask_user',
            bandwidthLimit: 0, // No limit
            deleteExtraFiles: false,
            preserveTimestamps: true,
            followSymlinks: false,
            excludePatterns: ['*.tmp', '*.log', '.DS_Store'],
            maxFileSize: 0, // No limit
            versioning: {
              enabled: true,
              maxVersions: 10
            }
          },
          isDefault: true
        }
      });
    }
    
    const profile = result.rows[0];
    
    res.json({
      success: true,
      profile: {
        id: profile.id,
        name: profile.name,
        description: profile.description,
        config: JSON.parse(profile.config),
        isDefault: true
      }
    });
  } catch (error) {
    console.error('Failed to get default profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get default profile'
    });
  }
});

module.exports = router;
