const { getDatabase, initializeDatabase } = require('../server/database/init');

async function checkDatabaseClients() {
  console.log('🔍 Checking Database Clients\n');

  try {
    // Initialize database connection
    await initializeDatabase();
    const db = getDatabase();
    console.log('✅ Connected to database');

    // Check desktop_clients table
    console.log('\n📊 DESKTOP CLIENTS TABLE:');
    console.log('=' .repeat(80));

    const rows = await db.query(`
      SELECT
        id,
        client_id,
        user_id,
        hostname,
        status,
        metadata,
        last_seen,
        created_at
      FROM desktop_clients
      ORDER BY created_at DESC
    `);

    const clientRows = rows.rows || rows;
    console.log(`📋 Total clients found: ${clientRows.length}\n`);

    if (clientRows.length === 0) {
      console.log('📭 No clients found in database');
    } else {
      clientRows.forEach((row, index) => {
        console.log(`${index + 1}. CLIENT RECORD:`);
        console.log(`   🆔 ID: ${row.id}`);
        console.log(`   🖥️ Client ID: ${row.client_id}`);
        console.log(`   👤 User ID: ${row.user_id}`);
        console.log(`   🏠 Hostname: ${row.hostname}`);
        console.log(`   📊 Status: ${row.status}`);
        console.log(`   📝 Metadata: ${row.metadata || 'null'}`);
        console.log(`   👁️ Last Seen: ${row.last_seen}`);
        console.log(`   📅 Created: ${row.created_at}`);
        console.log('');
      });
    }

    // Summary by status
    console.log('📊 SUMMARY BY STATUS:');
    console.log('-'.repeat(40));

    const statusCounts = {};
    clientRows.forEach(row => {
      statusCounts[row.status] = (statusCounts[row.status] || 0) + 1;
    });

    Object.entries(statusCounts).forEach(([status, count]) => {
      const emoji = status === 'online' ? '🟢' : status === 'offline' ? '🔴' : '⚪';
      console.log(`   ${emoji} ${status}: ${count} clients`);
    });

    // Summary by user
    console.log('\n👥 SUMMARY BY USER:');
    console.log('-'.repeat(40));

    const userCounts = {};
    clientRows.forEach(row => {
      userCounts[row.user_id] = (userCounts[row.user_id] || 0) + 1;
    });

    Object.entries(userCounts).forEach(([userId, count]) => {
      console.log(`   👤 User ${userId}: ${count} clients`);
    });

    // Check for duplicates
    console.log('\n🔍 DUPLICATE CHECK:');
    console.log('-'.repeat(40));

    const clientIdCounts = {};
    clientRows.forEach(row => {
      clientIdCounts[row.client_id] = (clientIdCounts[row.client_id] || 0) + 1;
    });

    const duplicates = Object.entries(clientIdCounts).filter(([_, count]) => count > 1);

    if (duplicates.length === 0) {
      console.log('   ✅ No duplicate client IDs found');
    } else {
      console.log('   ⚠️ Duplicate client IDs found:');
      duplicates.forEach(([clientId, count]) => {
        console.log(`      🔄 ${clientId}: ${count} records`);
      });
    }

    // Check users table for reference
    console.log('\n👤 USERS TABLE REFERENCE:');
    console.log('-'.repeat(40));

    const userResult = await db.query(`
      SELECT id, email, name, created_at
      FROM users
      ORDER BY id
    `);

    const userRows = userResult.rows || userResult;
    userRows.forEach(user => {
      const clientCount = userCounts[user.id] || 0;
      console.log(`   👤 ${user.id}: ${user.email} (${user.name}) - ${clientCount} clients`);
    });

    console.log('\n✅ Database check completed');

  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

// Run check
checkDatabaseClients();
