// Test script for hybrid folder picker functionality
// This script tests both native and web-based folder pickers

console.log('🧪 Testing Hybrid Folder Picker Implementation...\n');

// Test 1: Check if both picker types are available
function testPickerAvailability() {
  console.log('1. 📋 Testing Picker Availability...');
  
  // Check Electron API
  if (window.electronAPI) {
    console.log('✅ Native Windows Folder Picker: Available');
    console.log('   - electronAPI.selectFolder() method exists');
  } else {
    console.log('❌ Native Windows Folder Picker: Not Available');
    console.log('   - Running in web mode or electronAPI not exposed');
  }
  
  // Check Web-based Picker
  const folderPickerComponent = document.querySelector('[data-testid="folder-picker"]');
  if (folderPickerComponent || window.location.pathname.includes('sync')) {
    console.log('✅ Web-based Folder Picker: Available');
    console.log('   - FolderPicker component should be accessible');
  } else {
    console.log('⚠️ Web-based Folder Picker: Component not found in DOM');
    console.log('   - May not be rendered yet or not on sync page');
  }
  
  console.log('');
}

// Test 2: Test Native Folder Picker (Electron only)
async function testNativeFolderPicker() {
  console.log('2. 🗂️ Testing Native Folder Picker...');
  
  if (!window.electronAPI) {
    console.log('⏭️ Skipping - Native picker only available in Electron app');
    console.log('');
    return;
  }
  
  try {
    console.log('📁 Opening native folder picker...');
    console.log('💡 Please select a folder in the dialog that opens');
    
    const result = await window.electronAPI.selectFolder();
    
    if (result.canceled) {
      console.log('❌ User canceled folder selection');
    } else if (result.filePaths && result.filePaths.length > 0) {
      console.log('✅ Folder selected successfully:');
      console.log(`   📂 Path: ${result.filePaths[0]}`);
      console.log(`   📊 Type: Native Windows Dialog`);
    } else {
      console.log('⚠️ No folder selected (unexpected result)');
    }
    
  } catch (error) {
    console.log('❌ Error testing native folder picker:', error.message);
  }
  
  console.log('');
}

// Test 3: Test Web-based Folder Picker API
async function testWebFolderPickerAPI() {
  console.log('3. 🌐 Testing Web-based Folder Picker API...');
  
  try {
    // Check if we're logged in
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    if (!token) {
      console.log('❌ No authentication token found');
      console.log('💡 Please log in first to test the web folder picker');
      console.log('');
      return;
    }
    
    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
    
    // Test getting home directory
    console.log('🏠 Testing home directory API...');
    const response = await fetch(`${apiUrl}/sync/directories`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Web folder picker API working:');
      console.log(`   📂 Current Path: ${data.currentPath || 'Home'}`);
      console.log(`   📁 Directories Found: ${data.directories.length}`);
      
      // Show first few directories
      if (data.directories.length > 0) {
        console.log('   📋 Sample directories:');
        data.directories.slice(0, 3).forEach(dir => {
          console.log(`      - ${dir.name} ${dir.isSpecial ? '(Special)' : ''}`);
        });
      }
    } else {
      console.log('❌ Web folder picker API failed:', response.status, response.statusText);
    }
    
  } catch (error) {
    console.log('❌ Error testing web folder picker API:', error.message);
  }
  
  console.log('');
}

// Test 4: Check UI Integration
function testUIIntegration() {
  console.log('4. 🎨 Testing UI Integration...');
  
  // Check if we're on the sync tasks page
  const isOnSyncPage = window.location.pathname.includes('sync') || 
                       document.querySelector('[data-testid="create-task-modal"]') ||
                       document.querySelector('button[title*="folder picker"]');
  
  if (isOnSyncPage) {
    console.log('✅ On sync-related page');
    
    // Check for folder picker buttons
    const nativeButtons = document.querySelectorAll('button[title*="Windows folder picker"]');
    const webButtons = document.querySelectorAll('button[title*="web folder picker"]');
    
    console.log(`   🗂️ Native picker buttons found: ${nativeButtons.length}`);
    console.log(`   🌐 Web picker buttons found: ${webButtons.length}`);
    
    if (window.electronAPI) {
      console.log('   💡 Expected: 2 native + 2 web buttons (source + destination)');
    } else {
      console.log('   💡 Expected: 2 web buttons only (source + destination)');
    }
    
  } else {
    console.log('⚠️ Not on sync page - navigate to sync tasks to see folder pickers');
    console.log('   💡 Go to: Sync Tasks > Create New Task');
  }
  
  console.log('');
}

// Test 5: Performance and UX Test
function testPerformanceAndUX() {
  console.log('5. ⚡ Testing Performance & UX...');
  
  console.log('📊 Folder Picker Comparison:');
  console.log('');
  console.log('🗂️ Native Windows Picker:');
  console.log('   ✅ Pros: Fast, familiar UI, system integration');
  console.log('   ❌ Cons: Desktop only, limited customization');
  console.log('');
  console.log('🌐 Web-based Picker:');
  console.log('   ✅ Pros: Cross-platform, customizable, consistent UI');
  console.log('   ❌ Cons: Slower, requires API calls, custom UI');
  console.log('');
  console.log('🎯 Hybrid Approach Benefits:');
  console.log('   ✅ Best of both worlds');
  console.log('   ✅ Fallback option always available');
  console.log('   ✅ User choice and flexibility');
  console.log('');
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Hybrid Folder Picker Tests...\n');
  
  testPickerAvailability();
  await testNativeFolderPicker();
  await testWebFolderPickerAPI();
  testUIIntegration();
  testPerformanceAndUX();
  
  console.log('🎉 Hybrid Folder Picker Tests Completed!');
  console.log('');
  console.log('📝 Summary:');
  console.log('   - Native picker: Windows dialog for desktop app');
  console.log('   - Web picker: Custom component for all platforms');
  console.log('   - Both options available when possible');
  console.log('   - Graceful fallback to web picker');
}

// Auto-run tests
runAllTests();

// Export for manual testing
window.testHybridFolderPicker = {
  runAllTests,
  testNativeFolderPicker,
  testWebFolderPickerAPI,
  testUIIntegration
};
