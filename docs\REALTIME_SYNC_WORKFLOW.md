# 🔄 SyncMasterPro Real-time Sync Workflow

## 📋 Tổng quan

Chức năng đồng bộ real-time trong SyncMasterPro hoạt động dựa trên **File Watcher + WebSocket** để cung cấp trải nghiệm đồng bộ tức thời và cập nhật UI real-time.

## 🎯 Workflow chính

### Phase 1: Khởi tạo Task
```
User Action → UI → SyncContext → API → Database → Response
```

1. **User tạo task** với sync type = "real-time"
2. **CreateTaskModal** gửi data đến SyncContext
3. **SyncContext** gọi API `POST /api/sync/tasks`
4. **Server** lưu task vào database
5. **Response** trả về task đã tạo

### Phase 2: Bắt đầu Sync
```
Start Button → SyncContext → API/Local → File Watcher → Ready
```

1. **User click Start** trên task card
2. **SyncContext.startSync()** đư<PERSON><PERSON> gọi
3. **Environment check**:
   - Desktop: Sử dụng Local SyncEngine
   - Web: Gọi Server API
4. **Initialize File Watcher** (Chokidar)
5. **Socket.IO emit** 'sync-started' event

### Phase 3: File Monitoring
```
File Change → Chokidar → Event Handler → Sync Queue → Debounced Processing
```

1. **Chokidar watches** source directory
2. **File system events** được detect:
   - `add` - File mới
   - `change` - File sửa đổi
   - `unlink` - File xóa
   - `addDir` - Thư mục mới
   - `unlinkDir` - Thư mục xóa
3. **handleFileChange()** xử lý event
4. **Add to sync queue** với timestamp
5. **Debounced processing** (1000ms delay)

### Phase 4: Sync Processing
```
Queue Processing → File Operations → Progress Events → UI Updates
```

1. **processSyncQueue()** xử lý queue
2. **Validate file changes** và apply filters
3. **Determine sync actions** based on sync type
4. **File operations**:
   - Copy files
   - Update timestamps
   - Handle conflicts
5. **Emit progress events** qua Socket.IO
6. **Update database** với sync history

### Phase 5: Real-time UI Updates
```
Socket Events → SocketContext → SyncContext → UI Components → User Feedback
```

1. **Socket.IO events** được receive:
   - `sync-started` - Bắt đầu sync
   - `sync-progress` - Tiến độ sync
   - `sync-completed` - Hoàn thành sync
   - `sync-error` - Lỗi sync
2. **SocketContext** handle events
3. **SyncContext** update state
4. **UI components** re-render
5. **User sees** live updates

## 🔧 Technical Implementation

### File Watcher Configuration
```javascript
const watcher = chokidar.watch(sourcePath, {
  ignored: /node_modules|\.git/,
  persistent: true,
  ignoreInitial: false,
  followSymlinks: false,
  depth: undefined
});
```

### Debouncing Mechanism
```javascript
debouncedSync = debounce((taskId) => {
  this.processSyncQueue(taskId);
}, 1000); // 1 second delay
```

### Socket.IO Events
```javascript
// Server emits
io.to(`user-${userId}`).emit('sync-progress', {
  taskId,
  filesProcessed,
  totalFiles,
  currentFile
});

// Client receives
socket.on('sync-progress', (data) => {
  setSyncProgress(prev => ({
    ...prev,
    [data.taskId]: data
  }));
});
```

## 🎮 Sync Types & Behaviors

### 1. Real-time Sync (⚡)
- **Trigger**: Immediate file changes
- **Behavior**: Instant sync with 1s debounce
- **Use case**: Critical files cần sync ngay

### 2. Bidirectional Sync (🔄)
- **Trigger**: Changes in both directions
- **Behavior**: Two-way monitoring
- **Use case**: Shared folders

### 3. Source-to-Destination (➡️)
- **Trigger**: Source changes only
- **Behavior**: One-way sync
- **Use case**: Backup scenarios

### 4. Incremental Sync (📈)
- **Trigger**: Changed files only
- **Behavior**: Skip unchanged files
- **Use case**: Large directories

## 📊 Performance Considerations

### Debouncing Benefits
- **Prevents spam**: Tránh sync quá nhiều lần
- **Batches changes**: Gom nhóm multiple changes
- **Reduces load**: Giảm tải server và I/O

### File Filtering
- **Ignore patterns**: `.git`, `node_modules`, `*.tmp`
- **Size limits**: Skip files quá lớn
- **Extension filters**: Chỉ sync certain file types

### Memory Management
- **Queue cleanup**: Clear processed items
- **Watcher disposal**: Properly close watchers
- **Event listener cleanup**: Remove unused listeners

## 🔍 Monitoring & Debugging

### Console Logs
```javascript
console.log(`🔄 Starting sync for task: ${task.name}`);
console.log(`📁 File ${changeType}: ${filePath}`);
console.log(`📊 Socket: Sync progress:`, data);
```

### Error Handling
```javascript
watcher.on('error', (error) => {
  console.error(`Watcher error for task ${task.id}:`, error);
  this.onSyncError(task.id, error);
});
```

### Performance Metrics
- **Files processed**: Số file đã sync
- **Transfer speed**: Tốc độ transfer
- **Error rate**: Tỷ lệ lỗi
- **Queue size**: Kích thước queue

## 🚨 Error Scenarios

### Common Issues
1. **File locked**: File đang được sử dụng
2. **Permission denied**: Không có quyền access
3. **Disk full**: Hết dung lượng disk
4. **Network timeout**: Mất kết nối
5. **Path not found**: Đường dẫn không tồn tại

### Error Recovery
1. **Retry mechanism**: Thử lại sau delay
2. **Skip problematic files**: Bỏ qua file lỗi
3. **User notification**: Thông báo lỗi
4. **Fallback sync**: Chuyển sang manual sync

## 🎯 Best Practices

### For Users
1. **Avoid large files** trong real-time sync
2. **Use filters** để exclude unnecessary files
3. **Monitor disk space** trước khi sync
4. **Close files** trước khi sync

### For Developers
1. **Proper cleanup** của watchers và listeners
2. **Error boundaries** trong React components
3. **Memory leak prevention** trong long-running processes
4. **Graceful degradation** khi offline

## 📈 Future Enhancements

1. **Adaptive debouncing**: Điều chỉnh delay based on activity
2. **Intelligent batching**: Smart grouping của file changes
3. **Conflict resolution**: Auto-resolve file conflicts
4. **Bandwidth throttling**: Limit transfer speed
5. **Priority queuing**: Ưu tiên certain file types
