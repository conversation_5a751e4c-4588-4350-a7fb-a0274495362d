const { Pool } = require('pg');

async function createTables() {
  console.log('🚀 Creating PostgreSQL tables for SyncMasterPro...');

  const pool = new Pool({
    user: 'pi',
    host: '*************',
    database: 'syncmasterpro',
    password: 'ubuntu',
    port: 5432,
  });

  try {
    // Test connection
    console.log('🔌 Testing connection...');
    await pool.query('SELECT NOW()');
    console.log('✅ Connected to PostgreSQL');

    // Create users table
    console.log('👥 Creating users table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        avatar TEXT,
        settings JSONB DEFAULT '{}',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Users table created');

    // Create sync_tasks table
    console.log('📋 Creating sync_tasks table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS sync_tasks (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        source_path TEXT NOT NULL,
        destination_path TEXT NOT NULL,
        sync_type VARCHAR(50) DEFAULT 'bidirectional',
        schedule TEXT,
        filters JSONB DEFAULT '[]',
        options JSONB DEFAULT '{}',
        status VARCHAR(50) DEFAULT 'idle',
        last_sync TIMESTAMP,
        files_count INTEGER DEFAULT 0,
        total_size BIGINT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Sync_tasks table created');

    // Create sync_history table
    console.log('📊 Creating sync_history table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS sync_history (
        id SERIAL PRIMARY KEY,
        task_id INTEGER NOT NULL REFERENCES sync_tasks(id) ON DELETE CASCADE,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        status VARCHAR(50) NOT NULL,
        started_at TIMESTAMP NOT NULL,
        completed_at TIMESTAMP,
        files_processed INTEGER DEFAULT 0,
        files_added INTEGER DEFAULT 0,
        files_updated INTEGER DEFAULT 0,
        files_deleted INTEGER DEFAULT 0,
        total_size BIGINT DEFAULT 0,
        error_message TEXT,
        details JSONB DEFAULT '{}'
      )
    `);
    console.log('✅ Sync_history table created');

    // Create file_changes table
    console.log('📁 Creating file_changes table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS file_changes (
        id SERIAL PRIMARY KEY,
        task_id INTEGER NOT NULL REFERENCES sync_tasks(id) ON DELETE CASCADE,
        file_path TEXT NOT NULL,
        change_type VARCHAR(50) NOT NULL,
        file_size BIGINT,
        checksum VARCHAR(255),
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ File_changes table created');

    // Create sessions table
    console.log('🔐 Creating sessions table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS sessions (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        token VARCHAR(255) UNIQUE NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Sessions table created');

    // Create indexes
    console.log('🔍 Creating indexes...');
    
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_sync_tasks_user_id ON sync_tasks (user_id)',
      'CREATE INDEX IF NOT EXISTS idx_sync_history_task_id ON sync_history (task_id)',
      'CREATE INDEX IF NOT EXISTS idx_sync_history_user_id ON sync_history (user_id)',
      'CREATE INDEX IF NOT EXISTS idx_file_changes_task_id ON file_changes (task_id)',
      'CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions (token)',
      'CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions (user_id)',
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users (email)',
      'CREATE INDEX IF NOT EXISTS idx_sync_tasks_status ON sync_tasks (status)',
      'CREATE INDEX IF NOT EXISTS idx_sync_history_status ON sync_history (status)'
    ];

    for (let i = 0; i < indexes.length; i++) {
      try {
        await pool.query(indexes[i]);
        console.log(`  ✅ Index ${i + 1}/${indexes.length} created`);
      } catch (error) {
        if (!error.message.includes('already exists')) {
          console.log(`  ⚠️ Index ${i + 1} error:`, error.message);
        }
      }
    }

    // Verify tables
    console.log('🔍 Verifying tables...');
    const result = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);

    console.log('📋 Created tables:');
    result.rows.forEach(row => {
      console.log(`  - ${row.table_name}`);
    });

    // Check table counts
    console.log('📊 Table row counts:');
    for (const table of ['users', 'sync_tasks', 'sync_history', 'file_changes', 'sessions']) {
      try {
        const countResult = await pool.query(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`  - ${table}: ${countResult.rows[0].count} rows`);
      } catch (error) {
        console.log(`  - ${table}: Error - ${error.message}`);
      }
    }

    console.log('🎉 PostgreSQL tables created successfully!');
    console.log('');
    console.log('📋 Next steps:');
    console.log('1. Run: node scripts/simple-sync.js (to sync data from SQLite)');
    console.log('2. Or use the web interface to create new data');

  } catch (error) {
    console.log('❌ Error creating tables:', error.message);
    console.log('');
    
    if (error.code === '3D000') {
      console.log('💡 Database does not exist. Create it first:');
      console.log('   sudo -u postgres psql');
      console.log('   CREATE DATABASE syncmasterpro;');
      console.log('   GRANT ALL PRIVILEGES ON DATABASE syncmasterpro TO pi;');
    } else if (error.code === '28P01') {
      console.log('💡 Authentication failed. Check username/password.');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('💡 Connection refused. Check if PostgreSQL is running.');
    }
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  createTables();
}

module.exports = { createTables };
