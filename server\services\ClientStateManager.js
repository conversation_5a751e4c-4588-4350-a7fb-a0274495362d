const EventEmitter = require('events');

class ClientStateManager extends EventEmitter {
  constructor() {
    super();
    this.clients = new Map(); // In-memory client state cache
    this.db = null;
    this.isInitialized = false;
    this.syncLock = new Set(); // Prevent concurrent updates
    
    // Client state validation rules
    this.validStates = ['online', 'offline', 'connecting', 'disconnecting'];
    this.stateTransitions = {
      'offline': ['connecting', 'online'],
      'connecting': ['online', 'offline'],
      'online': ['disconnecting', 'offline'],
      'disconnecting': ['offline', 'online']
    };
  }

  async initialize(database) {
    if (this.isInitialized) {
      console.log('⚠️ ClientStateManager already initialized');
      return;
    }

    this.db = database;
    
    try {
      // Load existing clients from database
      await this.loadClientsFromDatabase();
      
      // Set up periodic state validation
      this.startStateValidation();
      
      this.isInitialized = true;
      console.log('✅ ClientStateManager initialized');
      
      this.emit('initialized');
    } catch (error) {
      console.error('❌ ClientStateManager initialization failed:', error);
      throw error;
    }
  }

  async loadClientsFromDatabase() {
    try {
      const result = await this.db.query(`
        SELECT client_id, user_id, hostname, status, last_seen, metadata
        FROM desktop_clients
        ORDER BY last_seen DESC
      `);

      const clients = result.rows || result;
      
      // Load into memory cache
      this.clients.clear();
      clients.forEach(client => {
        this.clients.set(client.client_id, {
          ...client,
          lastUpdate: new Date(client.last_seen),
          inMemoryStatus: client.status,
          isValid: true
        });
      });

      console.log(`📊 Loaded ${clients.length} clients into state manager`);
    } catch (error) {
      console.error('❌ Failed to load clients from database:', error);
      throw error;
    }
  }

  async registerClient(clientData) {
    const { clientId, userId, hostname, platform, arch, version, metadata } = clientData;
    
    if (this.syncLock.has(clientId)) {
      console.log(`⏳ Client ${clientId} registration already in progress`);
      return false;
    }

    this.syncLock.add(clientId);

    try {
      console.log(`📝 Registering client: ${clientId} for user ${userId}`);

      // Check if client exists in memory
      const existingClient = this.clients.get(clientId);
      const previousStatus = existingClient?.status || 'offline';

      // Always set to online when registering
      const newClientData = {
        clientId,
        userId,
        hostname,
        platform,
        arch,
        version,
        status: 'online',
        lastSeen: new Date(),
        metadata: metadata || {},
        previousStatus,
        lastUpdate: new Date(),
        inMemoryStatus: 'online',
        isValid: true
      };

      // Update database with UPSERT logic
      const updateResult = await this.upsertClientInDatabase(newClientData);

      // Update memory cache
      this.clients.set(clientId, newClientData);

      console.log(`✅ Client registered: ${clientId} (${hostname})`);
      console.log(`   Status: ${previousStatus} → online`);
      console.log(`   Database rows affected: ${updateResult.affectedRows || updateResult.rowCount || 'unknown'}`);

      // Emit state change event
      this.emit('clientStateChanged', {
        clientId,
        userId,
        previousStatus,
        newStatus: 'online',
        action: 'register',
        timestamp: new Date()
      });

      return true;

    } catch (error) {
      console.error(`❌ Failed to register client ${clientId}:`, error);
      return false;
    } finally {
      this.syncLock.delete(clientId);
    }
  }

  async updateClientStatus(clientId, newStatus, metadata = {}) {
    if (!this.validStates.includes(newStatus)) {
      console.error(`❌ Invalid status: ${newStatus}`);
      return false;
    }

    if (this.syncLock.has(clientId)) {
      console.log(`⏳ Client ${clientId} update already in progress`);
      return false;
    }

    this.syncLock.add(clientId);

    try {
      const existingClient = this.clients.get(clientId);
      
      if (!existingClient) {
        console.log(`⚠️ Client ${clientId} not found in state manager`);
        this.syncLock.delete(clientId);
        return false;
      }

      const previousStatus = existingClient.status;

      // Validate state transition
      if (!this.isValidStateTransition(previousStatus, newStatus)) {
        console.log(`⚠️ Invalid state transition: ${previousStatus} → ${newStatus} for client ${clientId}`);
        this.syncLock.delete(clientId);
        return false;
      }

      // Update database
      const updateResult = await this.db.query(`
        UPDATE desktop_clients 
        SET status = ?, last_seen = CURRENT_TIMESTAMP, metadata = ?
        WHERE client_id = ?
      `, [newStatus, JSON.stringify({ ...existingClient.metadata, ...metadata }), clientId]);

      // Update memory cache
      this.clients.set(clientId, {
        ...existingClient,
        status: newStatus,
        lastSeen: new Date(),
        metadata: { ...existingClient.metadata, ...metadata },
        lastUpdate: new Date(),
        inMemoryStatus: newStatus
      });

      console.log(`🔄 Client status updated: ${clientId}`);
      console.log(`   Status: ${previousStatus} → ${newStatus}`);
      console.log(`   Database rows affected: ${updateResult.affectedRows || updateResult.rowCount || 'unknown'}`);

      // Emit state change event
      this.emit('clientStateChanged', {
        clientId,
        userId: existingClient.userId,
        previousStatus,
        newStatus,
        action: 'statusUpdate',
        timestamp: new Date(),
        metadata
      });

      return true;

    } catch (error) {
      console.error(`❌ Failed to update client status ${clientId}:`, error);
      return false;
    } finally {
      this.syncLock.delete(clientId);
    }
  }

  async upsertClientInDatabase(clientData) {
    const { clientId, userId, hostname, platform, arch, version, status, metadata } = clientData;

    // Check if client exists
    const existingResult = await this.db.query(`
      SELECT id FROM desktop_clients WHERE client_id = ?
    `, [clientId]);

    if (existingResult.rows?.length || existingResult.length) {
      // Update existing client
      return await this.db.query(`
        UPDATE desktop_clients SET
          user_id = ?, hostname = ?, platform = ?, arch = ?, version = ?,
          status = ?, last_seen = CURRENT_TIMESTAMP, metadata = ?
        WHERE client_id = ?
      `, [userId, hostname, platform, arch, version, status, JSON.stringify(metadata), clientId]);
    } else {
      // Insert new client
      return await this.db.query(`
        INSERT INTO desktop_clients (
          user_id, client_id, hostname, platform, arch, version,
          status, last_seen, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?)
      `, [userId, clientId, hostname, platform, arch, version, status, JSON.stringify(metadata)]);
    }
  }

  isValidStateTransition(fromStatus, toStatus) {
    if (!fromStatus || !toStatus) return true; // Allow initial state
    if (fromStatus === toStatus) return true; // Same state is valid
    
    const allowedTransitions = this.stateTransitions[fromStatus] || [];
    return allowedTransitions.includes(toStatus);
  }

  getClient(clientId) {
    return this.clients.get(clientId);
  }

  getAllClients() {
    return Array.from(this.clients.values());
  }

  getClientsByUser(userId) {
    return Array.from(this.clients.values()).filter(client => client.userId === userId);
  }

  getClientsByStatus(status) {
    return Array.from(this.clients.values()).filter(client => client.status === status);
  }

  async removeClient(clientId) {
    if (this.syncLock.has(clientId)) {
      console.log(`⏳ Client ${clientId} operation already in progress`);
      return false;
    }

    this.syncLock.add(clientId);

    try {
      const existingClient = this.clients.get(clientId);
      
      if (!existingClient) {
        console.log(`⚠️ Client ${clientId} not found`);
        this.syncLock.delete(clientId);
        return false;
      }

      // Remove from database
      await this.db.query(`DELETE FROM desktop_clients WHERE client_id = ?`, [clientId]);

      // Remove from memory
      this.clients.delete(clientId);

      console.log(`🗑️ Client removed: ${clientId}`);

      // Emit removal event
      this.emit('clientRemoved', {
        clientId,
        userId: existingClient.userId,
        timestamp: new Date()
      });

      return true;

    } catch (error) {
      console.error(`❌ Failed to remove client ${clientId}:`, error);
      return false;
    } finally {
      this.syncLock.delete(clientId);
    }
  }

  startStateValidation() {
    // Validate client states every 5 minutes
    setInterval(async () => {
      await this.validateClientStates();
    }, 5 * 60 * 1000);

    console.log('🔍 Client state validation started (every 5 minutes)');
  }

  async validateClientStates() {
    try {
      console.log('🔍 Validating client states...');

      const now = new Date();
      const staleThreshold = 10 * 60 * 1000; // 10 minutes

      for (const [clientId, client] of this.clients) {
        const timeSinceLastSeen = now - new Date(client.lastSeen);

        // Mark clients as offline if they haven't been seen for too long
        if (client.status === 'online' && timeSinceLastSeen > staleThreshold) {
          console.log(`⏰ Client ${clientId} appears stale, marking as offline`);
          await this.updateClientStatus(clientId, 'offline', {
            reason: 'stale_timeout',
            lastValidation: now
          });
        }
      }

      // Sync with database to catch any discrepancies
      await this.syncWithDatabase();

    } catch (error) {
      console.error('❌ Client state validation failed:', error);
    }
  }

  async syncWithDatabase() {
    try {
      const dbResult = await this.db.query(`
        SELECT client_id, status, last_seen FROM desktop_clients
      `);

      const dbClients = dbResult.rows || dbResult;

      for (const dbClient of dbClients) {
        const memoryClient = this.clients.get(dbClient.client_id);

        if (memoryClient && memoryClient.status !== dbClient.status) {
          console.log(`🔄 Syncing client ${dbClient.client_id}: memory(${memoryClient.status}) vs db(${dbClient.status})`);
          
          // Update memory to match database
          this.clients.set(dbClient.client_id, {
            ...memoryClient,
            status: dbClient.status,
            lastSeen: new Date(dbClient.last_seen),
            inMemoryStatus: dbClient.status
          });
        }
      }

    } catch (error) {
      console.error('❌ Database sync failed:', error);
    }
  }

  getStats() {
    const clients = this.getAllClients();
    const stats = {
      total: clients.length,
      online: clients.filter(c => c.status === 'online').length,
      offline: clients.filter(c => c.status === 'offline').length,
      connecting: clients.filter(c => c.status === 'connecting').length,
      disconnecting: clients.filter(c => c.status === 'disconnecting').length,
      lastUpdate: new Date()
    };

    return stats;
  }
}

module.exports = ClientStateManager;
