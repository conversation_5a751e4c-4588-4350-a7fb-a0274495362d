const express = require('express');
const router = express.Router();
const UserManagementService = require('../services/UserManagementService');
const AuditTrailService = require('../services/AuditTrailService');
const authenticateToken = require('../middleware/auth');

// Lazy initialization of services
let userService = null;
let auditService = null;

const getUserService = () => {
  if (!userService) {
    userService = new UserManagementService();
  }
  return userService;
};

const getAuditService = () => {
  if (!auditService) {
    auditService = new AuditTrailService();
  }
  return auditService;
};

// Middleware to check admin role
const requireAdmin = async (req, res, next) => {
  try {
    const user = await getUserService().getUserById(req.userId);
    if (user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }
    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to verify admin access'
    });
  }
};

// Get all users (admin only)
router.get('/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const {
      search,
      role,
      status,
      limit = 50,
      offset = 0,
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    const filters = {
      search,
      role,
      status,
      limit: parseInt(limit),
      offset: parseInt(offset),
      sortBy,
      sortOrder
    };

    const result = await getUserService().getUsers(filters);

    // Log admin action
    await getAuditService().logEvent({
      userId: req.userId,
      action: 'admin_users_viewed',
      resource: 'users',
      details: { filters },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.token
    });

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error('Failed to get users:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get users'
    });
  }
});

// Get user by ID (admin only)
router.get('/users/:userId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    
    const user = await getUserService().getUserById(parseInt(userId));

    // Log admin action
    await getAuditService().logEvent({
      userId: req.userId,
      action: 'admin_user_viewed',
      resource: 'user',
      resourceId: userId,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.token
    });

    res.json({
      success: true,
      user
    });
  } catch (error) {
    console.error('Failed to get user:', error);
    res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }
});

// Create new user (admin only)
router.post('/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const userData = req.body;
    
    const result = await getUserService().createUser(userData);

    // Log admin action
    await getAuditService().logEvent({
      userId: req.userId,
      action: 'admin_user_created',
      resource: 'user',
      resourceId: result.id.toString(),
      details: { 
        email: userData.email,
        role: userData.role,
        createdBy: req.userId 
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.token
    });

    res.status(201).json({
      success: true,
      message: result.message,
      user: {
        id: result.id,
        email: result.email,
        firstName: result.firstName,
        lastName: result.lastName,
        role: result.role,
        status: result.status
      }
    });
  } catch (error) {
    console.error('Failed to create user:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to create user'
    });
  }
});

// Update user (admin only)
router.put('/users/:userId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const updateData = req.body;
    
    const result = await userService.updateUser(parseInt(userId), updateData);

    // Log admin action
    await auditService.logEvent({
      userId: req.userId,
      action: 'admin_user_updated',
      resource: 'user',
      resourceId: userId,
      details: { 
        updatedFields: Object.keys(updateData),
        updatedBy: req.userId 
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.token
    });

    res.json({
      success: true,
      message: result.message
    });
  } catch (error) {
    console.error('Failed to update user:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to update user'
    });
  }
});

// Delete user (admin only)
router.delete('/users/:userId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    
    // Prevent admin from deleting themselves
    if (parseInt(userId) === req.userId) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete your own account'
      });
    }
    
    const result = await userService.deleteUser(parseInt(userId));

    // Log admin action
    await auditService.logEvent({
      userId: req.userId,
      action: 'admin_user_deleted',
      resource: 'user',
      resourceId: userId,
      details: { deletedBy: req.userId },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.token
    });

    res.json({
      success: true,
      message: result.message
    });
  } catch (error) {
    console.error('Failed to delete user:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to delete user'
    });
  }
});

// Reset user password (admin only)
router.post('/users/:userId/reset-password', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { newPassword } = req.body;
    
    if (!newPassword) {
      return res.status(400).json({
        success: false,
        error: 'New password is required'
      });
    }
    
    const result = await userService.resetPassword(parseInt(userId), newPassword);

    // Log admin action
    await auditService.logEvent({
      userId: req.userId,
      action: 'admin_password_reset',
      resource: 'user',
      resourceId: userId,
      details: { resetBy: req.userId },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.token
    });

    res.json({
      success: true,
      message: result.message
    });
  } catch (error) {
    console.error('Failed to reset password:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to reset password'
    });
  }
});

// Get user sessions (admin only)
router.get('/users/:userId/sessions', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    
    const sessions = await userService.getUserSessions(parseInt(userId));

    res.json({
      success: true,
      sessions,
      count: sessions.length
    });
  } catch (error) {
    console.error('Failed to get user sessions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user sessions'
    });
  }
});

// Revoke user session (admin only)
router.delete('/users/:userId/sessions/:sessionToken', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId, sessionToken } = req.params;
    
    const result = await userService.revokeSession(parseInt(userId), sessionToken);

    // Log admin action
    await auditService.logEvent({
      userId: req.userId,
      action: 'admin_session_revoked',
      resource: 'session',
      resourceId: sessionToken,
      details: { 
        targetUserId: userId,
        revokedBy: req.userId 
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.token
    });

    res.json({
      success: true,
      message: result.message
    });
  } catch (error) {
    console.error('Failed to revoke session:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to revoke session'
    });
  }
});

// Get user statistics (admin only)
router.get('/statistics/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const statistics = await userService.getUserStatistics();

    res.json({
      success: true,
      statistics
    });
  } catch (error) {
    console.error('Failed to get user statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user statistics'
    });
  }
});

// Get admin dashboard
router.get('/dashboard', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const userStats = await userService.getUserStatistics();
    const auditStats = await auditService.getAuditStatistics('7d');
    const alerts = await auditService.getSecurityAlerts({ status: 'active', limit: 5 });

    const dashboard = {
      users: {
        total: userStats.totalUsers,
        active: userStats.activeUsers,
        newThisMonth: userStats.newLastMonth,
        activeLastWeek: userStats.activeLastWeek
      },
      security: {
        activeAlerts: alerts.length,
        auditEvents: auditStats.actionStatistics.reduce((sum, stat) => sum + stat.count, 0),
        criticalEvents: auditStats.severityStatistics.find(s => s.severity === 'critical')?.count || 0
      },
      activity: {
        topActions: auditStats.actionStatistics.slice(0, 5),
        mostActiveUsers: auditStats.userActivity.slice(0, 5),
        dailyActivity: auditStats.dailyActivity.slice(0, 7)
      },
      alerts: alerts.map(alert => ({
        id: alert.id,
        type: alert.type,
        severity: alert.severity,
        message: alert.message,
        createdAt: alert.createdAt
      }))
    };

    res.json({
      success: true,
      dashboard
    });
  } catch (error) {
    console.error('Failed to get admin dashboard:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get admin dashboard'
    });
  }
});

// System health check (admin only)
router.get('/system/health', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const health = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      services: {
        database: 'healthy',
        authentication: 'healthy',
        audit: 'healthy',
        sync: 'healthy'
      },
      metrics: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      }
    };

    // Test database connection
    try {
      await userService.getUserStatistics();
      health.services.database = 'healthy';
    } catch (error) {
      health.services.database = 'unhealthy';
      health.status = 'degraded';
    }

    res.json({
      success: true,
      health
    });
  } catch (error) {
    console.error('Failed to get system health:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get system health'
    });
  }
});

module.exports = router;
