// Quick test to verify real-time sync is working
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const baseURL = 'http://localhost:5002/api';

async function quickTest() {
  console.log('🚀 Quick Real-time Test\n');
  
  try {
    // Login
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    const headers = { 'Authorization': `Bearer ${token}` };
    
    // Get real-time tasks
    const tasksResponse = await axios.get(`${baseURL}/sync/tasks`, { headers });
    const tasks = tasksResponse.data.tasks || [];
    
    const realtimeTasks = tasks.filter(task => {
      const options = task.options || {};
      return options.enableRealtime;
    });
    
    if (realtimeTasks.length === 0) {
      console.log('❌ No real-time tasks found');
      return;
    }
    
    const task = realtimeTasks[0];
    console.log(`🎯 Testing task: ${task.name} (ID: ${task.id})`);
    
    // Check history before
    const historyBefore = await axios.get(`${baseURL}/sync/history?taskId=${task.id}&limit=3`, { headers });
    const beforeCount = historyBefore.data.history?.length || 0;
    console.log(`📊 History before: ${beforeCount} entries`);
    
    // Start real-time sync
    console.log('\n⚡ Starting real-time sync...');
    await axios.post(`${baseURL}/sync/tasks/${task.id}/realtime/start`, {}, { headers });
    
    // Create test file
    console.log('📄 Creating test file...');
    const testFileName = `quick-test-${Date.now()}.txt`;
    const testFilePath = path.join(task.sourcePath, testFileName);
    
    fs.writeFileSync(testFilePath, `Quick test at ${new Date().toISOString()}`);
    console.log(`✅ Created: ${testFileName}`);
    
    // Wait for sync
    console.log('⏳ Waiting 8 seconds...');
    await new Promise(resolve => setTimeout(resolve, 8000));
    
    // Check results
    const destFilePath = path.join(task.destinationPath, testFileName);
    const fileSynced = fs.existsSync(destFilePath);
    console.log(`📊 File synced: ${fileSynced ? '✅ YES' : '❌ NO'}`);
    
    // Check history after
    const historyAfter = await axios.get(`${baseURL}/sync/history?taskId=${task.id}&limit=3`, { headers });
    const afterCount = historyAfter.data.history?.length || 0;
    const newEntries = afterCount - beforeCount;
    
    console.log(`📊 History after: ${afterCount} entries`);
    console.log(`📈 New entries: ${newEntries}`);
    
    if (newEntries > 0) {
      console.log('\n🎉 SUCCESS: Real-time sync is working!');
      const latestEntry = historyAfter.data.history[0];
      console.log(`📄 Latest: ${latestEntry.status} - ${latestEntry.filesProcessed} files`);
      
      if (latestEntry.details && typeof latestEntry.details === 'object') {
        console.log(`🏷️ Type: ${latestEntry.details.type || 'unknown'}`);
      }
    } else {
      console.log('\n❌ ISSUE: No history entries created');
    }
    
    // Stop real-time sync
    await axios.post(`${baseURL}/sync/tasks/${task.id}/realtime/stop`, {}, { headers });
    console.log('\n🛑 Real-time sync stopped');
    
    // Summary
    console.log('\n📊 SUMMARY:');
    console.log(`   File synced: ${fileSynced ? 'YES' : 'NO'}`);
    console.log(`   History created: ${newEntries > 0 ? 'YES' : 'NO'}`);
    console.log(`   Status: ${fileSynced && newEntries > 0 ? '✅ SUCCESS' : '❌ FAILED'}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

quickTest();
