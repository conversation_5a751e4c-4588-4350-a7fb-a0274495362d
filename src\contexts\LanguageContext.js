import React, { createContext, useContext, useState, useEffect } from 'react';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Translation dictionaries
const translations = {
  en: {
    // Common
    loading: 'Loading...',
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    close: 'Close',
    confirm: 'Confirm',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Information',
    
    // Navigation
    dashboard: 'Dashboard',
    syncTasks: 'Sync Tasks',
    history: 'History',
    settings: 'Settings',
    
    // Settings
    settingsTitle: 'Settings',
    settingsDescription: 'Manage your application preferences and configuration',
    general: 'General',
    syncSettings: 'Sync Settings',
    notifications: 'Notifications',
    security: 'Security',
    about: 'About',
    appearance: 'Appearance',
    language: 'Language',
    
    // General Settings
    generalSettingsTitle: 'General Settings',
    generalSettingsDescription: 'Update your personal information and preferences',
    fullName: 'Full Name',
    emailAddress: 'Email Address',
    emailCannotBeChanged: 'Email cannot be changed',
    saveChanges: 'Save Changes',
    saving: 'Saving...',
    profileUpdatedSuccessfully: 'Profile updated successfully',
    
    // Appearance Settings
    appearanceSettingsTitle: 'Appearance Settings',
    appearanceSettingsDescription: 'Customize the look and feel of the application',
    themeMode: 'Theme Mode',
    lightTheme: 'Light',
    darkTheme: 'Dark',
    systemTheme: 'System',
    themeDescription: 'Choose your preferred theme or follow system settings',
    languageSelection: 'Language',
    languageDescription: 'Select your preferred language',
    
    // Sync Settings
    syncSettingsTitle: 'Sync Settings',
    syncSettingsDescription: 'Configure synchronization behavior and preferences',
    realTimeSync: 'Real-time Sync',
    realTimeSyncDescription: 'Automatically sync files when changes are detected',
    conflictResolution: 'Conflict Resolution',
    conflictResolutionDescription: 'Automatically resolve file conflicts',
    askMe: 'Ask me',
    keepNewer: 'Keep newer',
    keepLarger: 'Keep larger',
    bandwidthLimit: 'Bandwidth Limit',
    bandwidthLimitDescription: 'Limit sync bandwidth usage',
    maxBandwidth: 'Max Bandwidth (KB/s)',
    maxBandwidthDescription: 'Maximum bandwidth for sync operations',
    
    // Notification Settings
    notificationSettingsTitle: 'Notification Settings',
    notificationSettingsDescription: 'Choose what notifications you want to receive',
    syncCompletion: 'Sync Completion',
    syncCompletionDescription: 'Notify when sync tasks complete',
    syncErrors: 'Sync Errors',
    syncErrorsDescription: 'Notify when sync errors occur',
    systemNotifications: 'System Notifications',
    systemNotificationsDescription: 'Show desktop notifications',
    
    // Security Settings
    securitySettingsTitle: 'Security Settings',
    securitySettingsDescription: 'Manage your account security and privacy',
    changePassword: 'Change Password',
    changePasswordDescription: 'Update your account password',
    twoFactorAuth: 'Two-Factor Authentication',
    twoFactorAuthDescription: 'Add an extra layer of security (Coming Soon)',
    activeSessions: 'Active Sessions',
    activeSessionsDescription: 'Manage your active login sessions (Coming Soon)',
    
    // About Settings
    aboutTitle: 'About SyncMasterPro',
    aboutDescription: 'Application information and support',
    version: 'Version',
    environment: 'Environment',
    platform: 'Platform',
    apiServer: 'API Server',
    buildDate: 'Build Date',
    support: 'Support',
    license: 'License',
    
    // Messages
    settingsSavedSuccessfully: 'Settings saved successfully',
    failedToSaveSettings: 'Failed to save settings',
    notificationSettingsSavedSuccessfully: 'Notification settings saved successfully',
    failedToSaveNotificationSettings: 'Failed to save notification settings',
    passwordChangedSuccessfully: 'Password changed successfully',
    failedToChangePassword: 'Failed to change password',
    newPasswordsDoNotMatch: 'New passwords do not match',
    passwordMustBeAtLeast6Characters: 'Password must be at least 6 characters',

    // Dashboard
    welcomeBack: 'Welcome back',
    totalSyncTasks: 'Total Sync Tasks',
    activeSyncs: 'Active Syncs',
    completedToday: 'Completed Today',
    recentActivity: 'Recent Activity',
    quickActions: 'Quick Actions',
    createNewTask: 'Create New Task',
    viewAllTasks: 'View All Tasks',
    syncHistory: 'Sync History',
    systemStatus: 'System Status',
    online: 'Online',
    offline: 'Offline',

    // Sync Tasks
    syncTasksTitle: 'Sync Tasks',
    syncTasksDescription: 'Manage your file synchronization tasks',
    createTask: 'Create Task',
    taskName: 'Task Name',
    sourcePath: 'Source Path',
    destinationPath: 'Destination Path',
    syncType: 'Sync Type',
    bidirectional: 'Bidirectional',
    oneWay: 'One Way',
    status: 'Status',
    lastSync: 'Last Sync',
    actions: 'Actions',
    start: 'Start',
    stop: 'Stop',
    pause: 'Pause',
    resume: 'Resume',

    // History
    historyTitle: 'Sync History',
    historyDescription: 'View your synchronization history and logs',
    date: 'Date',
    task: 'Task',
    result: 'Result',
    duration: 'Duration',
    filesProcessed: 'Files Processed',
    viewDetails: 'View Details',
    clearHistory: 'Clear History',
    exportHistory: 'Export History',

    // Auth
    signIn: 'Sign In',
    signUp: 'Sign Up',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    rememberMe: 'Remember me',
    forgotPassword: 'Forgot password?',
    forgotPasswordDescription: 'Enter your email address and we will send you a link to reset your password.',
    resetPassword: 'Reset Password',
    resetPasswordDescription: 'Enter your new password below.',
    newPassword: 'New Password',
    sendResetLink: 'Send Reset Link',
    resetLinkSent: 'Reset link sent! Check your email.',
    backToLogin: 'Back to Login',
    back: 'Back',
    sending: 'Sending...',
    resetting: 'Resetting...',
    emailAddress: 'Email Address',
    forgotPasswordError: 'Failed to send reset link',
    resetPasswordError: 'Failed to reset password',
    invalidResetToken: 'Invalid or expired reset token',
    networkError: 'Network error. Please try again.',
    passwordResetSuccess: 'Password reset successfully! You can now login with your new password.',
    loading: 'Loading...',
    dontHaveAccount: "Don't have an account?",
    alreadyHaveAccount: 'Already have an account?',
    signInToAccount: 'Sign in to your account',
    createNewAccount: 'Create a new account',
    signOut: 'Sign out',

    // Common UI
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    refresh: 'Refresh',
    export: 'Export',
    import: 'Import',
    upload: 'Upload',
    download: 'Download',
    copy: 'Copy',
    paste: 'Paste',
    cut: 'Cut',
    selectAll: 'Select All',
    deselectAll: 'Deselect All',

    // File operations
    selectFolder: 'Select Folder',
    selectFile: 'Select File',
    browse: 'Browse',
    fileName: 'File Name',
    fileSize: 'File Size',
    fileType: 'File Type',
    lastModified: 'Last Modified',
    selectCurrentFolder: 'Select Current Folder',
    nativePicker: 'Native',
    webPicker: 'Browse',
    enableRealtime: 'Enable Real-time Sync',
    enableRealtimeDescription: 'Monitor and sync files immediately when changes are detected',

    // Status messages
    syncing: 'Syncing...',
    monitoring: 'Monitoring',
    completed: 'Completed',
    failed: 'Failed',
    paused: 'Paused',
    stopped: 'Stopped',
    pending: 'Pending',

    // Time
    today: 'Today',
    yesterday: 'Yesterday',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    never: 'Never',

    // Units
    bytes: 'bytes',
    kb: 'KB',
    mb: 'MB',
    gb: 'GB',
    seconds: 'seconds',
    minutes: 'minutes',
    hours: 'hours',
    days: 'days',

    // Additional missing keys
    appDescription: 'Professional File & Folder Synchronization',
    welcomeBack: 'Welcome Back',
    createAccount: 'Create Account',
    signInDescription: 'Sign in to your account to continue',
    signUpDescription: 'Sign up to get started with SyncMasterPro',
    enterFullName: 'Enter your full name',
    enterEmail: 'Enter your email',
    enterPassword: 'Enter your password',
    confirmPasswordPlaceholder: 'Confirm your password',
    signingIn: 'Signing in...',
    creatingAccount: 'Creating account...',
    noAccount: "Don't have an account?",
    haveAccount: 'Already have an account?',
    offlineMode: 'Offline Mode',
    offlineModeDescription: 'You can use SyncMasterPro without an account for local synchronization only',
    passwordsDoNotMatch: 'Passwords do not match',
    passwordTooShort: 'Password must be at least 6 characters long',

    // History Detail Modal
    syncHistoryDetails: 'Sync History Details',
    basicInformation: 'Basic Information',
    fileStatistics: 'File Statistics',
    errorInformation: 'Error Information',
    additionalDetails: 'Additional Details',
    syncError: 'Sync Error',

    // More missing keys
    createNewTask: 'Create New Task',
    totalSyncs: 'Total Syncs',
    successful: 'Successful',
    clearAllHistory: 'Clear All History',
    clearFilters: 'Clear Filters',
    syncHistoryDescription: 'View detailed history of all synchronization activities',
    exportHistory: 'Export History',
    selectCurrentFolder: 'Select Current Folder',
    view: 'View',

    // Task form
    taskName: 'Task Name',
    browse: 'Browse',
    update: 'Update',

    // Quick Actions
    createNewTaskDescription: 'Create a new synchronization task',
    startAllSyncs: 'Start All Syncs',
    startAllSyncsDescription: 'Start all configured sync tasks',
    stopAllSyncs: 'Stop All Syncs',
    stopAllSyncsDescription: 'Stop all running sync tasks',
    viewAllTasksDescription: 'Check synchronization history',

    // Security Settings
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmNewPassword: 'Confirm New Password',
    changing: 'Changing...',

    // Header
    notifications: 'Notifications',
    profile: 'Profile',

    // Startup Settings
    startup: 'Startup',
    startupSettings: 'Startup Settings',
    startupSettingsDescription: 'Configure application startup behavior and system tray options',
    startupWithWindows: 'Start with Windows',
    startupWithWindowsDescription: 'Automatically start SyncMasterPro when Windows starts',
    startMinimized: 'Start Minimized',
    startMinimizedDescription: 'Start the application minimized to system tray',
    minimizeToTray: 'Minimize to Tray',
    minimizeToTrayDescription: 'Hide window to system tray when minimized',
    closeToTray: 'Close to Tray',
    closeToTrayDescription: 'Hide window to system tray when closed instead of exiting',
    testHideToTray: 'Test Hide to Tray',
    testHideToTrayDescription: 'Test the hide to tray functionality',
    hideToTray: 'Hide to Tray',
    electronRequired: 'Desktop App Required',
    electronRequiredDescription: 'These features are only available in the desktop version of SyncMasterPro',
    electronOnlyFeature: 'This feature is only available in the desktop app',
    settingUpdated: 'Setting updated successfully',
    errorUpdatingSetting: 'Failed to update setting',
    errorLoadingSettings: 'Failed to load settings',
    hiddenToTray: 'Application hidden to system tray',
    errorHidingToTray: 'Failed to hide to tray',
    updating: 'Updating...',

    // Settings Redesign
    unsavedChanges: 'Unsaved Changes',
    discard: 'Discard',
    resetToDefaults: 'Reset to Defaults',
    confirmReset: 'Confirm Reset',
    confirmResetDescription: 'This will reset all settings to their default values. This action cannot be undone.',
    settingsSaved: 'Settings saved successfully',
    errorSavingSettings: 'Failed to save settings',
    saveChanges: 'Save Changes',
    cancel: 'Cancel',
    generalSettingsDesc: 'User profile and basic preferences',
    appearanceSettingsDesc: 'Theme, language, and startup behavior',
    syncSettingsDesc: 'Synchronization and performance settings',
    securitySettingsDesc: 'Security, privacy, and session management',

    // Missing translations
    generalSettings: 'General Settings',
    appearanceSettings: 'Appearance Settings',
    generalSettingsDescription: 'Manage your profile information and basic application settings',
    appearanceSettingsDescription: 'Customize the look, feel, and startup behavior of the application',

    // General Settings
    profileInformation: 'Profile Information',
    updateProfile: 'Update Profile',
    languageSettings: 'Language Settings',
    interfaceLanguage: 'Interface Language',
    applicationSettings: 'Application Settings',
    showNotificationsDescription: 'Show desktop notifications for sync events',
    soundEnabledDescription: 'Play sounds for notifications and alerts',
    profileUpdatedSuccessfully: 'Profile updated successfully',
    failedToUpdateProfile: 'Failed to update profile',
    languageChanged: 'Language changed successfully',
    failedToChangeLanguage: 'Failed to change language',
    emailCannotBeChanged: 'Email address cannot be changed',
    fullName: 'Full Name',
    emailAddress: 'Email Address',
    showNotifications: 'Show Notifications',
    soundEnabled: 'Sound Enabled',

    // Appearance Settings
    themeSettings: 'Theme Settings',
    colorTheme: 'Color Theme',
    themeDescription: 'Choose your preferred color theme for the application',
    lightTheme: 'Light',
    darkTheme: 'Dark',
    systemTheme: 'System',
    currentlyUsing: 'Currently using',
    startupBehavior: 'Startup Behavior',
    desktopFeaturesAvailable: 'Desktop Features Available',
    desktopFeaturesDescription: 'Additional startup and system tray features are available in the desktop version.',
    themeChanged: 'Theme changed successfully',
    failedToChangeTheme: 'Failed to change theme',

    // Sync Settings
    automaticSync: 'Automatic Synchronization',
    enableAutoSync: 'Enable Auto Sync',
    enableAutoSyncDescription: 'Automatically sync files at regular intervals',
    syncInterval: 'Sync Interval',
    syncIntervalDescription: 'How often to check for changes and sync files',
    errorHandling: 'Error Handling',
    maxRetries: 'Maximum Retries',
    maxRetriesDescription: 'Number of times to retry failed sync operations',
    performanceSettings: 'Performance Settings',
    performanceOptimization: 'Performance Optimization',
    performanceOptimizationDescription: 'Current sync configuration and performance metrics',
    currentSyncInterval: 'Current Sync Interval',
    maxRetryAttempts: 'Max Retry Attempts',
    autoSyncStatus: 'Auto Sync Status',
    advancedSettings: 'Advanced Settings',
    advancedSettingsWarning: 'Advanced Settings',
    advancedSettingsWarningDescription: 'These settings are for advanced users. Changing them may affect sync performance.',
    oneMinute: '1 Minute',
    fiveMinutes: '5 Minutes',
    tenMinutes: '10 Minutes',
    thirtyMinutes: '30 Minutes',
    oneHour: '1 Hour',
    enabled: 'Enabled',
    disabled: 'Disabled',
    custom: 'Custom',

    // Security Settings
    sessionManagement: 'Session Management',
    sessionTimeout: 'Session Timeout',
    sessionTimeoutDescription: 'Automatically log out after period of inactivity',
    passwordAuthentication: 'Password & Authentication',
    requirePasswordChange: 'Require Password Change',
    requirePasswordChangeDescription: 'Require users to change password periodically',
    privacySettings: 'Privacy Settings',
    dataProtection: 'Data Protection',
    dataProtectionDescription: 'Your data is protected with industry-standard encryption',
    encryptionStatus: 'Encryption Status',
    secureTransmission: 'Secure Transmission',
    currentSessionTimeout: 'Current Session Timeout',
    securityRecommendations: 'Security Recommendations',
    securityTips: 'Security Tips',
    useStrongPassword: 'Use a strong, unique password',
    enableTwoFactor: 'Enable two-factor authentication when available',
    regularlyUpdatePassword: 'Regularly update your password',
    logoutSharedDevices: 'Always log out from shared devices',
    auditMonitoring: 'Audit & Monitoring',
    activityMonitoring: 'Activity Monitoring',
    activityMonitoringDescription: 'Track and monitor security-related activities',
    loginTracking: 'Login Tracking',
    syncActivityLog: 'Sync Activity Log',
    errorReporting: 'Error Reporting',
    active: 'Active',
    fifteenMinutes: '15 Minutes',
    twoHours: '2 Hours',
    fourHours: '4 Hours',
    eightHours: '8 Hours',
    securitySettings: 'Security Settings',

    // Notifications
    syncCompleted: 'Sync Completed',
    syncStarted: 'Sync Started',
    syncWarning: 'Sync Warning',
    syncError: 'Sync Error',
    syncInProgress: 'Sync In Progress',
    tasksRunning: 'tasks running',
    justNow: 'Just now',
    minutesAgo: 'm ago',
    hoursAgo: 'h ago',
    daysAgo: 'd ago',
    markAllRead: 'Mark all read',
    clearAll: 'Clear all',
    noNotifications: 'No notifications',

    // Profile
    changePhoto: 'Change Photo',
    fullName: 'Full Name',
    phone: 'Phone',
    location: 'Location',
    bio: 'Bio',
    notSet: 'Not set',
    enterPhone: 'Enter your phone number',
    enterLocation: 'Enter your location',
    enterBio: 'Tell us about yourself',
    saving: 'Saving...',
    profileUpdated: 'Profile updated successfully',
    profileUpdateError: 'Failed to update profile',

    // SyncTasks - Missing translations
    syncTasksTitle: 'Sync Tasks',
    syncTasksDescription: 'Manage and monitor your file synchronization tasks',
    realTimeConnected: 'Real-time connected',
    offlineMode: 'Offline mode',
    allSyncTasks: 'All Sync Tasks',
    activeTasks: 'Active Tasks',
    completedTasks: 'Completed Tasks',
    errorTasks: 'Error Tasks',
    noSyncTasks: 'No sync tasks',
    createFirstTask: 'Create your first sync task to get started',
    clearFilters: 'Clear Filters',
    noTasksFound: 'No tasks found matching your criteria',
    createNewTask: 'Create New Task',
    editTask: 'Edit Task',
    taskName: 'Task Name',
    enterTaskName: 'Enter task name',
    sourcePath: 'Source Path',
    destinationPath: 'Destination Path',
    selectSourceFolder: 'Select source folder',
    selectDestinationFolder: 'Select destination folder',
    syncType: 'Sync Type',
    bidirectional: 'Bidirectional',
    sourceToDestination: 'Source to Destination',
    destinationToSource: 'Destination to Source',
    mirror: 'Mirror',
    incremental: 'Incremental',
    bidirectionalDescription: 'Keep both folders in sync',
    sourceToDestinationDescription: 'One-way sync (backup)',
    destinationToSourceDescription: 'One-way sync (restore)',
    mirrorDescription: 'Exact copy (deletes extra files)',
    incrementalDescription: 'Only sync changed files',
    todayOnly: 'Today Only',
    todayOnlyDescription: 'Sync only files created today',
    fileFilters: 'File Filters',
    optional: 'Optional',
    fileFiltersPlaceholder: 'e.g., *.txt, *.jpg, !temp/*',
    fileFiltersHelp: 'Use patterns to include/exclude files. Use ! to exclude.',
    advancedOptions: 'Advanced Options',
    deleteExtraFiles: 'Delete extra files in destination',
    preserveTimestamps: 'Preserve file timestamps',
    taskNameRequired: 'Task name is required',
    sourcePathRequired: 'Source path is required',
    destinationPathRequired: 'Destination path is required',
    pathsMustBeDifferent: 'Source and destination paths must be different',
    failedToCreateTask: 'Failed to create task',
    createTask: 'Create Task',
    updateTask: 'Update Task',
    editTaskTitle: 'Edit Task',
    deleteTask: 'Delete Task',
    start: 'Start',
    stop: 'Stop',
    progress: 'Progress',
    processing: 'Processing',
    source: 'Source',
    destination: 'Destination',
    lastSync: 'Last Sync',
    files: 'files',
    idle: 'Idle',
    unknown: 'Unknown',
    never: 'Never',
    justNow: 'Just now',
    minutesAgo: 'minutes ago',
    hoursAgo: 'hours ago',
    daysAgo: 'days ago',
    totalSyncTasks: 'Total Sync Tasks',
    activeSyncs: 'Active Syncs',
    syncing: 'Syncing',
    paused: 'Paused',
    failed: 'Failed'
  },
  
  vi: {
    // Common
    loading: 'Đang tải...',
    save: 'Lưu',
    cancel: 'Hủy',
    delete: 'Xóa',
    edit: 'Chỉnh sửa',
    close: 'Đóng',
    confirm: 'Xác nhận',
    success: 'Thành công',
    error: 'Lỗi',
    warning: 'Cảnh báo',
    info: 'Thông tin',
    
    // Navigation
    dashboard: 'Bảng điều khiển',
    syncTasks: 'Tác vụ đồng bộ',
    history: 'Lịch sử',
    settings: 'Cài đặt',
    
    // Settings
    settingsTitle: 'Cài đặt',
    settingsDescription: 'Quản lý tùy chọn và cấu hình ứng dụng của bạn',
    general: 'Chung',
    syncSettings: 'Cài đặt đồng bộ',
    notifications: 'Thông báo',
    security: 'Bảo mật',
    about: 'Giới thiệu',
    appearance: 'Giao diện',
    language: 'Ngôn ngữ',
    
    // General Settings
    generalSettingsTitle: 'Cài đặt chung',
    generalSettingsDescription: 'Cập nhật thông tin cá nhân và tùy chọn của bạn',
    fullName: 'Họ và tên',
    emailAddress: 'Địa chỉ email',
    emailCannotBeChanged: 'Không thể thay đổi email',
    saveChanges: 'Lưu thay đổi',
    saving: 'Đang lưu...',
    profileUpdatedSuccessfully: 'Cập nhật hồ sơ thành công',
    
    // Appearance Settings
    appearanceSettingsTitle: 'Cài đặt giao diện',
    appearanceSettingsDescription: 'Tùy chỉnh giao diện của ứng dụng',
    themeMode: 'Chế độ giao diện',
    lightTheme: 'Sáng',
    darkTheme: 'Tối',
    systemTheme: 'Theo hệ thống',
    themeDescription: 'Chọn giao diện ưa thích hoặc theo cài đặt hệ thống',
    languageSelection: 'Ngôn ngữ',
    languageDescription: 'Chọn ngôn ngữ ưa thích của bạn',
    
    // Sync Settings
    syncSettingsTitle: 'Cài đặt đồng bộ',
    syncSettingsDescription: 'Cấu hình hành vi và tùy chọn đồng bộ hóa',
    realTimeSync: 'Đồng bộ thời gian thực',
    realTimeSyncDescription: 'Tự động đồng bộ tệp khi phát hiện thay đổi',
    conflictResolution: 'Giải quyết xung đột',
    conflictResolutionDescription: 'Tự động giải quyết xung đột tệp',
    askMe: 'Hỏi tôi',
    keepNewer: 'Giữ mới hơn',
    keepLarger: 'Giữ lớn hơn',
    bandwidthLimit: 'Giới hạn băng thông',
    bandwidthLimitDescription: 'Giới hạn sử dụng băng thông đồng bộ',
    maxBandwidth: 'Băng thông tối đa (KB/s)',
    maxBandwidthDescription: 'Băng thông tối đa cho các hoạt động đồng bộ',
    
    // Notification Settings
    notificationSettingsTitle: 'Cài đặt thông báo',
    notificationSettingsDescription: 'Chọn thông báo bạn muốn nhận',
    syncCompletion: 'Hoàn thành đồng bộ',
    syncCompletionDescription: 'Thông báo khi tác vụ đồng bộ hoàn thành',
    syncErrors: 'Lỗi đồng bộ',
    syncErrorsDescription: 'Thông báo khi xảy ra lỗi đồng bộ',
    systemNotifications: 'Thông báo hệ thống',
    systemNotificationsDescription: 'Hiển thị thông báo trên desktop',
    
    // Security Settings
    securitySettingsTitle: 'Cài đặt bảo mật',
    securitySettingsDescription: 'Quản lý bảo mật và quyền riêng tư tài khoản',
    changePassword: 'Đổi mật khẩu',
    changePasswordDescription: 'Cập nhật mật khẩu tài khoản của bạn',
    twoFactorAuth: 'Xác thực hai yếu tố',
    twoFactorAuthDescription: 'Thêm lớp bảo mật bổ sung (Sắp ra mắt)',
    activeSessions: 'Phiên hoạt động',
    activeSessionsDescription: 'Quản lý các phiên đăng nhập đang hoạt động (Sắp ra mắt)',
    
    // About Settings
    aboutTitle: 'Giới thiệu SyncMasterPro',
    aboutDescription: 'Thông tin ứng dụng và hỗ trợ',
    version: 'Phiên bản',
    environment: 'Môi trường',
    platform: 'Nền tảng',
    apiServer: 'Máy chủ API',
    buildDate: 'Ngày build',
    support: 'Hỗ trợ',
    license: 'Giấy phép',
    
    // Messages
    settingsSavedSuccessfully: 'Lưu cài đặt thành công',
    failedToSaveSettings: 'Không thể lưu cài đặt',
    notificationSettingsSavedSuccessfully: 'Lưu cài đặt thông báo thành công',
    failedToSaveNotificationSettings: 'Không thể lưu cài đặt thông báo',
    passwordChangedSuccessfully: 'Đổi mật khẩu thành công',
    failedToChangePassword: 'Không thể đổi mật khẩu',
    newPasswordsDoNotMatch: 'Mật khẩu mới không khớp',
    passwordMustBeAtLeast6Characters: 'Mật khẩu phải có ít nhất 6 ký tự',

    // Dashboard
    welcomeBack: 'Chào mừng trở lại',
    totalSyncTasks: 'Tổng số tác vụ đồng bộ',
    activeSyncs: 'Đồng bộ đang hoạt động',
    completedToday: 'Hoàn thành hôm nay',
    recentActivity: 'Hoạt động gần đây',
    quickActions: 'Thao tác nhanh',
    createNewTask: 'Tạo tác vụ mới',
    viewAllTasks: 'Xem tất cả tác vụ',
    syncHistory: 'Lịch sử đồng bộ',
    systemStatus: 'Trạng thái hệ thống',
    online: 'Trực tuyến',
    offline: 'Ngoại tuyến',

    // Sync Tasks
    syncTasksTitle: 'Tác vụ đồng bộ',
    syncTasksDescription: 'Quản lý các tác vụ đồng bộ hóa tệp của bạn',
    createTask: 'Tạo tác vụ',
    taskName: 'Tên tác vụ',
    sourcePath: 'Đường dẫn nguồn',
    destinationPath: 'Đường dẫn đích',
    syncType: 'Loại đồng bộ',
    bidirectional: 'Hai chiều',
    oneWay: 'Một chiều',
    status: 'Trạng thái',
    lastSync: 'Đồng bộ lần cuối',
    actions: 'Thao tác',
    start: 'Bắt đầu',
    stop: 'Dừng',
    pause: 'Tạm dừng',
    resume: 'Tiếp tục',

    // History
    historyTitle: 'Lịch sử đồng bộ',
    historyDescription: 'Xem lịch sử và nhật ký đồng bộ hóa của bạn',
    date: 'Ngày',
    task: 'Tác vụ',
    result: 'Kết quả',
    duration: 'Thời gian',
    filesProcessed: 'Tệp đã xử lý',
    viewDetails: 'Xem chi tiết',
    clearHistory: 'Xóa lịch sử',
    exportHistory: 'Xuất lịch sử',

    // Auth
    signIn: 'Đăng nhập',
    signUp: 'Đăng ký',
    email: 'Email',
    password: 'Mật khẩu',
    confirmPassword: 'Xác nhận mật khẩu',
    rememberMe: 'Ghi nhớ đăng nhập',
    forgotPassword: 'Quên mật khẩu?',
    forgotPasswordDescription: 'Nhập địa chỉ email của bạn và chúng tôi sẽ gửi liên kết đặt lại mật khẩu.',
    resetPassword: 'Đặt lại mật khẩu',
    resetPasswordDescription: 'Nhập mật khẩu mới của bạn bên dưới.',
    newPassword: 'Mật khẩu mới',
    sendResetLink: 'Gửi liên kết đặt lại',
    resetLinkSent: 'Liên kết đặt lại đã được gửi! Kiểm tra email của bạn.',
    backToLogin: 'Quay lại đăng nhập',
    back: 'Quay lại',
    sending: 'Đang gửi...',
    resetting: 'Đang đặt lại...',
    emailAddress: 'Địa chỉ Email',
    forgotPasswordError: 'Không thể gửi liên kết đặt lại',
    resetPasswordError: 'Không thể đặt lại mật khẩu',
    invalidResetToken: 'Token đặt lại không hợp lệ hoặc đã hết hạn',
    networkError: 'Lỗi mạng. Vui lòng thử lại.',
    passwordResetSuccess: 'Đặt lại mật khẩu thành công! Bây giờ bạn có thể đăng nhập với mật khẩu mới.',
    loading: 'Đang tải...',
    dontHaveAccount: 'Chưa có tài khoản?',
    alreadyHaveAccount: 'Đã có tài khoản?',
    signInToAccount: 'Đăng nhập vào tài khoản của bạn',
    createNewAccount: 'Tạo tài khoản mới',
    signOut: 'Đăng xuất',

    // Common UI
    search: 'Tìm kiếm',
    filter: 'Lọc',
    sort: 'Sắp xếp',
    refresh: 'Làm mới',
    export: 'Xuất',
    import: 'Nhập',
    upload: 'Tải lên',
    download: 'Tải xuống',
    copy: 'Sao chép',
    paste: 'Dán',
    cut: 'Cắt',
    selectAll: 'Chọn tất cả',
    deselectAll: 'Bỏ chọn tất cả',

    // File operations
    selectFolder: 'Chọn thư mục',
    selectFile: 'Chọn tệp',
    browse: 'Duyệt',
    fileName: 'Tên tệp',
    fileSize: 'Kích thước tệp',
    fileType: 'Loại tệp',
    lastModified: 'Sửa đổi lần cuối',
    selectCurrentFolder: 'Chọn thư mục hiện tại',
    nativePicker: 'Gốc',
    webPicker: 'Duyệt',
    enableRealtime: 'Bật đồng bộ thời gian thực',
    enableRealtimeDescription: 'Theo dõi và đồng bộ tệp ngay lập tức khi phát hiện thay đổi',

    // Status messages
    syncing: 'Đang đồng bộ...',
    monitoring: 'Đang theo dõi',
    completed: 'Hoàn thành',
    failed: 'Thất bại',
    paused: 'Tạm dừng',
    stopped: 'Đã dừng',
    pending: 'Đang chờ',

    // Time
    today: 'Hôm nay',
    yesterday: 'Hôm qua',
    thisWeek: 'Tuần này',
    thisMonth: 'Tháng này',
    never: 'Chưa bao giờ',

    // Units
    bytes: 'byte',
    kb: 'KB',
    mb: 'MB',
    gb: 'GB',
    seconds: 'giây',
    minutes: 'phút',
    hours: 'giờ',
    days: 'ngày',

    // Additional missing keys
    appDescription: 'Đồng bộ hóa tệp và thư mục chuyên nghiệp',
    welcomeBack: 'Chào mừng trở lại',
    createAccount: 'Tạo tài khoản',
    signInDescription: 'Đăng nhập vào tài khoản của bạn để tiếp tục',
    signUpDescription: 'Đăng ký để bắt đầu với SyncMasterPro',
    enterFullName: 'Nhập họ và tên của bạn',
    enterEmail: 'Nhập email của bạn',
    enterPassword: 'Nhập mật khẩu của bạn',
    confirmPasswordPlaceholder: 'Xác nhận mật khẩu của bạn',
    signingIn: 'Đang đăng nhập...',
    creatingAccount: 'Đang tạo tài khoản...',
    noAccount: 'Chưa có tài khoản?',
    haveAccount: 'Đã có tài khoản?',
    offlineMode: 'Chế độ ngoại tuyến',
    offlineModeDescription: 'Bạn có thể sử dụng SyncMasterPro mà không cần tài khoản chỉ để đồng bộ cục bộ',
    passwordsDoNotMatch: 'Mật khẩu không khớp',
    passwordTooShort: 'Mật khẩu phải có ít nhất 6 ký tự',

    // History Detail Modal
    syncHistoryDetails: 'Chi tiết lịch sử đồng bộ',
    basicInformation: 'Thông tin cơ bản',
    fileStatistics: 'Thống kê tệp',
    errorInformation: 'Thông tin lỗi',
    additionalDetails: 'Chi tiết bổ sung',
    syncError: 'Lỗi đồng bộ',

    // More missing keys
    createNewTask: 'Tạo tác vụ mới',
    totalSyncs: 'Tổng số đồng bộ',
    successful: 'Thành công',
    clearAllHistory: 'Xóa tất cả lịch sử',
    clearFilters: 'Xóa bộ lọc',
    syncHistoryDescription: 'Xem lịch sử chi tiết của tất cả hoạt động đồng bộ hóa',
    exportHistory: 'Xuất lịch sử',
    selectCurrentFolder: 'Chọn thư mục hiện tại',
    view: 'Xem',

    // Task form
    taskName: 'Tên tác vụ',
    browse: 'Duyệt',
    update: 'Cập nhật',

    // Quick Actions
    createNewTaskDescription: 'Tạo một tác vụ đồng bộ hóa mới',
    startAllSyncs: 'Bắt đầu tất cả đồng bộ',
    startAllSyncsDescription: 'Bắt đầu tất cả các tác vụ đồng bộ đã cấu hình',
    stopAllSyncs: 'Dừng tất cả đồng bộ',
    stopAllSyncsDescription: 'Dừng tất cả các tác vụ đồng bộ đang chạy',
    viewAllTasksDescription: 'Kiểm tra lịch sử đồng bộ hóa',

    // Security Settings
    currentPassword: 'Mật khẩu hiện tại',
    newPassword: 'Mật khẩu mới',
    confirmNewPassword: 'Xác nhận mật khẩu mới',
    changing: 'Đang thay đổi...',

    // Header
    notifications: 'Thông báo',
    profile: 'Hồ sơ',

    // Startup Settings
    startup: 'Khởi động',
    startupSettings: 'Cài đặt khởi động',
    startupSettingsDescription: 'Cấu hình hành vi khởi động ứng dụng và tùy chọn system tray',
    startupWithWindows: 'Khởi động cùng Windows',
    startupWithWindowsDescription: 'Tự động khởi động SyncMasterPro khi Windows khởi động',
    startMinimized: 'Khởi động thu nhỏ',
    startMinimizedDescription: 'Khởi động ứng dụng ở chế độ thu nhỏ vào system tray',
    minimizeToTray: 'Thu nhỏ vào Tray',
    minimizeToTrayDescription: 'Ẩn cửa sổ vào system tray khi thu nhỏ',
    closeToTray: 'Đóng vào Tray',
    closeToTrayDescription: 'Ẩn cửa sổ vào system tray khi đóng thay vì thoát',
    testHideToTray: 'Thử ẩn vào Tray',
    testHideToTrayDescription: 'Thử nghiệm chức năng ẩn vào system tray',
    hideToTray: 'Ẩn vào Tray',
    electronRequired: 'Cần ứng dụng Desktop',
    electronRequiredDescription: 'Các tính năng này chỉ có sẵn trong phiên bản desktop của SyncMasterPro',
    electronOnlyFeature: 'Tính năng này chỉ có sẵn trong ứng dụng desktop',
    settingUpdated: 'Cập nhật cài đặt thành công',
    errorUpdatingSetting: 'Không thể cập nhật cài đặt',
    errorLoadingSettings: 'Không thể tải cài đặt',
    hiddenToTray: 'Ứng dụng đã được ẩn vào system tray',
    errorHidingToTray: 'Không thể ẩn vào tray',
    updating: 'Đang cập nhật...',

    // Settings Redesign
    unsavedChanges: 'Thay đổi chưa lưu',
    discard: 'Hủy bỏ',
    resetToDefaults: 'Đặt lại mặc định',
    confirmReset: 'Xác nhận đặt lại',
    confirmResetDescription: 'Điều này sẽ đặt lại tất cả cài đặt về giá trị mặc định. Hành động này không thể hoàn tác.',
    settingsSaved: 'Đã lưu cài đặt thành công',
    errorSavingSettings: 'Không thể lưu cài đặt',
    saveChanges: 'Lưu thay đổi',
    cancel: 'Hủy',
    generalSettingsDesc: 'Hồ sơ người dùng và tùy chọn cơ bản',
    appearanceSettingsDesc: 'Giao diện, ngôn ngữ và hành vi khởi động',
    syncSettingsDesc: 'Cài đặt đồng bộ và hiệu suất',
    securitySettingsDesc: 'Bảo mật, quyền riêng tư và quản lý phiên',

    // Missing translations
    generalSettings: 'Cài đặt chung',
    appearanceSettings: 'Cài đặt giao diện',
    generalSettingsDescription: 'Quản lý thông tin hồ sơ và cài đặt ứng dụng cơ bản',
    appearanceSettingsDescription: 'Tùy chỉnh giao diện, cảm nhận và hành vi khởi động của ứng dụng',

    // General Settings
    profileInformation: 'Thông tin hồ sơ',
    updateProfile: 'Cập nhật hồ sơ',
    languageSettings: 'Cài đặt ngôn ngữ',
    interfaceLanguage: 'Ngôn ngữ giao diện',
    applicationSettings: 'Cài đặt ứng dụng',
    showNotificationsDescription: 'Hiển thị thông báo desktop cho các sự kiện đồng bộ',
    soundEnabledDescription: 'Phát âm thanh cho thông báo và cảnh báo',
    profileUpdatedSuccessfully: 'Cập nhật hồ sơ thành công',
    failedToUpdateProfile: 'Không thể cập nhật hồ sơ',
    languageChanged: 'Đã thay đổi ngôn ngữ thành công',
    failedToChangeLanguage: 'Không thể thay đổi ngôn ngữ',
    emailCannotBeChanged: 'Không thể thay đổi địa chỉ email',
    fullName: 'Họ và tên',
    emailAddress: 'Địa chỉ email',
    showNotifications: 'Hiển thị thông báo',
    soundEnabled: 'Bật âm thanh',

    // Appearance Settings
    themeSettings: 'Cài đặt giao diện',
    colorTheme: 'Chủ đề màu sắc',
    themeDescription: 'Chọn chủ đề màu sắc ưa thích cho ứng dụng',
    lightTheme: 'Sáng',
    darkTheme: 'Tối',
    systemTheme: 'Hệ thống',
    currentlyUsing: 'Hiện đang sử dụng',
    startupBehavior: 'Hành vi khởi động',
    desktopFeaturesAvailable: 'Tính năng Desktop có sẵn',
    desktopFeaturesDescription: 'Các tính năng khởi động và system tray bổ sung có sẵn trong phiên bản desktop.',
    themeChanged: 'Đã thay đổi giao diện thành công',
    failedToChangeTheme: 'Không thể thay đổi giao diện',

    // Sync Settings
    automaticSync: 'Đồng bộ tự động',
    enableAutoSync: 'Bật đồng bộ tự động',
    enableAutoSyncDescription: 'Tự động đồng bộ tệp theo khoảng thời gian đều đặn',
    syncInterval: 'Khoảng thời gian đồng bộ',
    syncIntervalDescription: 'Tần suất kiểm tra thay đổi và đồng bộ tệp',
    errorHandling: 'Xử lý lỗi',
    maxRetries: 'Số lần thử lại tối đa',
    maxRetriesDescription: 'Số lần thử lại các thao tác đồng bộ thất bại',
    performanceSettings: 'Cài đặt hiệu suất',
    performanceOptimization: 'Tối ưu hóa hiệu suất',
    performanceOptimizationDescription: 'Cấu hình đồng bộ hiện tại và số liệu hiệu suất',
    currentSyncInterval: 'Khoảng thời gian đồng bộ hiện tại',
    maxRetryAttempts: 'Số lần thử lại tối đa',
    autoSyncStatus: 'Trạng thái đồng bộ tự động',
    advancedSettings: 'Cài đặt nâng cao',
    advancedSettingsWarning: 'Cài đặt nâng cao',
    advancedSettingsWarningDescription: 'Những cài đặt này dành cho người dùng nâng cao. Thay đổi chúng có thể ảnh hưởng đến hiệu suất đồng bộ.',
    oneMinute: '1 Phút',
    fiveMinutes: '5 Phút',
    tenMinutes: '10 Phút',
    thirtyMinutes: '30 Phút',
    oneHour: '1 Giờ',
    enabled: 'Đã bật',
    disabled: 'Đã tắt',
    custom: 'Tùy chỉnh',

    // Security Settings
    sessionManagement: 'Quản lý phiên',
    sessionTimeout: 'Thời gian chờ phiên',
    sessionTimeoutDescription: 'Tự động đăng xuất sau thời gian không hoạt động',
    passwordAuthentication: 'Mật khẩu & Xác thực',
    requirePasswordChange: 'Yêu cầu thay đổi mật khẩu',
    requirePasswordChangeDescription: 'Yêu cầu người dùng thay đổi mật khẩu định kỳ',
    privacySettings: 'Cài đặt quyền riêng tư',
    dataProtection: 'Bảo vệ dữ liệu',
    dataProtectionDescription: 'Dữ liệu của bạn được bảo vệ bằng mã hóa tiêu chuẩn ngành',
    encryptionStatus: 'Trạng thái mã hóa',
    secureTransmission: 'Truyền tải bảo mật',
    currentSessionTimeout: 'Thời gian chờ phiên hiện tại',
    securityRecommendations: 'Khuyến nghị bảo mật',
    securityTips: 'Mẹo bảo mật',
    useStrongPassword: 'Sử dụng mật khẩu mạnh và duy nhất',
    enableTwoFactor: 'Bật xác thực hai yếu tố khi có sẵn',
    regularlyUpdatePassword: 'Thường xuyên cập nhật mật khẩu',
    logoutSharedDevices: 'Luôn đăng xuất khỏi thiết bị dùng chung',
    auditMonitoring: 'Kiểm toán & Giám sát',
    activityMonitoring: 'Giám sát hoạt động',
    activityMonitoringDescription: 'Theo dõi và giám sát các hoạt động liên quan đến bảo mật',
    loginTracking: 'Theo dõi đăng nhập',
    syncActivityLog: 'Nhật ký hoạt động đồng bộ',
    errorReporting: 'Báo cáo lỗi',
    active: 'Hoạt động',
    fifteenMinutes: '15 Phút',
    twoHours: '2 Giờ',
    fourHours: '4 Giờ',
    eightHours: '8 Giờ',
    securitySettings: 'Cài đặt bảo mật',

    // Notifications
    syncCompleted: 'Đồng bộ hoàn thành',
    syncStarted: 'Bắt đầu đồng bộ',
    syncWarning: 'Cảnh báo đồng bộ',
    syncError: 'Lỗi đồng bộ',
    syncInProgress: 'Đang đồng bộ',
    tasksRunning: 'tác vụ đang chạy',
    justNow: 'Vừa xong',
    minutesAgo: 'p trước',
    hoursAgo: 'g trước',
    daysAgo: 'ng trước',
    markAllRead: 'Đánh dấu đã đọc tất cả',
    clearAll: 'Xóa tất cả',
    noNotifications: 'Không có thông báo',

    // Profile
    changePhoto: 'Thay đổi ảnh',
    fullName: 'Họ và tên',
    phone: 'Số điện thoại',
    location: 'Địa điểm',
    bio: 'Tiểu sử',
    notSet: 'Chưa thiết lập',
    enterPhone: 'Nhập số điện thoại của bạn',
    enterLocation: 'Nhập địa điểm của bạn',
    enterBio: 'Hãy kể về bản thân bạn',
    saving: 'Đang lưu...',
    profileUpdated: 'Cập nhật hồ sơ thành công',
    profileUpdateError: 'Không thể cập nhật hồ sơ',

    // SyncTasks - Missing translations
    syncTasksTitle: 'Tác vụ đồng bộ',
    syncTasksDescription: 'Quản lý và giám sát các tác vụ đồng bộ hóa tệp của bạn',
    realTimeConnected: 'Kết nối thời gian thực',
    offlineMode: 'Chế độ ngoại tuyến',
    allSyncTasks: 'Tất cả tác vụ đồng bộ',
    activeTasks: 'Tác vụ đang hoạt động',
    completedTasks: 'Tác vụ đã hoàn thành',
    errorTasks: 'Tác vụ lỗi',
    noSyncTasks: 'Không có tác vụ đồng bộ',
    createFirstTask: 'Tạo tác vụ đồng bộ đầu tiên để bắt đầu',
    clearFilters: 'Xóa bộ lọc',
    noTasksFound: 'Không tìm thấy tác vụ nào phù hợp với tiêu chí của bạn',
    createNewTask: 'Tạo tác vụ mới',
    editTask: 'Chỉnh sửa tác vụ',
    taskName: 'Tên tác vụ',
    enterTaskName: 'Nhập tên tác vụ',
    sourcePath: 'Đường dẫn nguồn',
    destinationPath: 'Đường dẫn đích',
    selectSourceFolder: 'Chọn thư mục nguồn',
    selectDestinationFolder: 'Chọn thư mục đích',
    syncType: 'Loại đồng bộ',
    bidirectional: 'Hai chiều',
    sourceToDestination: 'Nguồn đến đích',
    destinationToSource: 'Đích đến nguồn',
    mirror: 'Sao chép',
    incremental: 'Tăng dần',
    bidirectionalDescription: 'Giữ cả hai thư mục đồng bộ',
    sourceToDestinationDescription: 'Đồng bộ một chiều (sao lưu)',
    destinationToSourceDescription: 'Đồng bộ một chiều (khôi phục)',
    mirrorDescription: 'Sao chép chính xác (xóa tệp thừa)',
    incrementalDescription: 'Chỉ đồng bộ tệp đã thay đổi',
    todayOnly: 'Chỉ hôm nay',
    todayOnlyDescription: 'Chỉ đồng bộ tệp được tạo hôm nay',
    fileFilters: 'Bộ lọc tệp',
    optional: 'Tùy chọn',
    fileFiltersPlaceholder: 'ví dụ: *.txt, *.jpg, !temp/*',
    fileFiltersHelp: 'Sử dụng mẫu để bao gồm/loại trừ tệp. Sử dụng ! để loại trừ.',
    advancedOptions: 'Tùy chọn nâng cao',
    deleteExtraFiles: 'Xóa tệp thừa ở đích',
    preserveTimestamps: 'Bảo toàn thời gian tệp',
    taskNameRequired: 'Tên tác vụ là bắt buộc',
    sourcePathRequired: 'Đường dẫn nguồn là bắt buộc',
    destinationPathRequired: 'Đường dẫn đích là bắt buộc',
    pathsMustBeDifferent: 'Đường dẫn nguồn và đích phải khác nhau',
    failedToCreateTask: 'Không thể tạo tác vụ',
    createTask: 'Tạo tác vụ',
    updateTask: 'Cập nhật tác vụ',
    editTaskTitle: 'Chỉnh sửa tác vụ',
    deleteTask: 'Xóa tác vụ',
    start: 'Bắt đầu',
    stop: 'Dừng',
    progress: 'Tiến độ',
    processing: 'Đang xử lý',
    source: 'Nguồn',
    destination: 'Đích',
    lastSync: 'Đồng bộ lần cuối',
    files: 'tệp',
    idle: 'Rảnh rỗi',
    unknown: 'Không xác định',
    never: 'Chưa bao giờ',
    justNow: 'Vừa xong',
    minutesAgo: 'phút trước',
    hoursAgo: 'giờ trước',
    daysAgo: 'ngày trước',
    totalSyncTasks: 'Tổng số tác vụ đồng bộ',
    activeSyncs: 'Đồng bộ đang hoạt động',
    syncing: 'Đang đồng bộ',
    paused: 'Tạm dừng',
    failed: 'Thất bại'
  }
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('en'); // 'en' or 'vi'

  // Load stored language preference
  useEffect(() => {
    const getStoredLanguage = async () => {
      try {
        let storedLanguage = 'en';
        
        if (window.electronAPI) {
          // Desktop: use Electron store
          storedLanguage = await window.electronAPI.store.get('language') || 'en';
        } else {
          // Web: use localStorage
          storedLanguage = localStorage.getItem('language') || 'en';
        }
        
        setLanguage(storedLanguage);
        
        // Update document language
        document.documentElement.lang = storedLanguage;
      } catch (error) {
        console.error('Failed to load language preference:', error);
        setLanguage('en');
      }
    };

    getStoredLanguage();
  }, []);

  const setLanguagePreference = async (newLanguage) => {
    try {
      setLanguage(newLanguage);
      
      // Update document language
      document.documentElement.lang = newLanguage;
      
      // Store preference
      if (window.electronAPI) {
        await window.electronAPI.store.set('language', newLanguage);
      } else {
        localStorage.setItem('language', newLanguage);
      }
    } catch (error) {
      console.error('Failed to save language preference:', error);
    }
  };

  const t = (key) => {
    return translations[language]?.[key] || translations.en[key] || key;
  };

  const value = {
    language,
    setLanguage: setLanguagePreference,
    t, // Translation function
    isEnglish: language === 'en',
    isVietnamese: language === 'vi'
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
