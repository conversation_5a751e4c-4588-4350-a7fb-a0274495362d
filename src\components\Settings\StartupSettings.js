import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { useNotification } from '../../contexts/NotificationContext';
import Toggle from '../UI/Toggle';

const StartupSettings = () => {
  const { t } = useLanguage();
  const { addNotification } = useNotification();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({
    startupWithWindows: false,
    minimizeToTray: true,
    closeToTray: true,
    startMinimized: false
  });

  // Check if running in Electron
  const isElectron = window.electronAPI !== undefined;

  useEffect(() => {
    // Add a small delay to ensure Electron is fully ready
    const timer = setTimeout(() => {
      loadSettings();
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const loadSettings = async (retryCount = 0) => {
    if (!isElectron) return;

    try {
      setLoading(true);

      // Check if electronAPI is available
      if (!window.electronAPI || !window.electronAPI.startup) {
        throw new Error('Electron API not available');
      }

      const startupSettings = await window.electronAPI.startup.getSettings();
      setSettings(startupSettings);
    } catch (error) {
      console.error('Error loading startup settings:', error);

      // Retry logic for timing issues
      if (retryCount < 3 && error.message.includes('No handler registered')) {
        console.log(`Retrying startup settings load (attempt ${retryCount + 1}/3)...`);
        setTimeout(() => {
          loadSettings(retryCount + 1);
        }, 1000 * (retryCount + 1)); // Exponential backoff
        return;
      }

      addNotification(t('errorLoadingSettings'), 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = async (key, value) => {
    if (!isElectron) {
      addNotification(t('electronOnlyFeature'), 'warning');
      return;
    }

    // Check if electronAPI is available
    if (!window.electronAPI || !window.electronAPI.startup) {
      addNotification(t('electronOnlyFeature'), 'warning');
      return;
    }

    try {
      setLoading(true);

      // Update local state immediately for better UX
      setSettings(prev => ({ ...prev, [key]: value }));

      // Call appropriate Electron API
      let success = false;
      switch (key) {
        case 'startupWithWindows':
          success = await window.electronAPI.startup.setStartupWithWindows(value);
          break;
        case 'minimizeToTray':
          success = await window.electronAPI.startup.setMinimizeToTray(value);
          break;
        case 'closeToTray':
          success = await window.electronAPI.startup.setCloseToTray(value);
          break;
        case 'startMinimized':
          success = await window.electronAPI.startup.setStartMinimized(value);
          break;
        default:
          break;
      }

      if (success) {
        addNotification(t('settingUpdated'), 'success');
      } else {
        // Revert local state if API call failed
        setSettings(prev => ({ ...prev, [key]: !value }));
        addNotification(t('errorUpdatingSetting'), 'error');
      }
    } catch (error) {
      console.error('Error updating startup setting:', error);
      // Revert local state
      setSettings(prev => ({ ...prev, [key]: !value }));
      addNotification(t('errorUpdatingSetting'), 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleHideToTray = async () => {
    if (!isElectron) {
      addNotification(t('electronOnlyFeature'), 'warning');
      return;
    }

    // Check if electronAPI is available
    if (!window.electronAPI || !window.electronAPI.tray) {
      addNotification(t('electronOnlyFeature'), 'warning');
      return;
    }

    try {
      const success = await window.electronAPI.tray.hideToTray();
      if (success) {
        addNotification(t('hiddenToTray'), 'info');
      }
    } catch (error) {
      console.error('Error hiding to tray:', error);
      addNotification(t('errorHidingToTray'), 'error');
    }
  };

  if (!isElectron) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t('startupSettings')}</h2>
          <p className="text-gray-600 dark:text-gray-300 mt-1">{t('startupSettingsDescription')}</p>
        </div>
        
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400 text-xl">⚠️</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                {t('electronRequired')}
              </h3>
              <p className="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                {t('electronRequiredDescription')}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t('startupSettings')}</h2>
        <p className="text-gray-600 dark:text-gray-300 mt-1">{t('startupSettingsDescription')}</p>
      </div>

      <div className="space-y-4">
        {/* Startup with Windows */}
        <Toggle
          label={t('startupWithWindows')}
          description={t('startupWithWindowsDescription')}
          checked={settings.startupWithWindows}
          onChange={(checked) => handleSettingChange('startupWithWindows', checked)}
          disabled={loading}
        />

        {/* Start Minimized */}
        <Toggle
          label={t('startMinimized')}
          description={t('startMinimizedDescription')}
          checked={settings.startMinimized}
          onChange={(checked) => handleSettingChange('startMinimized', checked)}
          disabled={loading}
        />

        {/* Minimize to Tray */}
        <Toggle
          label={t('minimizeToTray')}
          description={t('minimizeToTrayDescription')}
          checked={settings.minimizeToTray}
          onChange={(checked) => handleSettingChange('minimizeToTray', checked)}
          disabled={loading}
        />

        {/* Close to Tray */}
        <Toggle
          label={t('closeToTray')}
          description={t('closeToTrayDescription')}
          checked={settings.closeToTray}
          onChange={(checked) => handleSettingChange('closeToTray', checked)}
          disabled={loading}
        />

        {/* Test Hide to Tray */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">{t('testHideToTray')}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">{t('testHideToTrayDescription')}</p>
            </div>
            <button
              onClick={handleHideToTray}
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md disabled:opacity-50"
            >
              {t('hideToTray')}
            </button>
          </div>
        </div>
      </div>

      {loading && (
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">{t('updating')}</span>
        </div>
      )}
    </div>
  );
};

export default StartupSettings;
