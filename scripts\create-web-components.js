#!/usr/bin/env node

/**
 * <PERSON>ript to create Web Management UI components
 * These are specifically designed for centralized management
 */

const fs = require('fs-extra');
const path = require('path');

console.log('🎨 Creating Web Management UI Components');
console.log('======================================');

async function createWebComponents() {
  const webUIPath = path.join(__dirname, '../web-ui/src');
  
  try {
    // Create Layout component
    console.log('📱 Creating Layout component...');
    const layoutComponent = `import React from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';

const Layout = () => {
  const location = useLocation();
  
  const navigation = [
    { name: 'Dashboard', href: '/', icon: '📊' },
    { name: 'Client Management', href: '/clients', icon: '🖥️' },
    { name: 'Analytics', href: '/analytics', icon: '📈' },
    { name: 'Settings', href: '/settings', icon: '⚙️' }
  ];
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                SyncMasterPro - Web Management
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">Admin Panel</span>
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">A</span>
              </div>
            </div>
          </div>
        </div>
      </header>
      
      <div className="flex">
        {/* Sidebar */}
        <nav className="w-64 bg-white shadow-sm min-h-screen">
          <div className="p-4">
            <ul className="space-y-2">
              {navigation.map((item) => (
                <li key={item.name}>
                  <Link
                    to={item.href}
                    className={\`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors \${
                      location.pathname === item.href
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                    }\`}
                  >
                    <span className="mr-3">{item.icon}</span>
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </nav>
        
        {/* Main content */}
        <main className="flex-1 p-6">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default Layout;`;
    
    await fs.ensureDir(path.join(webUIPath, 'components/Layout'));
    await fs.writeFile(path.join(webUIPath, 'components/Layout/Layout.js'), layoutComponent);
    
    // Create Dashboard page
    console.log('📊 Creating Dashboard page...');
    const dashboardPage = `import React, { useState, useEffect } from 'react';

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalClients: 0,
    onlineClients: 0,
    totalTasks: 0,
    activeTasks: 0
  });
  
  const [clients, setClients] = useState([]);
  
  useEffect(() => {
    // Load dashboard data
    loadDashboardData();
  }, []);
  
  const loadDashboardData = async () => {
    try {
      // Mock data for now
      setStats({
        totalClients: 5,
        onlineClients: 3,
        totalTasks: 12,
        activeTasks: 2
      });
      
      setClients([
        { id: 1, name: 'Desktop-001', status: 'online', lastSeen: '2 minutes ago' },
        { id: 2, name: 'Desktop-002', status: 'online', lastSeen: '5 minutes ago' },
        { id: 3, name: 'Desktop-003', status: 'offline', lastSeen: '1 hour ago' }
      ]);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Management Dashboard</h1>
        <p className="text-gray-600">Monitor and manage all SyncMasterPro desktop clients</p>
      </div>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <span className="text-2xl">🖥️</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Clients</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalClients}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <span className="text-2xl">🟢</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Online Clients</p>
              <p className="text-2xl font-bold text-gray-900">{stats.onlineClients}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <span className="text-2xl">📋</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Tasks</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalTasks}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <span className="text-2xl">⚡</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Tasks</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeTasks}</p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Recent Clients */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Connected Clients</h2>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {clients.map((client) => (
              <div key={client.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div className={\`w-3 h-3 rounded-full mr-3 \${
                    client.status === 'online' ? 'bg-green-500' : 'bg-gray-400'
                  }\`}></div>
                  <div>
                    <p className="font-medium text-gray-900">{client.name}</p>
                    <p className="text-sm text-gray-500">Last seen: {client.lastSeen}</p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md text-sm hover:bg-blue-200">
                    View
                  </button>
                  <button className="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200">
                    Control
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;`;
    
    await fs.ensureDir(path.join(webUIPath, 'pages'));
    await fs.writeFile(path.join(webUIPath, 'pages/Dashboard.js'), dashboardPage);
    
    // Create Client Management page
    console.log('🖥️ Creating Client Management page...');
    const clientManagementPage = `import React, { useState, useEffect } from 'react';

const ClientManagement = () => {
  const [clients, setClients] = useState([]);
  const [selectedClient, setSelectedClient] = useState(null);
  
  useEffect(() => {
    loadClients();
  }, []);
  
  const loadClients = async () => {
    // Mock data for now
    setClients([
      {
        id: 1,
        clientId: 'desktop-001-1234567890-abc123',
        hostname: 'DESKTOP-001',
        platform: 'win32',
        status: 'online',
        lastSeen: new Date(),
        tasks: 3,
        activeTasks: 1
      },
      {
        id: 2,
        clientId: 'desktop-002-1234567891-def456',
        hostname: 'DESKTOP-002',
        platform: 'win32',
        status: 'online',
        lastSeen: new Date(Date.now() - 300000),
        tasks: 2,
        activeTasks: 0
      }
    ]);
  };
  
  const sendCommand = async (clientId, command) => {
    console.log(\`Sending command \${command} to client \${clientId}\`);
    // Implementation will connect to actual API
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Client Management</h1>
        <p className="text-gray-600">Manage and control all connected desktop clients</p>
      </div>
      
      {/* Clients Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {clients.map((client) => (
          <div key={client.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className={\`w-3 h-3 rounded-full mr-2 \${
                  client.status === 'online' ? 'bg-green-500' : 'bg-gray-400'
                }\`}></div>
                <h3 className="text-lg font-semibold text-gray-900">{client.hostname}</h3>
              </div>
              <span className={\`px-2 py-1 rounded-full text-xs font-medium \${
                client.status === 'online' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800'
              }\`}>
                {client.status}
              </span>
            </div>
            
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Platform:</span>
                <span className="text-gray-900">{client.platform}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Tasks:</span>
                <span className="text-gray-900">{client.tasks} total, {client.activeTasks} active</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Last Seen:</span>
                <span className="text-gray-900">{client.lastSeen.toLocaleTimeString()}</span>
              </div>
            </div>
            
            <div className="flex space-x-2">
              <button 
                onClick={() => setSelectedClient(client)}
                className="flex-1 px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
              >
                View Details
              </button>
              <button 
                onClick={() => sendCommand(client.clientId, 'refresh')}
                className="px-3 py-2 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200"
                disabled={client.status !== 'online'}
              >
                Refresh
              </button>
            </div>
          </div>
        ))}
      </div>
      
      {/* Client Details Modal */}
      {selectedClient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-96 overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-gray-900">
                  Client Details: {selectedClient.hostname}
                </h2>
                <button 
                  onClick={() => setSelectedClient(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">System Information</h3>
                  <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Client ID:</span>
                      <span className="text-gray-900 font-mono text-sm">{selectedClient.clientId}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Hostname:</span>
                      <span className="text-gray-900">{selectedClient.hostname}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Platform:</span>
                      <span className="text-gray-900">{selectedClient.platform}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Remote Commands</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <button 
                      onClick={() => sendCommand(selectedClient.clientId, 'start-all-sync')}
                      className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                      disabled={selectedClient.status !== 'online'}
                    >
                      Start All Sync
                    </button>
                    <button 
                      onClick={() => sendCommand(selectedClient.clientId, 'stop-all-sync')}
                      className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                      disabled={selectedClient.status !== 'online'}
                    >
                      Stop All Sync
                    </button>
                    <button 
                      onClick={() => sendCommand(selectedClient.clientId, 'refresh-tasks')}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                      disabled={selectedClient.status !== 'online'}
                    >
                      Refresh Tasks
                    </button>
                    <button 
                      onClick={() => sendCommand(selectedClient.clientId, 'get-status')}
                      className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                      disabled={selectedClient.status !== 'online'}
                    >
                      Get Status
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientManagement;`;
    
    await fs.writeFile(path.join(webUIPath, 'pages/ClientManagement.js'), clientManagementPage);
    
    console.log('✅ Web Management UI components created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating web components:', error);
    throw error;
  }
}

async function main() {
  try {
    await createWebComponents();
    console.log('\n🎉 Web Management UI components created!');
    
  } catch (error) {
    console.error('\n❌ Component creation failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { createWebComponents };
