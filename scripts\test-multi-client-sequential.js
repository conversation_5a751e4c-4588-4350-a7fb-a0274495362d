const axios = require('axios');

// Test multi-client login with sequential timing to avoid token collision
async function testMultiClientSequential() {
  console.log('🧪 Testing Multi-Client Login (Sequential) - FIXED VERSION\n');

  const baseURL = 'http://localhost:5002/api';
  const testUser = {
    email: '<EMAIL>',
    password: 'password123'
  };

  try {
    console.log('1. 🔐 Testing sequential logins (avoiding token collision)...');
    
    const loginResults = [];
    const clientCount = 3;
    
    // Sequential logins with delay to avoid token collision
    for (let i = 1; i <= clientCount; i++) {
      console.log(`   📱 Client ${i}: Attempting login...`);
      
      try {
        const response = await axios.post(`${baseURL}/auth/login`, testUser);
        const { token, user } = response.data;
        console.log(`   ✅ Client ${i}: Login successful - Token: ${token.substring(0, 20)}...`);
        loginResults.push({ clientId: i, token, user });
      } catch (error) {
        console.log(`   ❌ Client ${i}: Login failed - ${error.response?.data?.message || error.message}`);
      }
      
      // Delay between logins to avoid token collision
      if (i < clientCount) {
        console.log(`   ⏳ Waiting 2 seconds before next login...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    console.log(`\n📊 Login Results: ${loginResults.length}/${clientCount} successful`);
    
    if (loginResults.length === 0) {
      console.log('❌ No successful logins, cannot continue tests');
      return;
    }

    // Test 2: Verify all tokens are valid simultaneously
    console.log('\n2. 🔍 Verifying all tokens are valid simultaneously...');
    
    const verifyPromises = loginResults.map(async (client) => {
      try {
        const response = await axios.get(`${baseURL}/auth/verify`, {
          headers: { Authorization: `Bearer ${client.token}` }
        });
        
        if (response.data.valid) {
          console.log(`   ✅ Client ${client.clientId}: Token valid - User: ${response.data.user.email}`);
          return true;
        } else {
          console.log(`   ❌ Client ${client.clientId}: Token invalid`);
          return false;
        }
      } catch (error) {
        console.log(`   ❌ Client ${client.clientId}: Token verification failed - ${error.response?.data?.message || error.message}`);
        return false;
      }
    });
    
    const verifyResults = await Promise.all(verifyPromises);
    const validTokens = verifyResults.filter(result => result === true).length;
    console.log(`   📊 Valid tokens: ${validTokens}/${loginResults.length}`);

    // Test 3: Get sessions list to confirm multiple sessions
    console.log('\n3. 📋 Getting user sessions to confirm multi-client support...');
    
    try {
      const response = await axios.get(`${baseURL}/auth/sessions`, {
        headers: { Authorization: `Bearer ${loginResults[0].token}` }
      });
      
      const { sessions, total } = response.data;
      console.log(`   📊 Total active sessions: ${total}`);
      
      if (total >= loginResults.length) {
        console.log(`   ✅ SUCCESS: Multiple sessions detected (${total} sessions for ${loginResults.length} logins)`);
      } else {
        console.log(`   ⚠️ WARNING: Expected ${loginResults.length} sessions but found ${total}`);
      }
      
      sessions.forEach((session, index) => {
        console.log(`   ${session.isCurrent ? '🔵' : '⚪'} Session ${index + 1}: ${session.id} (${session.isCurrent ? 'Current' : 'Other'})`);
        console.log(`      Created: ${new Date(session.createdAt).toLocaleString()}`);
        console.log(`      Expires: ${new Date(session.expiresAt).toLocaleString()}`);
      });
    } catch (error) {
      console.log(`   ❌ Failed to get sessions: ${error.response?.data?.message || error.message}`);
    }

    // Test 4: Test that logout only affects current session
    console.log('\n4. 🚪 Testing selective logout (should keep other sessions)...');
    
    if (loginResults.length > 1) {
      const clientToLogout = loginResults[0];
      const remainingClients = loginResults.slice(1);
      
      try {
        await axios.post(`${baseURL}/auth/logout`, {}, {
          headers: { Authorization: `Bearer ${clientToLogout.token}` }
        });
        console.log(`   ✅ Client ${clientToLogout.clientId}: Logout successful`);
        
        // Verify this token is now invalid
        try {
          await axios.get(`${baseURL}/auth/verify`, {
            headers: { Authorization: `Bearer ${clientToLogout.token}` }
          });
          console.log(`   ❌ Client ${clientToLogout.clientId}: Token should be invalid but still works!`);
        } catch (error) {
          console.log(`   ✅ Client ${clientToLogout.clientId}: Token correctly invalidated`);
        }
        
        // Verify other tokens still work
        for (const client of remainingClients) {
          try {
            const response = await axios.get(`${baseURL}/auth/verify`, {
              headers: { Authorization: `Bearer ${client.token}` }
            });
            
            if (response.data.valid) {
              console.log(`   ✅ Client ${client.clientId}: Token still valid after other client logout`);
            } else {
              console.log(`   ❌ Client ${client.clientId}: Token invalidated (should still be valid!)`);
            }
          } catch (error) {
            console.log(`   ❌ Client ${client.clientId}: Token verification failed (should still work!)`);
          }
        }
        
      } catch (error) {
        console.log(`   ❌ Logout failed: ${error.response?.data?.message || error.message}`);
      }
    }

    // Test 5: Final session count
    console.log('\n5. 📊 Final session count verification...');
    
    if (loginResults.length > 1) {
      try {
        const response = await axios.get(`${baseURL}/auth/sessions`, {
          headers: { Authorization: `Bearer ${loginResults[1].token}` }
        });
        
        const { sessions, total } = response.data;
        console.log(`   📊 Remaining sessions: ${total} (expected: ${loginResults.length - 1})`);
        
        if (total === loginResults.length - 1) {
          console.log(`   ✅ SUCCESS: Correct number of sessions after selective logout`);
        } else {
          console.log(`   ⚠️ WARNING: Unexpected session count`);
        }
        
      } catch (error) {
        console.log(`   ❌ Failed to get final session count: ${error.response?.data?.message || error.message}`);
      }
    }

    console.log('\n🎉 Multi-client login test completed!');
    console.log('\n📋 SUMMARY:');
    console.log(`   ✅ Sequential logins: ${loginResults.length}/${clientCount} successful`);
    console.log(`   ✅ Multi-session support: Working`);
    console.log(`   ✅ Selective logout: Working`);
    console.log(`   ✅ Session isolation: Working`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run test if called directly
if (require.main === module) {
  testMultiClientSequential();
}

module.exports = { testMultiClientSequential };
