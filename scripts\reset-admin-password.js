const Database = require('better-sqlite3');
const bcrypt = require('bcryptjs');
const path = require('path');

async function resetAdminPassword() {
  console.log('🔑 Resetting admin password...');

  const dbPath = path.join(__dirname, '../data/syncmasterpro.db');
  
  try {
    const db = new Database(dbPath);

    // Hash new password
    const newPassword = 'admin123';
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update admin password
    const result = db.prepare(`
      UPDATE users 
      SET password = ?, updated_at = datetime('now') 
      WHERE email = '<EMAIL>'
    `).run(hashedPassword);

    if (result.changes > 0) {
      console.log('✅ Admin password updated successfully');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: admin123');
    } else {
      console.log('❌ Admin user not found');
    }

    db.close();

  } catch (error) {
    console.log('❌ Error resetting password:', error.message);
  }
}

resetAdminPassword();
