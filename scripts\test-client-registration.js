#!/usr/bin/env node

/**
 * Test client registration with web server
 */

const axios = require('axios');

console.log('🧪 Testing Client Registration with Web Server');
console.log('===============================================');

async function testClientRegistration() {
  console.log('\n1. Testing client registration...');
  
  const mockClient = {
    clientId: 'test-client-' + Date.now(),
    hostname: 'TEST-DESKTOP',
    platform: 'win32',
    version: '1.0.0',
    userId: 1
  };
  
  try {
    const response = await axios.post('http://localhost:5001/api/clients/register', mockClient, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Registration Response Status:', response.status);
    console.log('📝 Registration Response:', response.data);
    
    if (response.status === 200 || response.status === 201) {
      console.log('✅ Client registration successful!');
      return mockClient.clientId;
    } else {
      console.log('⚠️ Registration failed with status:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ Registration Error:', error.message);
    return null;
  }
}

async function checkRegisteredClients() {
  console.log('\n2. Checking registered clients...');
  
  try {
    const response = await axios.get('http://localhost:5001/api/clients', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📊 Clients API Status:', response.status);
    
    if (response.status === 200) {
      console.log('📊 Registered Clients:', response.data);
      console.log('📊 Total Clients:', response.data.length);
      
      if (response.data.length > 0) {
        console.log('\n📱 Client Details:');
        response.data.forEach((client, index) => {
          console.log(`   ${index + 1}. ID: ${client.clientId}`);
          console.log(`      Hostname: ${client.hostname}`);
          console.log(`      Status: ${client.status}`);
          console.log(`      Last Seen: ${client.lastSeen}`);
        });
        return true;
      } else {
        console.log('⚠️ No clients registered yet.');
        return false;
      }
    } else {
      console.log('📊 Clients API Response:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Clients Check Error:', error.message);
    return false;
  }
}

async function checkWebServerLogs() {
  console.log('\n3. Checking if web server received the registration...');
  console.log('💡 Check the web server terminal for any new logs');
  console.log('💡 Look for POST /api/clients/register requests');
}

async function main() {
  try {
    console.log('🚀 Starting client registration test...\n');
    
    // Test client registration
    const clientId = await testClientRegistration();
    
    // Wait a moment for registration to process
    if (clientId) {
      console.log('\n⏳ Waiting 3 seconds for registration to process...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    // Check registered clients
    const hasClients = await checkRegisteredClients();
    
    // Check web server logs
    await checkWebServerLogs();
    
    console.log('\n📊 Test Summary:');
    console.log('================');
    console.log('- Client Registration:', clientId ? '✅ Success' : '❌ Failed');
    console.log('- Clients in Database:', hasClients ? '✅ Found' : '⚠️ None');
    
    if (clientId && hasClients) {
      console.log('\n🎉 SUCCESS! Client registration is working!');
      console.log('🔍 Now check why desktop ClientManager cannot connect:');
      console.log('   1. Check ClientManager connection URL');
      console.log('   2. Check authentication headers');
      console.log('   3. Check CORS settings');
      console.log('   4. Check network connectivity');
    } else if (clientId && !hasClients) {
      console.log('\n🔄 Registration succeeded but client not found in database');
      console.log('💡 Check web server logs for any errors');
    } else {
      console.log('\n❌ Client registration failed');
      console.log('💡 Check web server status and API endpoints');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { testClientRegistration, checkRegisteredClients };
