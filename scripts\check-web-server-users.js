#!/usr/bin/env node

/**
 * Check users in web server database
 */

const { Pool } = require('pg');

console.log('🔍 Checking Web Server Users Database');
console.log('=====================================');

async function checkPostgreSQLUsers() {
  console.log('\n1. 🐘 Connecting to PostgreSQL...');
  
  const pool = new Pool({
    host: '*************',
    port: 5432,
    database: 'syncmasterpro',
    user: 'pi',
    password: 'ubuntu'
  });

  try {
    // Test connection
    const client = await pool.connect();
    console.log('✅ Connected to PostgreSQL');
    
    // Check users table
    console.log('\n2. 👥 Checking users table...');
    const usersResult = await client.query('SELECT id, email, name, role, status, created_at FROM users ORDER BY id');
    
    console.log(`📊 Found ${usersResult.rows.length} users:`);
    usersResult.rows.forEach(user => {
      console.log(`   - ID: ${user.id}`);
      console.log(`     Email: ${user.email}`);
      console.log(`     Name: ${user.name}`);
      console.log(`     Role: ${user.role}`);
      console.log(`     Status: ${user.status}`);
      console.log(`     Created: ${user.created_at}`);
      console.log('');
    });
    
    // <NAME_EMAIL> exists
    console.log('\n3. 🔍 Checking <EMAIL> specifically...');
    const adminResult = await client.query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    
    if (adminResult.rows.length > 0) {
      const admin = adminResult.rows[0];
      console.log('✅ <EMAIL> found:');
      console.log(`   - ID: ${admin.id}`);
      console.log(`   - Name: ${admin.name}`);
      console.log(`   - Password hash: ${admin.password.substring(0, 20)}...`);
      console.log(`   - Role: ${admin.role}`);
      console.log(`   - Status: ${admin.status}`);
    } else {
      console.log('❌ <EMAIL> not found in PostgreSQL');
      
      // Try to create admin user
      console.log('\n4. 🔧 Creating admin user...');
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('admin', 12);
      
      const insertResult = await client.query(`
        INSERT INTO users (email, password, name, role, status, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id, email, name
      `, ['<EMAIL>', hashedPassword, 'admin', 'admin', 'active']);
      
      console.log('✅ Admin user created:', insertResult.rows[0]);
    }
    
    client.release();
    await pool.end();
    
  } catch (error) {
    console.error('❌ PostgreSQL error:', error.message);
    await pool.end();
  }
}

async function testWebServerLogin() {
  console.log('\n5. 🔐 Testing web server login...');
  
  const axios = require('axios');
  
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Login Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Web server login successful!');
      console.log('👤 User:', response.data.user.name);
      return true;
    } else {
      console.log('❌ Web server login failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Web server login error:', error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting user database check...\n');
    
    // Check PostgreSQL users
    await checkPostgreSQLUsers();
    
    // Test login
    const loginSuccess = await testWebServerLogin();
    
    console.log('\n📊 Summary:');
    console.log('===========');
    console.log('- PostgreSQL Connection:', '✅ Success');
    console.log('- Web Server Login:', loginSuccess ? '✅ Success' : '❌ Failed');
    
    if (loginSuccess) {
      console.log('\n🎉 SUCCESS! Web server authentication is working!');
      console.log('🔍 You can now login to web <NAME_EMAIL> / admin');
    } else {
      console.log('\n❌ Web server authentication still has issues');
      console.log('💡 Check web server logs for more details');
    }
    
  } catch (error) {
    console.error('\n❌ Check failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
