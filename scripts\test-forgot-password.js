// Test script for forgot password functionality
const axios = require('axios');

const baseURL = 'http://localhost:5002/api';

async function testForgotPassword() {
  console.log('🔄 Testing Forgot Password Functionality\n');
  
  try {
    // Test 1: Request password reset
    console.log('1. 📧 Testing forgot password request...');
    
    const forgotResponse = await axios.post(`${baseURL}/auth/forgot-password`, {
      email: '<EMAIL>'
    });
    
    console.log('✅ Forgot password response:', forgotResponse.data);
    
    if (forgotResponse.data.resetToken) {
      const resetToken = forgotResponse.data.resetToken;
      console.log(`🔑 Reset token: ${resetToken}`);
      
      // Test 2: Reset password with token
      console.log('\n2. 🔐 Testing password reset with token...');
      
      const resetResponse = await axios.post(`${baseURL}/auth/reset-password`, {
        token: resetToken,
        newPassword: 'newpassword123'
      });
      
      console.log('✅ Password reset response:', resetResponse.data);
      
      // Test 3: Try to login with new password
      console.log('\n3. 🔓 Testing login with new password...');
      
      const loginResponse = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'newpassword123'
      });
      
      console.log('✅ Login with new password successful!');
      console.log('User:', loginResponse.data.user.name);
      
      // Test 4: Reset password back to original
      console.log('\n4. 🔄 Resetting password back to original...');
      
      const token = loginResponse.data.token;
      
      const changePasswordResponse = await axios.put(`${baseURL}/auth/password`, {
        currentPassword: 'newpassword123',
        newPassword: 'admin123'
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('✅ Password changed back to original');
      
      // Test 5: Verify original password works
      console.log('\n5. ✅ Testing login with original password...');
      
      const finalLoginResponse = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      console.log('✅ Login with original password successful!');
      
    } else {
      console.log('⚠️ No reset token in development mode');
    }
    
    // Test 6: Test with non-existent email
    console.log('\n6. 🚫 Testing with non-existent email...');
    
    const nonExistentResponse = await axios.post(`${baseURL}/auth/forgot-password`, {
      email: '<EMAIL>'
    });
    
    console.log('✅ Non-existent email response:', nonExistentResponse.data);
    
    // Test 7: Test with invalid token
    console.log('\n7. ❌ Testing with invalid token...');
    
    try {
      await axios.post(`${baseURL}/auth/reset-password`, {
        token: 'invalid-token-12345',
        newPassword: 'newpassword123'
      });
    } catch (error) {
      console.log('✅ Invalid token correctly rejected:', error.response.data);
    }
    
    console.log('\n📊 FORGOT PASSWORD TEST SUMMARY:');
    console.log('   ✅ Forgot password request: Working');
    console.log('   ✅ Password reset with token: Working');
    console.log('   ✅ Login with new password: Working');
    console.log('   ✅ Password change back: Working');
    console.log('   ✅ Non-existent email handling: Working');
    console.log('   ✅ Invalid token rejection: Working');
    
    console.log('\n🎉 All forgot password tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

async function testUIIntegration() {
  console.log('\n🖥️ UI INTEGRATION TEST INSTRUCTIONS:');
  console.log('   📝 Follow these steps to test UI integration:');
  console.log('   ');
  console.log('   1. 🌐 Open SyncMasterPro in browser/Electron');
  console.log('   2. 🔓 Go to login page');
  console.log('   3. 👀 Verify "Remember me" toggle is visible');
  console.log('   4. 👀 Verify "Forgot password?" link is visible');
  console.log('   5. 🖱️ Click "Forgot password?" link');
  console.log('   6. 📧 Enter email address (<EMAIL>)');
  console.log('   7. 📤 Click "Send Reset Link"');
  console.log('   8. 👀 Check console for reset token');
  console.log('   9. 🔗 Navigate to: http://localhost:3000/reset-password?token=TOKEN');
  console.log('   10. 🔐 Enter new password');
  console.log('   11. ✅ Submit and verify success');
  console.log('   12. 🔓 Test login with new password');
  console.log('   ');
  console.log('   Expected UI Elements:');
  console.log('   ✅ Remember Me toggle (working)');
  console.log('   ✅ Forgot Password link (clickable)');
  console.log('   ✅ Forgot Password form (email input)');
  console.log('   ✅ Reset Password form (password inputs)');
  console.log('   ✅ Success/Error messages');
  console.log('   ✅ Navigation between forms');
}

// Export for manual use
module.exports = { testForgotPassword, testUIIntegration };

// Auto-run if called directly
if (require.main === module) {
  testForgotPassword().then(() => {
    testUIIntegration();
  });
}

console.log('🔄 Forgot Password Tester loaded!');
console.log('📝 Run: node scripts/test-forgot-password.js');
