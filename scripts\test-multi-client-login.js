const axios = require('axios');

// Test multi-client login functionality
async function testMultiClientLogin() {
  console.log('🧪 Testing Multi-Client Login Functionality\n');

  const baseURL = 'http://localhost:5002/api'; // Desktop server
  const testUser = {
    email: '<EMAIL>',
    password: 'password123'
  };

  try {
    // Test 1: Login from multiple "clients"
    console.log('1. 🔐 Testing multiple concurrent logins...');
    
    const loginPromises = [];
    const clientCount = 3;
    
    for (let i = 1; i <= clientCount; i++) {
      console.log(`   📱 Client ${i}: Attempting login...`);
      
      const loginPromise = axios.post(`${baseURL}/auth/login`, testUser)
        .then(response => {
          const { token, user } = response.data;
          console.log(`   ✅ Client ${i}: Login successful - Token: ${token.substring(0, 20)}...`);
          return { clientId: i, token, user };
        })
        .catch(error => {
          console.log(`   ❌ Client ${i}: Login failed - ${error.response?.data?.message || error.message}`);
          return null;
        });
      
      loginPromises.push(loginPromise);
      
      // Small delay between logins to simulate real-world scenario
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    const loginResults = await Promise.all(loginPromises);
    const successfulLogins = loginResults.filter(result => result !== null);
    
    console.log(`\n📊 Login Results: ${successfulLogins.length}/${clientCount} successful`);
    
    if (successfulLogins.length === 0) {
      console.log('❌ No successful logins, cannot continue tests');
      return;
    }

    // Test 2: Verify all tokens are valid
    console.log('\n2. 🔍 Verifying all tokens are valid...');
    
    for (const client of successfulLogins) {
      try {
        const response = await axios.get(`${baseURL}/auth/verify`, {
          headers: { Authorization: `Bearer ${client.token}` }
        });
        
        if (response.data.valid) {
          console.log(`   ✅ Client ${client.clientId}: Token valid - User: ${response.data.user.email}`);
        } else {
          console.log(`   ❌ Client ${client.clientId}: Token invalid`);
        }
      } catch (error) {
        console.log(`   ❌ Client ${client.clientId}: Token verification failed - ${error.response?.data?.message || error.message}`);
      }
    }

    // Test 3: Get sessions list
    console.log('\n3. 📋 Getting user sessions...');
    
    try {
      const response = await axios.get(`${baseURL}/auth/sessions`, {
        headers: { Authorization: `Bearer ${successfulLogins[0].token}` }
      });
      
      const { sessions, total } = response.data;
      console.log(`   📊 Total active sessions: ${total}`);
      
      sessions.forEach((session, index) => {
        console.log(`   ${session.isCurrent ? '🔵' : '⚪'} Session ${index + 1}: ${session.id} (${session.isCurrent ? 'Current' : 'Other'})`);
        console.log(`      Created: ${new Date(session.createdAt).toLocaleString()}`);
        console.log(`      Expires: ${new Date(session.expiresAt).toLocaleString()}`);
      });
    } catch (error) {
      console.log(`   ❌ Failed to get sessions: ${error.response?.data?.message || error.message}`);
    }

    // Test 4: Test logout (should only affect current session)
    console.log('\n4. 🚪 Testing logout (should keep other sessions)...');
    
    const clientToLogout = successfulLogins[0];
    try {
      await axios.post(`${baseURL}/auth/logout`, {}, {
        headers: { Authorization: `Bearer ${clientToLogout.token}` }
      });
      console.log(`   ✅ Client ${clientToLogout.clientId}: Logout successful`);
      
      // Verify this token is now invalid
      try {
        await axios.get(`${baseURL}/auth/verify`, {
          headers: { Authorization: `Bearer ${clientToLogout.token}` }
        });
        console.log(`   ❌ Client ${clientToLogout.clientId}: Token should be invalid but still works!`);
      } catch (error) {
        console.log(`   ✅ Client ${clientToLogout.clientId}: Token correctly invalidated`);
      }
      
      // Verify other tokens still work
      for (let i = 1; i < successfulLogins.length; i++) {
        const client = successfulLogins[i];
        try {
          const response = await axios.get(`${baseURL}/auth/verify`, {
            headers: { Authorization: `Bearer ${client.token}` }
          });
          
          if (response.data.valid) {
            console.log(`   ✅ Client ${client.clientId}: Token still valid after other client logout`);
          } else {
            console.log(`   ❌ Client ${client.clientId}: Token invalidated (should still be valid!)`);
          }
        } catch (error) {
          console.log(`   ❌ Client ${client.clientId}: Token verification failed (should still work!)`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ Logout failed: ${error.response?.data?.message || error.message}`);
    }

    // Test 5: Test revoke other sessions
    if (successfulLogins.length > 1) {
      console.log('\n5. 🔄 Testing revoke other sessions...');
      
      const remainingClient = successfulLogins[1];
      try {
        const response = await axios.post(`${baseURL}/auth/sessions/revoke-others`, {}, {
          headers: { Authorization: `Bearer ${remainingClient.token}` }
        });
        
        console.log(`   ✅ Revoked ${response.data.revokedCount} other sessions`);
        
        // Verify only current session remains
        const sessionsResponse = await axios.get(`${baseURL}/auth/sessions`, {
          headers: { Authorization: `Bearer ${remainingClient.token}` }
        });
        
        console.log(`   📊 Remaining sessions: ${sessionsResponse.data.total} (should be 1)`);
        
      } catch (error) {
        console.log(`   ❌ Revoke other sessions failed: ${error.response?.data?.message || error.message}`);
      }
    }

    console.log('\n🎉 Multi-client login test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run test if called directly
if (require.main === module) {
  testMultiClientLogin();
}

module.exports = { testMultiClientLogin };
