import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { useNotification } from './NotificationContext';
import { useSocket } from './SocketContext';

// API Base URL
// Auto-detect environment for API URL
// Check multiple ways to detect Electron environment
const isElectron = window.electronAPI !== undefined ||
                   window.platform !== undefined ||
                   navigator.userAgent.toLowerCase().indexOf('electron') > -1 ||
                   (window.process && window.process.versions && window.process.versions.electron);

console.log('🔍 Environment Detection:');
console.log('  - window.electronAPI:', typeof window.electronAPI);
console.log('  - window.platform:', typeof window.platform);
console.log('  - navigator.userAgent:', navigator.userAgent);
console.log('  - isElectron:', isElectron);

const API_BASE_URL = isElectron
  ? 'http://localhost:5002/api'  // Desktop uses port 5002
  : (process.env.REACT_APP_API_URL || 'http://localhost:5001/api'); // Web uses port 5001

console.log('🎯 Selected API URL:', API_BASE_URL);

// SyncEngine will be loaded dynamically for Electron environment
let SyncEngine = null;
if (window.electronAPI) {
  // In Electron environment, SyncEngine is handled server-side
  // We don't need local SyncEngine since we use server API
  console.log('🖥️ Desktop environment detected - using server-side sync engine');
}

const SyncContext = createContext();

export const useSync = () => {
  const context = useContext(SyncContext);
  if (!context) {
    throw new Error('useSync must be used within a SyncProvider');
  }
  return context;
};

export const SyncProvider = ({ children }) => {
  const { user, token } = useAuth();
  const { addNotification } = useNotification();
  const { socket, isConnected } = useSocket();
  
  const [syncTasks, setSyncTasks] = useState([]);
  const [activeSyncs, setActiveSyncs] = useState(new Set());
  const [syncHistory, setSyncHistory] = useState([]);
  const [syncEngine, setSyncEngine] = useState(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    if (user) {
      initializeSyncEngine();
      loadSyncTasks();
      loadSyncHistory();
    }
  }, [user]);

  // This useEffect will be moved after function definitions

  useEffect(() => {
    // Monitor online status
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const initializeSyncEngine = () => {
    if (window.electronAPI) {
      console.log('🖥️ Desktop sync engine: Using server-side sync via API');
      // Desktop uses server-side sync engine via API calls
      setSyncEngine({ type: 'server-side' });
    } else {
      console.log('🌐 Web sync engine: Using server-side sync via API');
      // Web also uses server-side sync engine via API calls
      setSyncEngine({ type: 'server-side' });
    }
  };

  const loadSyncTasks = async () => {
    console.log('🔄 SyncContext: loadSyncTasks called');
    try {
      // Always prioritize server data if online
      if (isOnline && token) {
        console.log('🌐 Loading tasks from server...');
        const response = await fetch(`${API_BASE_URL}/sync/tasks`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ Server tasks loaded:', data.tasks.length);
          setSyncTasks(data.tasks || []);

          // Sync activeSyncs state with server task statuses
          const activeTasks = (data.tasks || []).filter(task =>
            task.status === 'running' ||
            task.status === 'syncing' ||
            task.status === 'monitoring'
          );

          if (activeTasks.length > 0) {
            console.log(`🔄 Syncing activeSyncs state: ${activeTasks.length} active tasks`);
            setActiveSyncs(new Set(activeTasks.map(task => task.id)));

            activeTasks.forEach(task => {
              console.log(`📊 Active task: ${task.name} (${task.status})`);
            });
          } else {
            console.log('📊 No active tasks found');
            setActiveSyncs(new Set());
          }

          // Update local storage with server data (server is source of truth)
          if (window.electronAPI) {
            await window.electronAPI.store.set('syncTasks', data.tasks || []);
            console.log('💾 Local storage synced with server');
          }
          return;
        } else {
          console.log('❌ Failed to load from server, status:', response.status);
        }
      }

      // Fallback to local storage only if server is unavailable
      console.log('📱 Loading tasks from local storage...');
      if (window.electronAPI) {
        const stored = await window.electronAPI.store.get('syncTasks') || [];
        console.log('📱 Local tasks loaded:', stored.length);
        setSyncTasks(stored);
      } else {
        console.log('⚠️ No local storage available, setting empty tasks');
        setSyncTasks([]);
      }
    } catch (error) {
      console.error('❌ Failed to load sync tasks:', error);
      addNotification('Failed to load sync tasks', 'error');

      // Final fallback to local storage
      if (window.electronAPI) {
        try {
          const stored = await window.electronAPI.store.get('syncTasks') || [];
          console.log('📱 Fallback: loaded from local storage:', stored.length);
          setSyncTasks(stored);
        } catch (localError) {
          console.error('❌ Failed to load from local storage:', localError);
          setSyncTasks([]);
        }
      } else {
        setSyncTasks([]);
      }
    }
  };

  const loadSyncHistory = async () => {
    console.log('📋 SyncContext: loadSyncHistory called');
    try {
      // Load from server if online
      if (isOnline && token) {
        console.log('🌐 Loading history from server...');
        const response = await fetch(`${API_BASE_URL}/sync/history`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          const serverHistory = data.history || [];
          console.log(`✅ Loaded ${serverHistory.length} history entries from server`);
          setSyncHistory(serverHistory);

          // Also save to local storage
          if (window.electronAPI) {
            await window.electronAPI.store.set('syncHistory', serverHistory);
          }
          return;
        } else {
          console.log('⚠️ Failed to load history from server, falling back to local storage');
        }
      }

      // Fallback to local storage
      if (window.electronAPI) {
        console.log('💾 Loading history from local storage...');
        const stored = await window.electronAPI.store.get('syncHistory') || [];
        console.log(`📱 Loaded ${stored.length} history entries from local storage`);
        setSyncHistory(stored);
      }
    } catch (error) {
      console.error('❌ Failed to load sync history:', error);
      setSyncHistory([]); // Set empty array as fallback
    }
  };

  const createSyncTask = async (taskData) => {
    console.log('🔍 SyncContext: createSyncTask called');
    console.log('📋 Task data:', taskData);
    console.log('🌐 isOnline:', isOnline);
    console.log('🔑 token:', token ? 'Present' : 'Missing');

    try {
      // Create task on server if online
      if (isOnline && token) {
        console.log('🌐 Creating task on server...');
        const response = await fetch(`${API_BASE_URL}/sync/tasks`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(taskData)
        });

        console.log('📊 Response status:', response.status);

        if (response.ok) {
          const data = await response.json();
          console.log('✅ Server response:', data);
          const newTask = data.task;

          // Update local state
          const updatedTasks = [...syncTasks, newTask];
          console.log('🔄 Updating local state with', updatedTasks.length, 'tasks');
          setSyncTasks(updatedTasks);

          // Save to local storage
          if (window.electronAPI) {
            await window.electronAPI.store.set('syncTasks', updatedTasks);
          }

          addNotification(`Sync task "${newTask.name}" created successfully`, 'success');
          console.log('✅ Task created successfully, returning result');
          return { success: true, task: newTask };
        } else {
          const errorData = await response.json();
          console.log('❌ Server error response:', errorData);
          throw new Error(errorData.message || 'Failed to create sync task');
        }
      } else {
        // Create task locally if offline
        const newTask = {
          id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          ...taskData,
          status: 'idle',
          createdAt: new Date().toISOString(),
          lastSync: null,
          filesCount: 0,
          totalSize: 0
        };

        const updatedTasks = [...syncTasks, newTask];
        setSyncTasks(updatedTasks);

        // Save to local storage
        if (window.electronAPI) {
          await window.electronAPI.store.set('syncTasks', updatedTasks);
        }

        addNotification(`Sync task "${newTask.name}" created successfully (offline)`, 'success');
        return { success: true, task: newTask };
      }
    } catch (error) {
      console.error('❌ SyncContext: Failed to create sync task:', error);
      console.error('❌ Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      addNotification('Failed to create sync task', 'error');
      return { success: false, error: error.message };
    }
  };

  const updateSyncTask = async (taskId, updates) => {
    console.log('🔄 SyncContext: updateSyncTask called for ID:', taskId, 'with updates:', updates);
    try {
      // Update server if online
      if (isOnline && token) {
        console.log('🌐 Updating task on server...');

        // Convert frontend field names to backend field names
        const serverUpdates = {};
        if (updates.name !== undefined) serverUpdates.name = updates.name;
        if (updates.sourcePath !== undefined) serverUpdates.source_path = updates.sourcePath;
        if (updates.destinationPath !== undefined) serverUpdates.destination_path = updates.destinationPath;
        if (updates.syncType !== undefined) serverUpdates.sync_type = updates.syncType;
        if (updates.schedule !== undefined) serverUpdates.schedule = updates.schedule;
        if (updates.filters !== undefined) serverUpdates.filters = updates.filters;
        if (updates.options !== undefined) serverUpdates.options = updates.options;
        if (updates.status !== undefined) serverUpdates.status = updates.status;

        const response = await fetch(`${API_BASE_URL}/sync/tasks/${taskId}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(serverUpdates)
        });

        console.log('📊 Update response status:', response.status);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to update sync task');
        }

        const responseData = await response.json();
        console.log('✅ Server response:', responseData);

        // Reload tasks from server to get updated data
        await loadSyncTasks();

        addNotification('Sync task updated successfully', 'success');
        console.log('✅ Task updated successfully');
        return { success: true, task: responseData.task };
      } else {
        // Update local state only if offline
        console.log('📱 Updating local state only (offline mode)...');
        const updatedTasks = syncTasks.map(task =>
          task.id === taskId ? { ...task, ...updates } : task
        );
        setSyncTasks(updatedTasks);

        if (window.electronAPI) {
          await window.electronAPI.store.set('syncTasks', updatedTasks);
          console.log('💾 Local storage updated');
        }

        return { success: true };
      }
    } catch (error) {
      console.error('❌ Failed to update sync task:', error);
      addNotification('Failed to update sync task', 'error');
      return { success: false, error: error.message };
    }
  };

  const deleteSyncTask = async (taskId) => {
    console.log('🗑️ SyncContext: deleteSyncTask called for ID:', taskId);
    try {
      // Stop sync if running
      if (activeSyncs.has(taskId)) {
        console.log('🛑 Stopping active sync before delete...');
        await stopSync(taskId);
      }

      // Delete from server if online
      if (isOnline && token) {
        console.log('🌐 Deleting task from server...');
        const response = await fetch(`${API_BASE_URL}/sync/tasks/${taskId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('📊 Delete response status:', response.status);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to delete sync task');
        }

        console.log('✅ Task deleted from server successfully');
      }

      // Update local state
      const updatedTasks = syncTasks.filter(task => task.id !== taskId);
      console.log('🔄 Updating local state, tasks remaining:', updatedTasks.length);
      setSyncTasks(updatedTasks);

      // Update local storage
      if (window.electronAPI) {
        await window.electronAPI.store.set('syncTasks', updatedTasks);
        console.log('💾 Local storage updated');
      }

      addNotification('Sync task deleted successfully', 'success');
      console.log('✅ Task deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Failed to delete sync task:', error);
      addNotification('Failed to delete sync task', 'error');
      return { success: false, error: error.message };
    }
  };

  const startSync = async (taskId) => {
    try {
      const task = syncTasks.find(t => t.id === taskId);
      if (!task) {
        throw new Error('Sync task not found');
      }

      if (activeSyncs.has(taskId)) {
        throw new Error('Sync already running for this task');
      }

      setActiveSyncs(prev => new Set([...prev, taskId]));

      // Check if this task has real-time sync enabled
      if (task.options?.enableRealtime) {
        await updateSyncTask(taskId, { status: 'monitoring' });

        if (isOnline && token) {
          const response = await fetch(`${API_BASE_URL}/sync/tasks/${taskId}/realtime/start`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Failed to start real-time sync');
          }

          addNotification(`Real-time monitoring started for "${task.name}"`, 'success');
        } else {
          throw new Error('Real-time sync requires online connection');
        }
      } else {
        await updateSyncTask(taskId, { status: 'syncing' });

        // Start regular sync on server if online
        if (isOnline && token) {
          const response = await fetch(`${API_BASE_URL}/sync/tasks/${taskId}/start`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Failed to start sync');
          }

          addNotification(`Sync started for "${task.name}"`, 'success');
        } else if (syncEngine) {
          // Use local sync engine if offline
          await syncEngine.startSync(task);
        } else {
          throw new Error('No sync engine available');
        }
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to start sync:', error);
      addNotification(`Failed to start sync: ${error.message}`, 'error');
      setActiveSyncs(prev => {
        const newSet = new Set(prev);
        newSet.delete(taskId);
        return newSet;
      });
      await updateSyncTask(taskId, { status: 'error' });
      return { success: false, error: error.message };
    }
  };

  const stopSync = async (taskId) => {
    try {
      const task = syncTasks.find(t => t.id === taskId);

      // Stop sync on server if online
      if (isOnline && token) {
        let endpoint;

        // Check if this task has real-time sync enabled
        if (task && task.options?.enableRealtime) {
          endpoint = `${API_BASE_URL}/sync/tasks/${taskId}/realtime/stop`;
        } else {
          endpoint = `${API_BASE_URL}/sync/tasks/${taskId}/stop`;
        }

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to stop sync');
        }
      } else if (syncEngine && syncEngine.stopSync) {
        // Use local sync engine if offline and method exists
        await syncEngine.stopSync(taskId);
      }

      setActiveSyncs(prev => {
        const newSet = new Set(prev);
        newSet.delete(taskId);
        return newSet;
      });

      await updateSyncTask(taskId, { status: 'idle' });

      const message = task && task.options?.enableRealtime
        ? 'Real-time monitoring stopped'
        : 'Sync stopped';
      addNotification(message, 'info');
      return { success: true };
    } catch (error) {
      console.error('Failed to stop sync:', error);
      addNotification('Failed to stop sync', 'error');
      return { success: false, error: error.message };
    }
  };

  const startAllSyncs = async () => {
    const results = await Promise.allSettled(
      syncTasks.map(task => startSync(task.id))
    );
    
    const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    addNotification(`Started ${successful} sync tasks`, 'success');
  };

  const stopAllSyncs = async () => {
    const results = await Promise.allSettled(
      Array.from(activeSyncs).map(taskId => stopSync(taskId))
    );
    
    addNotification('All sync tasks stopped', 'info');
  };

  // Event handlers
  const handleSyncStart = (taskId) => {
    updateSyncTask(taskId, { status: 'syncing', lastSync: new Date().toISOString() });
  };

  const handleSyncProgress = (taskId, progress) => {
    updateSyncTask(taskId, { progress });
  };

  const handleSyncComplete = (taskId, result) => {
    setActiveSyncs(prev => {
      const newSet = new Set(prev);
      newSet.delete(taskId);
      return newSet;
    });

    updateSyncTask(taskId, {
      status: 'completed',
      lastSync: new Date().toISOString(),
      filesCount: result.filesCount,
      totalSize: result.totalSize
    });

    // Add to history
    const historyEntry = {
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      taskId,
      timestamp: new Date().toISOString(),
      status: 'completed',
      filesProcessed: result.filesCount,
      totalSize: result.totalSize,
      duration: result.duration
    };

    setSyncHistory(prev => [historyEntry, ...prev.slice(0, 999)]); // Keep last 1000 entries
    addNotification(`Sync completed: ${result.filesCount} files processed`, 'success');
  };

  const handleSyncError = (taskId, error) => {
    setActiveSyncs(prev => {
      const newSet = new Set(prev);
      newSet.delete(taskId);
      return newSet;
    });

    updateSyncTask(taskId, { status: 'error', lastError: error.message });

    // Add to history
    const historyEntry = {
      id: Date.now().toString(),
      taskId,
      timestamp: new Date().toISOString(),
      status: 'error',
      error: error.message
    };

    setSyncHistory(prev => [historyEntry, ...prev.slice(0, 999)]);
    addNotification(`Sync failed: ${error.message}`, 'error');
  };

  const handleFileChange = (taskId, filePath, changeType) => {
    console.log(`File ${changeType}: ${filePath} in task ${taskId}`);
    // Handle real-time file changes
  };

  // Setup Socket.IO event listeners for real-time sync updates
  useEffect(() => {
    if (!socket || !isConnected) return;

    console.log('🔌 Setting up Socket.IO event listeners for sync updates...');

    // Sync started event
    const handleSyncStarted = (data) => {
      console.log('🔄 Socket: Sync started:', data);
      setActiveSyncs(prev => new Set([...prev, data.taskId]));
      updateSyncTask(data.taskId, {
        status: 'syncing',
        lastSync: new Date().toISOString()
      });
    };

    // Sync progress event
    const handleSyncProgressEvent = (data) => {
      console.log('📊 Socket: Sync progress:', data);
      // Progress is handled by SocketContext, just update task status if needed
      updateSyncTask(data.taskId, {
        status: 'syncing',
        progress: data.progress
      });
    };

    // Sync completed event - THIS IS THE KEY FIX
    const handleSyncCompletedEvent = async (data) => {
      console.log('✅ Socket: Sync completed:', data);

      // Remove from active syncs
      setActiveSyncs(prev => {
        const newSet = new Set(prev);
        newSet.delete(data.taskId);
        return newSet;
      });

      // Update task with completion data
      await updateSyncTask(data.taskId, {
        status: 'idle', // Set back to idle after completion
        lastSync: new Date().toISOString(),
        filesCount: data.result?.filesProcessed || data.stats?.filesProcessed || 0,
        totalSize: data.result?.totalSize || data.stats?.totalSize || 0
      });

      // Add to history
      const historyEntry = {
        id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        taskId: data.taskId,
        timestamp: data.timestamp || new Date().toISOString(),
        status: 'completed',
        filesProcessed: data.result?.filesProcessed || data.stats?.filesProcessed || 0,
        totalSize: data.result?.totalSize || data.stats?.totalSize || 0,
        duration: data.result?.duration || data.stats?.duration || 0
      };

      setSyncHistory(prev => [historyEntry, ...prev.slice(0, 999)]);

      // Reload tasks from server to get fresh data
      console.log('🔄 Reloading tasks after sync completion...');
      await loadSyncTasks();
      await loadSyncHistory();

      addNotification(`Sync completed: ${data.taskName}`, 'success');
    };

    // Sync error event
    const handleSyncErrorEvent = async (data) => {
      console.log('❌ Socket: Sync error:', data);

      // Remove from active syncs
      setActiveSyncs(prev => {
        const newSet = new Set(prev);
        newSet.delete(data.taskId);
        return newSet;
      });

      // Update task with error status
      await updateSyncTask(data.taskId, {
        status: 'error',
        lastError: data.error
      });

      // Add to history
      const historyEntry = {
        id: Date.now().toString(),
        taskId: data.taskId,
        timestamp: data.timestamp || new Date().toISOString(),
        status: 'error',
        error: data.error
      };

      setSyncHistory(prev => [historyEntry, ...prev.slice(0, 999)]);

      // Reload data
      await loadSyncTasks();
      await loadSyncHistory();

      addNotification(`Sync failed: ${data.taskName} - ${data.error}`, 'error');
    };

    // Real-time sync event handlers
    const handleRealtimeSyncStarted = (data) => {
      console.log('⚡ Socket: Real-time sync started:', data);
      setActiveSyncs(prev => new Set([...prev, data.taskId]));
      updateSyncTask(data.taskId, {
        status: 'monitoring',
        lastSync: new Date().toISOString()
      });
      addNotification(`Real-time monitoring started: ${data.taskName}`, 'success');
    };

    const handleFileChangeDetected = (data) => {
      console.log('📂 Socket: File change detected:', data);
      // You can add UI indicators for file changes here
    };

    const handleRealtimeSyncProcessing = (data) => {
      console.log('🔄 Socket: Real-time sync processing:', data);
      updateSyncTask(data.taskId, {
        status: 'syncing'
      });
    };

    const handleRealtimeSyncCompleted = (data) => {
      console.log('✅ Socket: Real-time sync completed:', data);
      updateSyncTask(data.taskId, {
        status: 'monitoring' // Back to monitoring after processing
      });
    };

    const handleRealtimeSyncStopped = (data) => {
      console.log('🛑 Socket: Real-time sync stopped:', data);
      setActiveSyncs(prev => {
        const newSet = new Set(prev);
        newSet.delete(data.taskId);
        return newSet;
      });
      updateSyncTask(data.taskId, {
        status: 'idle'
      });
    };

    const handleRealtimeSyncError = (data) => {
      console.log('❌ Socket: Real-time sync error:', data);
      updateSyncTask(data.taskId, {
        status: 'error',
        lastError: data.error
      });
      addNotification(`Real-time sync error: ${data.error}`, 'error');
    };

    // Register event listeners
    socket.on('sync-started', handleSyncStarted);
    socket.on('sync-progress', handleSyncProgressEvent);
    socket.on('sync-completed', handleSyncCompletedEvent);
    socket.on('sync-error', handleSyncErrorEvent);

    // Real-time sync events
    socket.on('realtime-sync-started', handleRealtimeSyncStarted);
    socket.on('file-change-detected', handleFileChangeDetected);
    socket.on('realtime-sync-processing', handleRealtimeSyncProcessing);
    socket.on('realtime-sync-completed', handleRealtimeSyncCompleted);
    socket.on('realtime-sync-stopped', handleRealtimeSyncStopped);
    socket.on('realtime-sync-error', handleRealtimeSyncError);

    // Cleanup function
    return () => {
      console.log('🧹 Cleaning up Socket.IO event listeners...');
      socket.off('sync-started', handleSyncStarted);
      socket.off('sync-progress', handleSyncProgressEvent);
      socket.off('sync-completed', handleSyncCompletedEvent);
      socket.off('sync-error', handleSyncErrorEvent);

      // Real-time sync event cleanup
      socket.off('realtime-sync-started', handleRealtimeSyncStarted);
      socket.off('file-change-detected', handleFileChangeDetected);
      socket.off('realtime-sync-processing', handleRealtimeSyncProcessing);
      socket.off('realtime-sync-completed', handleRealtimeSyncCompleted);
      socket.off('realtime-sync-stopped', handleRealtimeSyncStopped);
      socket.off('realtime-sync-error', handleRealtimeSyncError);
    };
  }, [socket, isConnected, updateSyncTask, loadSyncTasks, loadSyncHistory, addNotification]);

  // Delete single history entry
  const deleteHistoryEntry = async (historyId) => {
    try {
      if (isOnline && token) {
        const response = await fetch(`${API_BASE_URL}/sync/history/${historyId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to delete history entry');
        }

        // Refresh history after deletion
        await loadSyncHistory();

        return {
          success: true,
          message: 'History entry deleted successfully'
        };
      } else {
        // Offline mode - remove from local state
        setSyncHistory(prev => prev.filter(entry => entry.id !== historyId));
        return {
          success: true,
          message: 'History entry deleted locally'
        };
      }
    } catch (error) {
      console.error('Failed to delete history entry:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete history entry'
      };
    }
  };

  // Delete multiple history entries
  const deleteHistoryEntries = async (historyIds) => {
    try {
      if (isOnline && token) {
        const response = await fetch(`${API_BASE_URL}/sync/history`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ ids: historyIds })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to delete history entries');
        }

        // Refresh history after deletion
        await loadSyncHistory();

        return {
          success: true,
          message: `${historyIds.length} history entries deleted successfully`
        };
      } else {
        // Offline mode - remove from local state
        setSyncHistory(prev => prev.filter(entry => !historyIds.includes(entry.id)));
        return {
          success: true,
          message: `${historyIds.length} history entries deleted locally`
        };
      }
    } catch (error) {
      console.error('Failed to delete history entries:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete history entries'
      };
    }
  };

  // Clear all history
  const clearAllHistory = async () => {
    try {
      if (isOnline && token) {
        const response = await fetch(`${API_BASE_URL}/sync/history/clear/all`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to clear all history');
        }

        const data = await response.json();

        // Refresh history after clearing
        await loadSyncHistory();

        return {
          success: true,
          message: data.message,
          deletedCount: data.deletedCount
        };
      } else {
        // Offline mode - clear local state
        const count = syncHistory.length;
        setSyncHistory([]);
        return {
          success: true,
          message: 'All history cleared locally',
          deletedCount: count
        };
      }
    } catch (error) {
      console.error('Failed to clear all history:', error);
      return {
        success: false,
        error: error.message || 'Failed to clear all history'
      };
    }
  };

  const value = {
    syncTasks,
    activeSyncs,
    syncHistory,
    isOnline,
    createSyncTask,
    updateSyncTask,
    deleteSyncTask,
    startSync,
    stopSync,
    startAllSyncs,
    stopAllSyncs,
    loadSyncTasks,
    loadSyncHistory,
    deleteHistoryEntry,
    deleteHistoryEntries,
    clearAllHistory
  };

  return (
    <SyncContext.Provider value={value}>
      {children}
    </SyncContext.Provider>
  );
};
