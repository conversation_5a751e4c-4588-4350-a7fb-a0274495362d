const express = require('express');
const router = express.Router();
const ConflictResolver = require('../services/ConflictResolver');
const FileVersioning = require('../services/FileVersioning');
const authenticateToken = require('../middleware/auth');
const { getDatabase } = require('../database/init');

const fileVersioning = new FileVersioning();
const conflictResolver = new ConflictResolver(fileVersioning);

// Get pending conflicts for a task
router.get('/tasks/:taskId/conflicts', authenticateToken, async (req, res) => {
  try {
    const { taskId } = req.params;
    
    const conflicts = await conflictResolver.getPendingConflicts(taskId);
    
    res.json({
      success: true,
      conflicts,
      count: conflicts.length
    });
  } catch (error) {
    console.error('Failed to get conflicts:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get conflicts'
    });
  }
});

// Get conflict statistics for a task
router.get('/tasks/:taskId/conflicts/stats', authenticateToken, async (req, res) => {
  try {
    const { taskId } = req.params;
    
    const stats = await conflictResolver.getConflictStats(taskId);
    
    res.json({
      success: true,
      stats: {
        totalConflicts: parseInt(stats.total_conflicts) || 0,
        pendingConflicts: parseInt(stats.pending_conflicts) || 0,
        resolvedConflicts: parseInt(stats.resolved_conflicts) || 0,
        resolutionRate: stats.total_conflicts > 0 
          ? ((stats.resolved_conflicts / stats.total_conflicts) * 100).toFixed(1)
          : '0.0'
      }
    });
  } catch (error) {
    console.error('Failed to get conflict stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get conflict stats'
    });
  }
});

// Resolve a specific conflict
router.post('/conflicts/:conflictId/resolve', authenticateToken, async (req, res) => {
  try {
    const { conflictId } = req.params;
    const { action, userChoice } = req.body;
    
    if (!action) {
      return res.status(400).json({
        success: false,
        error: 'Action is required'
      });
    }
    
    const validActions = ['keep_source', 'keep_destination', 'merge_both', 'skip'];
    if (!validActions.includes(action)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid action. Must be one of: ' + validActions.join(', ')
      });
    }
    
    const result = await conflictResolver.resolveUserConflict(conflictId, action, userChoice);
    
    if (result.success) {
      res.json({
        success: true,
        message: 'Conflict resolved successfully',
        action,
        reason: result.reason
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.reason || 'Failed to resolve conflict'
      });
    }
  } catch (error) {
    console.error('Failed to resolve conflict:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to resolve conflict'
    });
  }
});

// Get conflict details
router.get('/conflicts/:conflictId', authenticateToken, async (req, res) => {
  try {
    const { conflictId } = req.params;
    const db = getDatabase();
    
    const result = await db.query(
      'SELECT * FROM file_conflicts WHERE id = ?',
      [conflictId]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Conflict not found'
      });
    }
    
    const conflict = result.rows[0];
    
    res.json({
      success: true,
      conflict: {
        id: conflict.id,
        taskId: conflict.task_id,
        filePath: conflict.file_path,
        sourcePath: conflict.source_path,
        destPath: conflict.dest_path,
        sourceSize: conflict.source_size,
        destSize: conflict.dest_size,
        sourceMtime: conflict.source_mtime,
        destMtime: conflict.dest_mtime,
        status: conflict.status,
        resolution: conflict.resolution,
        resolvedAt: conflict.resolved_at,
        createdAt: conflict.created_at,
        sourceSizeMB: (conflict.source_size / (1024 * 1024)).toFixed(2),
        destSizeMB: (conflict.dest_size / (1024 * 1024)).toFixed(2)
      }
    });
  } catch (error) {
    console.error('Failed to get conflict details:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get conflict details'
    });
  }
});

// Get conflict resolution history
router.get('/tasks/:taskId/conflicts/history', authenticateToken, async (req, res) => {
  try {
    const { taskId } = req.params;
    const { limit = 50, offset = 0 } = req.query;
    const db = getDatabase();
    
    const result = await db.query(
      `SELECT * FROM conflict_log 
       WHERE task_id = ? 
       ORDER BY created_at DESC 
       LIMIT ? OFFSET ?`,
      [taskId, parseInt(limit), parseInt(offset)]
    );
    
    const history = result.rows.map(log => ({
      id: log.id,
      taskId: log.task_id,
      filePath: log.file_path,
      strategy: log.strategy,
      sourceSize: log.source_size,
      destSize: log.dest_size,
      sourceMtime: log.source_mtime,
      destMtime: log.dest_mtime,
      createdAt: log.created_at,
      sourceSizeMB: log.source_size ? (log.source_size / (1024 * 1024)).toFixed(2) : null,
      destSizeMB: log.dest_size ? (log.dest_size / (1024 * 1024)).toFixed(2) : null
    }));
    
    res.json({
      success: true,
      history,
      count: history.length,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });
  } catch (error) {
    console.error('Failed to get conflict history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get conflict history'
    });
  }
});

// Update conflict resolution strategy for a task
router.put('/tasks/:taskId/conflict-strategy', authenticateToken, async (req, res) => {
  try {
    const { taskId } = req.params;
    const { strategy } = req.body;
    
    const validStrategies = [
      'ask_user', 'keep_newer', 'keep_larger', 
      'keep_source', 'keep_destination', 'merge_both', 'skip'
    ];
    
    if (!strategy || !validStrategies.includes(strategy)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid strategy. Must be one of: ' + validStrategies.join(', ')
      });
    }
    
    const db = getDatabase();
    
    // Update task options with conflict strategy
    const result = await db.query(
      'SELECT options FROM sync_tasks WHERE id = ? AND user_id = ?',
      [taskId, req.userId]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Task not found'
      });
    }
    
    let options = {};
    try {
      options = JSON.parse(result.rows[0].options || '{}');
    } catch (e) {
      options = {};
    }
    
    options.conflictStrategy = strategy;
    
    await db.query(
      'UPDATE sync_tasks SET options = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [JSON.stringify(options), taskId]
    );
    
    res.json({
      success: true,
      message: 'Conflict strategy updated successfully',
      strategy
    });
  } catch (error) {
    console.error('Failed to update conflict strategy:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update conflict strategy'
    });
  }
});

// Bulk resolve conflicts
router.post('/tasks/:taskId/conflicts/bulk-resolve', authenticateToken, async (req, res) => {
  try {
    const { taskId } = req.params;
    const { action, conflictIds } = req.body;
    
    if (!action || !conflictIds || !Array.isArray(conflictIds)) {
      return res.status(400).json({
        success: false,
        error: 'Action and conflictIds array are required'
      });
    }
    
    const validActions = ['keep_source', 'keep_destination', 'merge_both', 'skip'];
    if (!validActions.includes(action)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid action'
      });
    }
    
    const results = [];
    let successCount = 0;
    let failureCount = 0;
    
    for (const conflictId of conflictIds) {
      try {
        const result = await conflictResolver.resolveUserConflict(conflictId, action);
        results.push({
          conflictId,
          success: result.success,
          reason: result.reason
        });
        
        if (result.success) {
          successCount++;
        } else {
          failureCount++;
        }
      } catch (error) {
        results.push({
          conflictId,
          success: false,
          reason: error.message
        });
        failureCount++;
      }
    }
    
    res.json({
      success: true,
      message: `Bulk resolution completed: ${successCount} successful, ${failureCount} failed`,
      results,
      summary: {
        total: conflictIds.length,
        successful: successCount,
        failed: failureCount
      }
    });
  } catch (error) {
    console.error('Failed to bulk resolve conflicts:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to bulk resolve conflicts'
    });
  }
});

module.exports = router;
