{"name": "syncmasterpro-web-ui", "version": "1.0.0", "description": "SyncMasterPro Web Management Interface", "private": true, "dependencies": {"@heroicons/react": "^2.0.18", "axios": "^1.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "socket.io-client": "^4.8.1", "tailwindcss": "^3.3.6"}, "scripts": {"start": "cross-env PORT=3001 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "devDependencies": {"cross-env": "^7.0.3", "react-scripts": "5.0.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}