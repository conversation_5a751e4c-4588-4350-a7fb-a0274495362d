#!/usr/bin/env node

/**
 * Test client registration with proper authentication
 */

const axios = require('axios');

console.log('🧪 Testing Client Registration with Authentication');
console.log('=================================================');

async function loginToDesktopServer() {
  console.log('\n1. 🔐 Logging into desktop server...');
  
  try {
    const response = await axios.post('http://localhost:5002/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Desktop Login Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Desktop login successful!');
      console.log('👤 User:', response.data.user.name);
      console.log('🔑 Token length:', response.data.token.length);
      return {
        token: response.data.token,
        user: response.data.user
      };
    } else {
      console.log('❌ Desktop login failed:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Desktop login error:', error.message);
    return null;
  }
}

async function testClientRegistrationWithToken(token, user) {
  console.log('\n2. 📡 Testing client registration with token...');
  
  const mockClient = {
    clientId: 'test-client-' + Date.now(),
    hostname: 'TEST-DESKTOP',
    platform: 'win32',
    version: '1.0.0',
    userId: user.id
  };
  
  try {
    const response = await axios.post('http://localhost:5001/api/clients/register', mockClient, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Registration Status:', response.status);
    console.log('📝 Registration Response:', response.data);
    
    if (response.status === 200 || response.status === 201) {
      console.log('✅ Client registration successful!');
      return true;
    } else {
      console.log('❌ Client registration failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Registration error:', error.message);
    return false;
  }
}

async function testTokenValidation(token) {
  console.log('\n3. 🔍 Testing token validation on web server...');
  
  try {
    const response = await axios.get('http://localhost:5001/api/auth/verify', {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📝 Token Validation Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Token is valid on web server!');
      console.log('👤 Verified User:', response.data.user.name);
      return true;
    } else {
      console.log('❌ Token validation failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Token validation error:', error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting authentication test...\n');
    
    // Step 1: Login to desktop server
    const auth = await loginToDesktopServer();
    if (!auth) {
      console.log('\n❌ Cannot proceed without desktop login');
      return;
    }
    
    // Step 2: Test token validation on web server
    const tokenValid = await testTokenValidation(auth.token);
    
    // Step 3: Test client registration
    const registrationSuccess = await testClientRegistrationWithToken(auth.token, auth.user);
    
    console.log('\n📊 Test Summary:');
    console.log('================');
    console.log('- Desktop Login:', '✅ Success');
    console.log('- Token Validation:', tokenValid ? '✅ Success' : '❌ Failed');
    console.log('- Client Registration:', registrationSuccess ? '✅ Success' : '❌ Failed');
    
    if (tokenValid && registrationSuccess) {
      console.log('\n🎉 SUCCESS! Authentication is working correctly!');
      console.log('🔍 The issue might be in ClientManager token handling');
    } else if (!tokenValid) {
      console.log('\n❌ TOKEN ISSUE: Desktop token not valid on web server');
      console.log('💡 Check JWT_SECRET consistency between servers');
    } else {
      console.log('\n❌ REGISTRATION ISSUE: Token valid but registration failed');
      console.log('💡 Check client registration API implementation');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
