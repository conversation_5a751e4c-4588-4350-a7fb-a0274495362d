const chokidar = require('chokidar');
const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');
const { EventEmitter } = require('events');

class RealtimeSyncEngine extends EventEmitter {
  constructor(io = null) {
    super();
    this.io = io;
    this.activeWatchers = new Map(); // taskId -> watcher
    this.syncQueues = new Map(); // taskId -> queue
    this.debounceTimers = new Map(); // taskId -> timer
    this.taskConfigs = new Map(); // taskId -> task config
  }

  // Start real-time monitoring for a task
  async startRealtimeSync(task) {
    console.log(`🔄 Starting real-time sync for task: ${task.name}`);
    
    if (this.activeWatchers.has(task.id)) {
      console.log(`⚠️ Real-time sync already active for task: ${task.id}`);
      return;
    }

    // Store task config
    this.taskConfigs.set(task.id, task);
    
    // Initialize sync queue
    this.syncQueues.set(task.id, []);

    try {
      // Validate paths
      await this.validatePaths(task);

      // Start file watcher
      await this.startFileWatcher(task);

      // Emit started event
      if (this.io) {
        this.io.emit('realtime-sync-started', {
          taskId: task.id,
          taskName: task.name,
          sourcePath: task.sourcePath || task.source_path,
          destinationPath: task.destinationPath || task.destination_path,
          timestamp: new Date().toISOString()
        });
      }

      console.log(`✅ Real-time sync started for task: ${task.name}`);
      
    } catch (error) {
      console.error(`❌ Failed to start real-time sync for task ${task.id}:`, error);
      this.cleanup(task.id);
      throw error;
    }
  }

  // Stop real-time monitoring for a task
  async stopRealtimeSync(taskId) {
    console.log(`🛑 Stopping real-time sync for task: ${taskId}`);
    
    const watcher = this.activeWatchers.get(taskId);
    if (watcher) {
      await watcher.close();
      this.activeWatchers.delete(taskId);
    }

    // Clear debounce timer
    const timer = this.debounceTimers.get(taskId);
    if (timer) {
      clearTimeout(timer);
      this.debounceTimers.delete(taskId);
    }

    // Clean up
    this.cleanup(taskId);

    // Emit stopped event
    if (this.io) {
      this.io.emit('realtime-sync-stopped', {
        taskId,
        timestamp: new Date().toISOString()
      });
    }

    console.log(`✅ Real-time sync stopped for task: ${taskId}`);
  }

  // Start file watcher for a task
  async startFileWatcher(task) {
    const watchPaths = [];
    
    // Watch source path (handle both field name formats)
    const sourcePath = task.sourcePath || task.source_path;
    const destinationPath = task.destinationPath || task.destination_path;
    const syncType = task.syncType || task.sync_type;

    if (await fs.pathExists(sourcePath)) {
      watchPaths.push(sourcePath);
    }

    // For bidirectional sync, also watch destination
    if (syncType === 'bidirectional' && await fs.pathExists(destinationPath)) {
      watchPaths.push(destinationPath);
    }

    if (watchPaths.length === 0) {
      throw new Error('No valid paths to watch');
    }

    const watcher = chokidar.watch(watchPaths, {
      ignored: this.getIgnorePatterns(task),
      persistent: true,
      ignoreInitial: true, // Don't trigger for existing files
      followSymlinks: false,
      depth: task.options?.maxDepth || undefined,
      awaitWriteFinish: {
        stabilityThreshold: 1000,
        pollInterval: 100
      }
    });

    // Set up event handlers
    watcher
      .on('add', (filePath) => this.handleFileChange(task.id, filePath, 'add'))
      .on('change', (filePath) => this.handleFileChange(task.id, filePath, 'change'))
      .on('unlink', (filePath) => this.handleFileChange(task.id, filePath, 'unlink'))
      .on('addDir', (dirPath) => this.handleFileChange(task.id, dirPath, 'addDir'))
      .on('unlinkDir', (dirPath) => this.handleFileChange(task.id, dirPath, 'unlinkDir'))
      .on('error', (error) => {
        console.error(`🔥 Watcher error for task ${task.id}:`, error);
        this.emit('watcherError', { taskId: task.id, error });
      });

    this.activeWatchers.set(task.id, watcher);
    console.log(`👀 File watcher started for task: ${task.name}`);
    console.log(`📁 Watching paths: ${watchPaths.join(', ')}`);
  }

  // Handle file change events
  handleFileChange(taskId, filePath, changeType) {
    const task = this.taskConfigs.get(taskId);
    if (!task) return;

    console.log(`📂 File ${changeType}: ${filePath} (Task: ${taskId})`);

    // Add to sync queue
    const queue = this.syncQueues.get(taskId) || [];
    queue.push({
      filePath,
      changeType,
      timestamp: Date.now()
    });
    this.syncQueues.set(taskId, queue);

    // Emit file change event
    if (this.io) {
      this.io.emit('file-change-detected', {
        taskId,
        filePath,
        changeType,
        timestamp: new Date().toISOString()
      });
    }

    // Debounce sync processing
    this.debouncedSync(taskId);
  }

  // Debounced sync processing
  debouncedSync(taskId) {
    // Clear existing timer
    const existingTimer = this.debounceTimers.get(taskId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set new timer
    const timer = setTimeout(() => {
      this.processSyncQueue(taskId);
    }, 2000); // 2 second debounce

    this.debounceTimers.set(taskId, timer);
  }

  // Process sync queue for a task
  async processSyncQueue(taskId) {
    const queue = this.syncQueues.get(taskId);
    const task = this.taskConfigs.get(taskId);
    
    if (!queue || queue.length === 0 || !task) return;

    console.log(`🔄 Processing ${queue.length} file changes for task: ${taskId}`);

    // Clear the queue
    this.syncQueues.set(taskId, []);

    try {
      // Emit sync start
      if (this.io) {
        this.io.emit('realtime-sync-processing', {
          taskId,
          changesCount: queue.length,
          timestamp: new Date().toISOString()
        });
      }

      // Process each change
      let filesProcessed = 0;
      let filesAdded = 0;
      let filesUpdated = 0;
      let filesDeleted = 0;
      let totalSize = 0;

      for (const change of queue) {
        const result = await this.processFileChange(task, change);
        if (result) {
          filesProcessed++;
          if (change.changeType === 'add') filesAdded++;
          else if (change.changeType === 'change') filesUpdated++;
          else if (change.changeType === 'unlink') filesDeleted++;

          if (result.size) totalSize += result.size;
        }
      }

      // Create stats object
      const stats = {
        filesProcessed,
        filesAdded,
        filesUpdated,
        filesDeleted,
        totalSize,
        bytesTransferred: totalSize,
        duration: Date.now() - Date.now(), // Will be calculated properly
        type: 'realtime'
      };

      // Emit EventEmitter event for history creation
      this.emit('syncCompleted', { taskId, stats });

      // Emit Socket.IO event for UI updates
      if (this.io) {
        this.io.emit('realtime-sync-completed', {
          taskId,
          changesProcessed: queue.length,
          stats,
          timestamp: new Date().toISOString()
        });
      }

      console.log(`✅ Processed ${queue.length} file changes for task: ${taskId}`);

    } catch (error) {
      console.error(`❌ Failed to process sync queue for task ${taskId}:`, error);
      
      if (this.io) {
        this.io.emit('realtime-sync-error', {
          taskId,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }
  }

  // Process individual file change
  async processFileChange(task, change) {
    const { filePath, changeType } = change;

    try {
      // Handle both field name formats
      const taskSourcePath = task.sourcePath || task.source_path;
      const taskDestPath = task.destinationPath || task.destination_path;
      const syncType = task.syncType || task.sync_type;

      // For today-only sync, check if file was created today
      if (syncType === 'today-only' && (changeType === 'add' || changeType === 'change')) {
        const isToday = await this.isFileCreatedToday(filePath);
        if (!isToday) {
          console.log(`📅 Skipping file (not created today): ${path.basename(filePath)}`);
          return null; // Skip this file
        }
      }

      // Determine source and destination
      const isInSource = filePath.startsWith(taskSourcePath);
      const isInDestination = filePath.startsWith(taskDestPath);

      if (!isInSource && !isInDestination) {
        console.log(`⚠️ File not in watched paths: ${filePath}`);
        return;
      }

      // Calculate relative path and target
      let relativePath, sourcePath, destPath;

      if (isInSource) {
        relativePath = path.relative(taskSourcePath, filePath);
        sourcePath = filePath;
        destPath = path.join(taskDestPath, relativePath);
      } else {
        relativePath = path.relative(taskDestPath, filePath);
        sourcePath = path.join(taskSourcePath, relativePath);
        destPath = filePath;
      }

      // Process based on change type and sync type
      let result = null;

      switch (changeType) {
        case 'add':
        case 'change':
          if (await fs.pathExists(sourcePath)) {
            await this.copyFile(sourcePath, destPath);
            console.log(`📁 Copied: ${relativePath}`);

            // Get file size for stats
            try {
              const stats = await fs.stat(sourcePath);
              result = { size: stats.size, success: true };
            } catch (error) {
              result = { size: 0, success: true };
            }
          }
          break;

        case 'unlink':
          if (task.options?.deleteExtraFiles && await fs.pathExists(destPath)) {
            await fs.remove(destPath);
            console.log(`🗑️ Deleted: ${relativePath}`);
            result = { size: 0, success: true };
          }
          break;

        case 'addDir':
          await fs.ensureDir(destPath);
          console.log(`📂 Created directory: ${relativePath}`);
          result = { size: 0, success: true };
          break;

        case 'unlinkDir':
          if (task.options?.deleteExtraFiles && await fs.pathExists(destPath)) {
            await fs.remove(destPath);
            console.log(`🗑️ Deleted directory: ${relativePath}`);
            result = { size: 0, success: true };
          }
          break;
      }

      return result;

    } catch (error) {
      console.error(`❌ Failed to process file change ${filePath}:`, error);
      throw error;
    }
  }

  // Copy file with metadata preservation
  async copyFile(sourcePath, destPath) {
    await fs.ensureDir(path.dirname(destPath));
    await fs.copy(sourcePath, destPath, {
      preserveTimestamps: true,
      overwrite: true
    });
  }

  // Check if file was created today
  async isFileCreatedToday(filePath) {
    try {
      const stats = await fs.stat(filePath);
      const fileCreationTime = new Date(stats.birthtime || stats.mtime);
      const fileMtime = new Date(stats.mtime);

      const today = new Date();
      const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);

      // Check both creation time and modification time
      const createdToday = fileCreationTime >= todayStart && fileCreationTime < todayEnd;
      const modifiedToday = fileMtime >= todayStart && fileMtime < todayEnd;
      const isToday = createdToday || modifiedToday;

      console.log(`📅 File ${path.basename(filePath)}:`);
      console.log(`   📊 Created: ${fileCreationTime.toISOString()} (today: ${createdToday})`);
      console.log(`   📊 Modified: ${fileMtime.toISOString()} (today: ${modifiedToday})`);
      console.log(`   📊 Is today: ${isToday}`);

      return isToday;
    } catch (error) {
      console.error(`❌ Failed to check file creation time for ${filePath}:`, error);
      return false;
    }
  }

  // Validate paths exist
  async validatePaths(task) {
    const sourcePath = task.sourcePath || task.source_path;
    const destPath = task.destinationPath || task.destination_path;

    if (!await fs.pathExists(sourcePath)) {
      throw new Error(`Source path does not exist: ${sourcePath}`);
    }

    await fs.ensureDir(destPath);
  }

  // Stop all real-time sync tasks
  async stopAllRealtimeSync() {
    console.log('🛑 Stopping all real-time sync tasks...');

    // Check if activeTasks exists and has keys method
    if (!this.activeTasks || typeof this.activeTasks.keys !== 'function') {
      console.log('ℹ️ No active tasks to stop');
      return;
    }

    const taskIds = Array.from(this.activeTasks.keys());

    for (const taskId of taskIds) {
      try {
        await this.stopRealtimeSync(taskId);
      } catch (error) {
        console.error(`Failed to stop real-time sync for task ${taskId}:`, error);
      }
    }

    console.log(`✅ Stopped ${taskIds.length} real-time sync tasks`);
  }

  // Get active real-time tasks
  getActiveRealtimeTasks() {
    return Array.from(this.activeTasks.keys());
  }

  // Get ignore patterns for file watcher
  getIgnorePatterns(task) {
    const defaultIgnores = [
      '**/node_modules/**',
      '**/.git/**',
      '**/.DS_Store',
      '**/Thumbs.db',
      '**/*.tmp',
      '**/*.temp'
    ];

    const taskFilters = task.filters || [];
    const excludePatterns = taskFilters
      .filter(filter => filter.startsWith('!'))
      .map(filter => filter.substring(1));

    return [...defaultIgnores, ...excludePatterns];
  }

  // Cleanup resources for a task
  cleanup(taskId) {
    this.syncQueues.delete(taskId);
    this.taskConfigs.delete(taskId);
    this.debounceTimers.delete(taskId);
  }

  // Get status of all active watchers
  getStatus() {
    const status = {};
    for (const [taskId, watcher] of this.activeWatchers) {
      const task = this.taskConfigs.get(taskId);
      status[taskId] = {
        taskName: task?.name || 'Unknown',
        watchedPaths: watcher.getWatched(),
        queueSize: this.syncQueues.get(taskId)?.length || 0,
        isActive: true
      };
    }
    return status;
  }

  // Stop all watchers
  async stopAll() {
    console.log('🛑 Stopping all real-time sync watchers...');
    
    for (const taskId of this.activeWatchers.keys()) {
      await this.stopRealtimeSync(taskId);
    }
    
    console.log('✅ All real-time sync watchers stopped');
  }
}

module.exports = RealtimeSyncEngine;
