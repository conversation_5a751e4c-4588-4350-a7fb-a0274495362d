const path = require('path');
const fs = require('fs').promises;

// Database configuration with auto-detection
const isElectron = process.env.ELECTRON_ENV === 'true' || (process.versions && process.versions.electron);
const DB_TYPE = isElectron ? 'sqlite' : (process.env.DB_TYPE || 'postgresql');

let db;

async function initializeDatabase() {
  console.log(`🔍 Environment detection:`);
  console.log(`  - isElectron: ${isElectron}`);
  console.log(`  - ELECTRON_ENV: ${process.env.ELECTRON_ENV}`);
  console.log(`  - DB_TYPE: ${DB_TYPE}`);
  console.log(`  - Target: ${isElectron ? 'Desktop (SQLite)' : 'Web (PostgreSQL)'}`);

  if (DB_TYPE === 'postgresql') {
    // PostgreSQL for production/web
    const { Pool } = require('pg');
    
    const pool = new Pool({
      user: process.env.DB_USER || 'postgres',
      host: process.env.DB_HOST || 'localhost',
      database: process.env.DB_NAME || 'syncmasterpro',
      password: process.env.DB_PASSWORD || 'password',
      port: process.env.DB_PORT || 5432,
    });
    
    db = {
      query: (text, params = []) => {
        // Convert SQLite-style placeholders (?) to PostgreSQL-style ($1, $2, etc.)
        let pgSQL = text;
        if (text.includes('?') && params.length > 0) {
          let paramIndex = 1;
          pgSQL = text.replace(/\?/g, () => `$${paramIndex++}`);
        }
        // console.log('🐘 PostgreSQL Query:', pgSQL, 'Params:', params);
        return pool.query(pgSQL, params);
      },
      end: () => pool.end()
    };
    
    await createPostgreSQLTables();
  } else {
    // SQLite for desktop/development
    const Database = require('better-sqlite3');
    const dbPath = process.env.DB_PATH
      ? path.resolve(process.env.DB_PATH)
      : path.join(__dirname, '../../data/syncmasterpro.db');

    // Ensure data directory exists
    await fs.mkdir(path.dirname(dbPath), { recursive: true });

    const sqlite = new Database(dbPath);

    // Create wrapper to match PostgreSQL interface
    db = {
      query: (sql, params = []) => {
        try {
          // Convert PostgreSQL-style placeholders ($1, $2) to SQLite-style (?)
          let sqliteSQL = sql;
          if (sql.includes('$')) {
            for (let i = params.length; i >= 1; i--) {
              sqliteSQL = sqliteSQL.replace(new RegExp('\\$' + i, 'g'), '?');
            }
          }

          if (sqliteSQL.trim().toLowerCase().startsWith('select')) {
            const stmt = sqlite.prepare(sqliteSQL);
            const rows = Array.isArray(params) ? stmt.all(...params) : stmt.all(params);
            return Promise.resolve({ rows });
          } else {
            const stmt = sqlite.prepare(sqliteSQL);
            const result = Array.isArray(params) ? stmt.run(...params) : stmt.run(params);
            return Promise.resolve({
              rows: [],
              rowCount: result.changes,
              insertId: result.lastInsertRowid
            });
          }
        } catch (error) {
          console.error('SQLite query error:', error);
          console.error('SQL:', sql);
          console.error('Params:', params);
          return Promise.reject(error);
        }
      },
      end: () => sqlite.close()
    };

    await createSQLiteTables();
  }
  
  console.log(`Database initialized: ${DB_TYPE}`);
  return db;
}

async function createSQLiteTables() {
  const tables = [
    // Users table
    `CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      email TEXT UNIQUE NOT NULL,
      password TEXT NOT NULL,
      name TEXT NOT NULL,
      avatar TEXT,
      settings TEXT DEFAULT '{}',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // Sync tasks table
    `CREATE TABLE IF NOT EXISTS sync_tasks (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      source_path TEXT NOT NULL,
      destination_path TEXT NOT NULL,
      sync_type TEXT DEFAULT 'bidirectional',
      schedule TEXT,
      filters TEXT DEFAULT '[]',
      options TEXT DEFAULT '{}',
      status TEXT DEFAULT 'idle',
      last_sync DATETIME,
      files_count INTEGER DEFAULT 0,
      total_size INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )`,
    
    // Sync history table
    `CREATE TABLE IF NOT EXISTS sync_history (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      task_id INTEGER NOT NULL,
      user_id INTEGER,
      status TEXT NOT NULL,
      started_at DATETIME NOT NULL,
      completed_at DATETIME,
      files_processed INTEGER DEFAULT 0,
      files_added INTEGER DEFAULT 0,
      files_updated INTEGER DEFAULT 0,
      files_deleted INTEGER DEFAULT 0,
      total_size INTEGER DEFAULT 0,
      bytes_transferred INTEGER DEFAULT 0,
      error_message TEXT,
      details TEXT DEFAULT '{}',
      FOREIGN KEY (task_id) REFERENCES sync_tasks (id) ON DELETE CASCADE,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )`,
    
    // File changes table
    `CREATE TABLE IF NOT EXISTS file_changes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      task_id INTEGER NOT NULL,
      file_path TEXT NOT NULL,
      change_type TEXT NOT NULL,
      file_size INTEGER,
      checksum TEXT,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (task_id) REFERENCES sync_tasks (id) ON DELETE CASCADE
    )`,
    
    // Sessions table
    `CREATE TABLE IF NOT EXISTS sessions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      token TEXT UNIQUE NOT NULL,
      expires_at DATETIME NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )`,

    // Password resets table
    `CREATE TABLE IF NOT EXISTS password_resets (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      token TEXT UNIQUE NOT NULL,
      expires_at DATETIME NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      used_at DATETIME NULL,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )`,

    // File versions table (for versioning system)
    `CREATE TABLE IF NOT EXISTS file_versions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      task_id INTEGER NOT NULL,
      file_path TEXT NOT NULL,
      version_path TEXT NOT NULL,
      file_hash VARCHAR(64) NOT NULL,
      file_size INTEGER NOT NULL,
      reason VARCHAR(50) DEFAULT 'sync',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (task_id) REFERENCES sync_tasks (id) ON DELETE CASCADE
    )`,

    // File conflicts table (for conflict resolution)
    `CREATE TABLE IF NOT EXISTS file_conflicts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      task_id INTEGER NOT NULL,
      file_path TEXT NOT NULL,
      source_path TEXT NOT NULL,
      dest_path TEXT NOT NULL,
      source_size INTEGER NOT NULL,
      dest_size INTEGER NOT NULL,
      source_mtime DATETIME NOT NULL,
      dest_mtime DATETIME NOT NULL,
      status VARCHAR(20) DEFAULT 'pending',
      resolution VARCHAR(50),
      resolved_at DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (task_id) REFERENCES sync_tasks (id) ON DELETE CASCADE
    )`,

    // Conflict log table (for tracking conflict resolutions)
    `CREATE TABLE IF NOT EXISTS conflict_log (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      task_id INTEGER NOT NULL,
      file_path TEXT NOT NULL,
      strategy VARCHAR(50) NOT NULL,
      source_size INTEGER,
      dest_size INTEGER,
      source_mtime DATETIME,
      dest_mtime DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (task_id) REFERENCES sync_tasks (id) ON DELETE CASCADE
    )`,

    // Sync profiles table (for advanced sync configurations)
    `CREATE TABLE IF NOT EXISTS sync_profiles (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      config TEXT NOT NULL,
      is_default BOOLEAN DEFAULT FALSE,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )`,

    // Filter configurations table
    `CREATE TABLE IF NOT EXISTS filter_configurations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      filters TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )`,

    // Cloud connections table
    `CREATE TABLE IF NOT EXISTS cloud_connections (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      type VARCHAR(50) NOT NULL,
      config TEXT NOT NULL,
      connected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )`,

    // Audit trail table
    `CREATE TABLE IF NOT EXISTS audit_trail (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER,
      action VARCHAR(100) NOT NULL,
      resource VARCHAR(100),
      resource_id VARCHAR(100),
      details TEXT,
      ip_address VARCHAR(45),
      user_agent TEXT,
      session_id VARCHAR(255),
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      severity VARCHAR(20) DEFAULT 'low',
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
    )`,

    // Security alerts table
    `CREATE TABLE IF NOT EXISTS security_alerts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER,
      type VARCHAR(50) NOT NULL,
      severity VARCHAR(20) NOT NULL,
      message TEXT NOT NULL,
      details TEXT,
      status VARCHAR(20) DEFAULT 'active',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      resolved_at DATETIME,
      resolved_by INTEGER,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL,
      FOREIGN KEY (resolved_by) REFERENCES users (id) ON DELETE SET NULL
    )`,

    // Analytics reports table
    `CREATE TABLE IF NOT EXISTS analytics_reports (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      report_id VARCHAR(100) NOT NULL,
      type VARCHAR(50) NOT NULL,
      time_range VARCHAR(20) NOT NULL,
      data TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )`,

    // System notifications table
    `CREATE TABLE IF NOT EXISTS system_notifications (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER,
      type VARCHAR(50) NOT NULL,
      title VARCHAR(255) NOT NULL,
      message TEXT NOT NULL,
      data TEXT,
      status VARCHAR(20) DEFAULT 'unread',
      priority VARCHAR(20) DEFAULT 'normal',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      read_at DATETIME,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )`,

    // Desktop clients table (for client-server architecture)
    `CREATE TABLE IF NOT EXISTS desktop_clients (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      client_id VARCHAR(255) UNIQUE NOT NULL,
      hostname VARCHAR(255) NOT NULL,
      platform VARCHAR(50),
      arch VARCHAR(50),
      version VARCHAR(50),
      status VARCHAR(20) DEFAULT 'offline',
      last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
      metadata TEXT DEFAULT '{}',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )`,

    // Client commands table (for remote command execution)
    `CREATE TABLE IF NOT EXISTS client_commands (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      client_id VARCHAR(255) NOT NULL,
      command_type VARCHAR(100) NOT NULL,
      command_data TEXT,
      status VARCHAR(20) DEFAULT 'pending',
      sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      executed_at DATETIME,
      result TEXT,
      FOREIGN KEY (client_id) REFERENCES desktop_clients (client_id) ON DELETE CASCADE
    )`
  ];
  
  for (const table of tables) {
    await db.query(table);
  }
  
  // Create indexes
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_sync_tasks_user_id ON sync_tasks (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_sync_history_task_id ON sync_history (task_id)',
    'CREATE INDEX IF NOT EXISTS idx_sync_history_user_id ON sync_history (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_file_changes_task_id ON file_changes (task_id)',
    'CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions (token)',
    'CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_file_versions_task_id ON file_versions (task_id)',
    'CREATE INDEX IF NOT EXISTS idx_file_versions_hash ON file_versions (file_hash)',
    'CREATE INDEX IF NOT EXISTS idx_file_conflicts_task_id ON file_conflicts (task_id)',
    'CREATE INDEX IF NOT EXISTS idx_file_conflicts_status ON file_conflicts (status)',
    'CREATE INDEX IF NOT EXISTS idx_conflict_log_task_id ON conflict_log (task_id)',
    'CREATE INDEX IF NOT EXISTS idx_sync_profiles_user_id ON sync_profiles (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_filter_configurations_user_id ON filter_configurations (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_cloud_connections_user_id ON cloud_connections (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_cloud_connections_type ON cloud_connections (type)',
    'CREATE INDEX IF NOT EXISTS idx_audit_trail_user_id ON audit_trail (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_audit_trail_action ON audit_trail (action)',
    'CREATE INDEX IF NOT EXISTS idx_audit_trail_timestamp ON audit_trail (timestamp)',
    'CREATE INDEX IF NOT EXISTS idx_security_alerts_user_id ON security_alerts (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_security_alerts_status ON security_alerts (status)',
    'CREATE INDEX IF NOT EXISTS idx_analytics_reports_user_id ON analytics_reports (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_system_notifications_user_id ON system_notifications (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_system_notifications_status ON system_notifications (status)',
    'CREATE INDEX IF NOT EXISTS idx_desktop_clients_user_id ON desktop_clients (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_desktop_clients_client_id ON desktop_clients (client_id)',
    'CREATE INDEX IF NOT EXISTS idx_desktop_clients_status ON desktop_clients (status)',
    'CREATE INDEX IF NOT EXISTS idx_client_commands_client_id ON client_commands (client_id)',
    'CREATE INDEX IF NOT EXISTS idx_client_commands_status ON client_commands (status)'
  ];
  
  for (const index of indexes) {
    await db.query(index);
  }

  // Migration: Add bytes_transferred column if it doesn't exist
  try {
    // Check if column exists first
    const tableInfo = await db.query("PRAGMA table_info(sync_history)");
    const hasColumn = tableInfo.rows.some(row => row.name === 'bytes_transferred');

    if (!hasColumn) {
      await db.query('ALTER TABLE sync_history ADD COLUMN bytes_transferred INTEGER DEFAULT 0');
      console.log('✅ Migration: Added bytes_transferred column to sync_history');
    } else {
      console.log('ℹ️ Migration: bytes_transferred column already exists');
    }
  } catch (error) {
    if (error.message.includes('duplicate column name')) {
      console.log('ℹ️ Migration: bytes_transferred column already exists (duplicate error)');
    } else {
      console.log('⚠️ Migration error:', error.message);
    }
  }

  // Migration: Add user management fields
  const userMigrations = [
    { column: 'role', sql: 'ALTER TABLE users ADD COLUMN role VARCHAR(20) DEFAULT "user"' },
    { column: 'status', sql: 'ALTER TABLE users ADD COLUMN status VARCHAR(20) DEFAULT "active"' },
    { column: 'last_login', sql: 'ALTER TABLE users ADD COLUMN last_login DATETIME' },
    { column: 'login_count', sql: 'ALTER TABLE users ADD COLUMN login_count INTEGER DEFAULT 0' },
    { column: 'preferences', sql: 'ALTER TABLE users ADD COLUMN preferences TEXT' },
    { column: 'settings', sql: 'ALTER TABLE users ADD COLUMN settings TEXT' }
  ];

  for (const migration of userMigrations) {
    try {
      const userTableInfo = await db.query("PRAGMA table_info(users)");
      const hasUserColumn = userTableInfo.rows.some(row => row.name === migration.column);

      if (!hasUserColumn) {
        await db.query(migration.sql);
        console.log(`✅ Migration: Added ${migration.column} column to users`);
      } else {
        console.log(`ℹ️ Migration: ${migration.column} column already exists`);
      }
    } catch (error) {
      if (error.message.includes('duplicate column name')) {
        console.log(`ℹ️ Migration: ${migration.column} column already exists (duplicate error)`);
      } else {
        console.log(`⚠️ Migration error for ${migration.column}:`, error.message);
      }
    }
  }

  // Migration: Add client_id to sync_tasks
  try {
    const syncTasksTableInfo = await db.query("PRAGMA table_info(sync_tasks)");
    const hasClientIdColumn = syncTasksTableInfo.rows.some(row => row.name === 'client_id');

    if (!hasClientIdColumn) {
      await db.query('ALTER TABLE sync_tasks ADD COLUMN client_id VARCHAR(255)');
      console.log('✅ Migration: Added client_id column to sync_tasks');
    } else {
      console.log('ℹ️ Migration: client_id column already exists in sync_tasks');
    }
  } catch (error) {
    if (error.message.includes('duplicate column name')) {
      console.log('ℹ️ Migration: client_id column already exists in sync_tasks (duplicate error)');
    } else {
      console.log('⚠️ Migration error for client_id:', error.message);
    }
  }

  // Migration: Add session tracking fields
  const sessionMigrations = [
    { column: 'ip_address', sql: 'ALTER TABLE sessions ADD COLUMN ip_address VARCHAR(45)' },
    { column: 'user_agent', sql: 'ALTER TABLE sessions ADD COLUMN user_agent TEXT' },
    { column: 'last_used', sql: 'ALTER TABLE sessions ADD COLUMN last_used DATETIME' }
  ];

  for (const migration of sessionMigrations) {
    try {
      const sessionTableInfo = await db.query("PRAGMA table_info(sessions)");
      const hasSessionColumn = sessionTableInfo.rows.some(row => row.name === migration.column);

      if (!hasSessionColumn) {
        await db.query(migration.sql);
        console.log(`✅ Migration: Added ${migration.column} column to sessions`);
      } else {
        console.log(`ℹ️ Migration: ${migration.column} column already exists`);
      }
    } catch (error) {
      if (error.message.includes('duplicate column name')) {
        console.log(`ℹ️ Migration: ${migration.column} column already exists (duplicate error)`);
      } else {
        console.log(`⚠️ Migration error for ${migration.column}:`, error.message);
      }
    }
  }
}

async function createPostgreSQLTables() {
  const tables = [
    // Users table
    `CREATE TABLE IF NOT EXISTS users (
      id SERIAL PRIMARY KEY,
      email VARCHAR(255) UNIQUE NOT NULL,
      password VARCHAR(255) NOT NULL,
      name VARCHAR(255) NOT NULL,
      avatar TEXT,
      settings JSONB DEFAULT '{}',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // Sync tasks table
    `CREATE TABLE IF NOT EXISTS sync_tasks (
      id SERIAL PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      name VARCHAR(255) NOT NULL,
      source_path TEXT NOT NULL,
      destination_path TEXT NOT NULL,
      sync_type VARCHAR(50) DEFAULT 'bidirectional',
      schedule TEXT,
      filters JSONB DEFAULT '[]',
      options JSONB DEFAULT '{}',
      status VARCHAR(50) DEFAULT 'idle',
      last_sync TIMESTAMP,
      files_count INTEGER DEFAULT 0,
      total_size BIGINT DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // Sync history table
    `CREATE TABLE IF NOT EXISTS sync_history (
      id SERIAL PRIMARY KEY,
      task_id INTEGER NOT NULL REFERENCES sync_tasks(id) ON DELETE CASCADE,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      status VARCHAR(50) NOT NULL,
      started_at TIMESTAMP NOT NULL,
      completed_at TIMESTAMP,
      files_processed INTEGER DEFAULT 0,
      files_added INTEGER DEFAULT 0,
      files_updated INTEGER DEFAULT 0,
      files_deleted INTEGER DEFAULT 0,
      total_size BIGINT DEFAULT 0,
      bytes_transferred BIGINT DEFAULT 0,
      error_message TEXT,
      details JSONB DEFAULT '{}'
    )`,
    
    // File changes table
    `CREATE TABLE IF NOT EXISTS file_changes (
      id SERIAL PRIMARY KEY,
      task_id INTEGER NOT NULL REFERENCES sync_tasks(id) ON DELETE CASCADE,
      file_path TEXT NOT NULL,
      change_type VARCHAR(50) NOT NULL,
      file_size BIGINT,
      checksum VARCHAR(255),
      timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // Sessions table
    `CREATE TABLE IF NOT EXISTS sessions (
      id SERIAL PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      token VARCHAR(255) UNIQUE NOT NULL,
      expires_at TIMESTAMP NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,

    // Password resets table
    `CREATE TABLE IF NOT EXISTS password_resets (
      id SERIAL PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      token VARCHAR(255) UNIQUE NOT NULL,
      expires_at TIMESTAMP NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      used_at TIMESTAMP NULL
    )`,

    // File versions table (for versioning system)
    `CREATE TABLE IF NOT EXISTS file_versions (
      id SERIAL PRIMARY KEY,
      task_id INTEGER NOT NULL REFERENCES sync_tasks(id) ON DELETE CASCADE,
      file_path TEXT NOT NULL,
      version_path TEXT NOT NULL,
      file_hash VARCHAR(64) NOT NULL,
      file_size BIGINT NOT NULL,
      reason VARCHAR(50) DEFAULT 'sync',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,

    // File conflicts table (for conflict resolution)
    `CREATE TABLE IF NOT EXISTS file_conflicts (
      id SERIAL PRIMARY KEY,
      task_id INTEGER NOT NULL REFERENCES sync_tasks(id) ON DELETE CASCADE,
      file_path TEXT NOT NULL,
      source_path TEXT NOT NULL,
      dest_path TEXT NOT NULL,
      source_size BIGINT NOT NULL,
      dest_size BIGINT NOT NULL,
      source_mtime TIMESTAMP NOT NULL,
      dest_mtime TIMESTAMP NOT NULL,
      status VARCHAR(20) DEFAULT 'pending',
      resolution VARCHAR(50),
      resolved_at TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,

    // Conflict log table (for tracking conflict resolutions)
    `CREATE TABLE IF NOT EXISTS conflict_log (
      id SERIAL PRIMARY KEY,
      task_id INTEGER NOT NULL REFERENCES sync_tasks(id) ON DELETE CASCADE,
      file_path TEXT NOT NULL,
      strategy VARCHAR(50) NOT NULL,
      source_size BIGINT,
      dest_size BIGINT,
      source_mtime TIMESTAMP,
      dest_mtime TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,

    // Sync profiles table (for advanced sync configurations)
    `CREATE TABLE IF NOT EXISTS sync_profiles (
      id SERIAL PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      config JSONB NOT NULL,
      is_default BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,

    // Filter configurations table
    `CREATE TABLE IF NOT EXISTS filter_configurations (
      id SERIAL PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      filters JSONB NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,

    // Cloud connections table
    `CREATE TABLE IF NOT EXISTS cloud_connections (
      id SERIAL PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      type VARCHAR(50) NOT NULL,
      config JSONB NOT NULL,
      connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,

    // Audit trail table
    `CREATE TABLE IF NOT EXISTS audit_trail (
      id SERIAL PRIMARY KEY,
      user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
      action VARCHAR(100) NOT NULL,
      resource VARCHAR(100),
      resource_id VARCHAR(100),
      details JSONB,
      ip_address INET,
      user_agent TEXT,
      session_id VARCHAR(255),
      timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      severity VARCHAR(20) DEFAULT 'low'
    )`,

    // Security alerts table
    `CREATE TABLE IF NOT EXISTS security_alerts (
      id SERIAL PRIMARY KEY,
      user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
      type VARCHAR(50) NOT NULL,
      severity VARCHAR(20) NOT NULL,
      message TEXT NOT NULL,
      details JSONB,
      status VARCHAR(20) DEFAULT 'active',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      resolved_at TIMESTAMP,
      resolved_by INTEGER REFERENCES users(id) ON DELETE SET NULL
    )`,

    // Analytics reports table
    `CREATE TABLE IF NOT EXISTS analytics_reports (
      id SERIAL PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      report_id VARCHAR(100) NOT NULL,
      type VARCHAR(50) NOT NULL,
      time_range VARCHAR(20) NOT NULL,
      data JSONB NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,

    // System notifications table
    `CREATE TABLE IF NOT EXISTS system_notifications (
      id SERIAL PRIMARY KEY,
      user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
      type VARCHAR(50) NOT NULL,
      title VARCHAR(255) NOT NULL,
      message TEXT NOT NULL,
      data JSONB,
      status VARCHAR(20) DEFAULT 'unread',
      priority VARCHAR(20) DEFAULT 'normal',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      read_at TIMESTAMP
    )`,

    // Desktop clients table (for client-server architecture)
    `CREATE TABLE IF NOT EXISTS desktop_clients (
      id SERIAL PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      client_id VARCHAR(255) UNIQUE NOT NULL,
      hostname VARCHAR(255) NOT NULL,
      platform VARCHAR(50),
      arch VARCHAR(50),
      version VARCHAR(50),
      status VARCHAR(20) DEFAULT 'offline',
      last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      metadata JSONB DEFAULT '{}',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,

    // Client commands table (for remote command execution)
    `CREATE TABLE IF NOT EXISTS client_commands (
      id SERIAL PRIMARY KEY,
      client_id VARCHAR(255) NOT NULL REFERENCES desktop_clients(client_id) ON DELETE CASCADE,
      command_type VARCHAR(100) NOT NULL,
      command_data JSONB,
      status VARCHAR(20) DEFAULT 'pending',
      sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      executed_at TIMESTAMP,
      result JSONB
    )`
  ];
  
  for (const table of tables) {
    await db.query(table);
  }
  
  // Create indexes
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_sync_tasks_user_id ON sync_tasks (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_sync_history_task_id ON sync_history (task_id)',
    'CREATE INDEX IF NOT EXISTS idx_sync_history_user_id ON sync_history (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_file_changes_task_id ON file_changes (task_id)',
    'CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions (token)',
    'CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_file_versions_task_id ON file_versions (task_id)',
    'CREATE INDEX IF NOT EXISTS idx_file_versions_hash ON file_versions (file_hash)',
    'CREATE INDEX IF NOT EXISTS idx_file_conflicts_task_id ON file_conflicts (task_id)',
    'CREATE INDEX IF NOT EXISTS idx_file_conflicts_status ON file_conflicts (status)',
    'CREATE INDEX IF NOT EXISTS idx_conflict_log_task_id ON conflict_log (task_id)',
    'CREATE INDEX IF NOT EXISTS idx_sync_profiles_user_id ON sync_profiles (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_filter_configurations_user_id ON filter_configurations (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_cloud_connections_user_id ON cloud_connections (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_cloud_connections_type ON cloud_connections (type)',
    'CREATE INDEX IF NOT EXISTS idx_audit_trail_user_id ON audit_trail (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_audit_trail_action ON audit_trail (action)',
    'CREATE INDEX IF NOT EXISTS idx_audit_trail_timestamp ON audit_trail (timestamp)',
    'CREATE INDEX IF NOT EXISTS idx_security_alerts_user_id ON security_alerts (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_security_alerts_status ON security_alerts (status)',
    'CREATE INDEX IF NOT EXISTS idx_analytics_reports_user_id ON analytics_reports (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_system_notifications_user_id ON system_notifications (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_system_notifications_status ON system_notifications (status)',
    'CREATE INDEX IF NOT EXISTS idx_desktop_clients_user_id ON desktop_clients (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_desktop_clients_client_id ON desktop_clients (client_id)',
    'CREATE INDEX IF NOT EXISTS idx_desktop_clients_status ON desktop_clients (status)',
    'CREATE INDEX IF NOT EXISTS idx_client_commands_client_id ON client_commands (client_id)',
    'CREATE INDEX IF NOT EXISTS idx_client_commands_status ON client_commands (status)'
  ];
  
  for (const index of indexes) {
    try {
      await db.query(index);
    } catch (error) {
      // Ignore "already exists" errors
      if (!error.message.includes('already exists')) {
        throw error;
      }
    }
  }
}

function getDatabase() {
  if (!db) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return db;
}

module.exports = {
  initializeDatabase,
  getDatabase
};
