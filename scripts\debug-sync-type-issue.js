const axios = require('axios');

async function debugSyncTypeIssue() {
  console.log('🔍 DEBUGGING SYNC TYPE UPDATE ISSUE\n');

  const serverUrl = 'http://localhost:5000';
  let token = null;

  try {
    // Step 1: Login
    console.log('🔐 STEP 1: Authentication');
    console.log('=' .repeat(50));

    try {
      const loginResponse = await axios.post(`${serverUrl}/api/auth/login`, {
        email: '<EMAIL>',
        password: 'password123'
      }, { timeout: 5000 });

      token = loginResponse.data.token;
      console.log('✅ Login successful');
    } catch (error) {
      console.log(`❌ Login failed: ${error.code === 'ECONNREFUSED' ? 'Server not running' : error.message}`);
      return;
    }

    // Step 2: Get existing tasks
    console.log('\n📋 STEP 2: Get Existing Tasks');
    console.log('=' .repeat(50));

    let tasks = [];
    try {
      const tasksResponse = await axios.get(`${serverUrl}/api/sync/tasks`, {
        headers: { Authorization: `Bearer ${token}` },
        timeout: 10000
      });

      tasks = tasksResponse.data.tasks || [];
      console.log(`✅ Found ${tasks.length} existing tasks`);

      if (tasks.length > 0) {
        const task = tasks[0];
        console.log(`\n📋 SAMPLE TASK:`);
        console.log(`   🆔 ID: ${task.id}`);
        console.log(`   📝 Name: ${task.name}`);
        console.log(`   🔄 Sync Type: ${task.syncType}`);
        console.log(`   📁 Source: ${task.sourcePath}`);
        console.log(`   📂 Destination: ${task.destinationPath}`);
      }
    } catch (error) {
      console.log(`❌ Failed to get tasks: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }

    // Step 3: Create a test task if none exist
    let testTask = null;
    if (tasks.length === 0) {
      console.log('\n➕ STEP 3: Create Test Task');
      console.log('=' .repeat(50));

      try {
        const createResponse = await axios.post(`${serverUrl}/api/sync/tasks`, {
          name: 'Debug Test Task',
          sourcePath: 'C:\\TestSource',
          destinationPath: 'C:\\TestDestination',
          syncType: 'bidirectional',
          schedule: null,
          filters: [],
          options: {
            deleteExtraFiles: false,
            preserveTimestamps: true,
            enableRealtime: false
          }
        }, {
          headers: { Authorization: `Bearer ${token}` },
          timeout: 10000
        });

        testTask = createResponse.data.task;
        console.log(`✅ Created test task: ${testTask.id}`);
        console.log(`   🔄 Initial Sync Type: ${testTask.syncType}`);
      } catch (error) {
        console.log(`❌ Failed to create test task: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
        return;
      }
    } else {
      testTask = tasks[0];
    }

    // Step 4: Test sync type update
    console.log('\n🔄 STEP 4: Test Sync Type Update');
    console.log('=' .repeat(50));

    const syncTypeOptions = [
      'bidirectional',
      'source-to-destination', 
      'destination-to-source',
      'mirror',
      'incremental',
      'today-only'
    ];

    const currentSyncType = testTask.syncType;
    const newSyncType = syncTypeOptions.find(type => type !== currentSyncType) || 'mirror';

    console.log(`📝 Current sync type: ${currentSyncType}`);
    console.log(`🔄 Updating to: ${newSyncType}`);

    // Test 1: Frontend-style update (camelCase)
    console.log('\n🧪 TEST 1: Frontend-style Update (camelCase)');
    try {
      const updateData = {
        syncType: newSyncType
      };

      console.log('📤 Sending update data:', updateData);

      const updateResponse = await axios.put(`${serverUrl}/api/sync/tasks/${testTask.id}`, updateData, {
        headers: { Authorization: `Bearer ${token}` },
        timeout: 10000
      });

      console.log(`✅ Update response: ${updateResponse.status}`);
      console.log('📥 Response data:', updateResponse.data);

      // Verify the update
      const verifyResponse = await axios.get(`${serverUrl}/api/sync/tasks`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      const updatedTask = verifyResponse.data.tasks.find(t => t.id === testTask.id);
      if (updatedTask) {
        console.log(`🔍 Verification - Sync Type: ${updatedTask.syncType}`);
        if (updatedTask.syncType === newSyncType) {
          console.log('✅ Frontend-style update SUCCESSFUL');
        } else {
          console.log('❌ Frontend-style update FAILED');
          console.log(`   Expected: ${newSyncType}`);
          console.log(`   Got: ${updatedTask.syncType}`);
        }
      }
    } catch (error) {
      console.log(`❌ Frontend-style update failed: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }

    // Test 2: Backend-style update (snake_case)
    console.log('\n🧪 TEST 2: Backend-style Update (snake_case)');
    const anotherSyncType = syncTypeOptions.find(type => type !== newSyncType && type !== currentSyncType) || 'incremental';

    try {
      const updateData = {
        sync_type: anotherSyncType
      };

      console.log('📤 Sending update data:', updateData);

      const updateResponse = await axios.put(`${serverUrl}/api/sync/tasks/${testTask.id}`, updateData, {
        headers: { Authorization: `Bearer ${token}` },
        timeout: 10000
      });

      console.log(`✅ Update response: ${updateResponse.status}`);

      // Verify the update
      const verifyResponse = await axios.get(`${serverUrl}/api/sync/tasks`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      const updatedTask = verifyResponse.data.tasks.find(t => t.id === testTask.id);
      if (updatedTask) {
        console.log(`🔍 Verification - Sync Type: ${updatedTask.syncType}`);
        if (updatedTask.syncType === anotherSyncType) {
          console.log('✅ Backend-style update SUCCESSFUL');
        } else {
          console.log('❌ Backend-style update FAILED');
          console.log(`   Expected: ${anotherSyncType}`);
          console.log(`   Got: ${updatedTask.syncType}`);
        }
      }
    } catch (error) {
      console.log(`❌ Backend-style update failed: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }

    // Step 5: Test complete task update
    console.log('\n🧪 STEP 5: Test Complete Task Update');
    console.log('=' .repeat(50));

    try {
      const completeUpdateData = {
        name: 'Updated Debug Task',
        sourcePath: 'C:\\UpdatedSource',
        destinationPath: 'C:\\UpdatedDestination',
        syncType: 'destination-to-source',
        schedule: 'daily',
        filters: ['*.txt', '*.doc'],
        options: {
          deleteExtraFiles: true,
          preserveTimestamps: false,
          enableRealtime: true
        }
      };

      console.log('📤 Sending complete update data:', JSON.stringify(completeUpdateData, null, 2));

      const updateResponse = await axios.put(`${serverUrl}/api/sync/tasks/${testTask.id}`, completeUpdateData, {
        headers: { Authorization: `Bearer ${token}` },
        timeout: 10000
      });

      console.log(`✅ Complete update response: ${updateResponse.status}`);

      // Verify the complete update
      const verifyResponse = await axios.get(`${serverUrl}/api/sync/tasks`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      const updatedTask = verifyResponse.data.tasks.find(t => t.id === testTask.id);
      if (updatedTask) {
        console.log('\n🔍 VERIFICATION RESULTS:');
        console.log(`   📝 Name: ${updatedTask.name}`);
        console.log(`   📁 Source: ${updatedTask.sourcePath}`);
        console.log(`   📂 Destination: ${updatedTask.destinationPath}`);
        console.log(`   🔄 Sync Type: ${updatedTask.syncType}`);
        console.log(`   ⏰ Schedule: ${updatedTask.schedule}`);
        console.log(`   🔍 Filters: ${JSON.stringify(updatedTask.filters)}`);
        console.log(`   ⚙️ Options: ${JSON.stringify(updatedTask.options)}`);

        // Check each field
        const checks = [
          { field: 'name', expected: completeUpdateData.name, actual: updatedTask.name },
          { field: 'sourcePath', expected: completeUpdateData.sourcePath, actual: updatedTask.sourcePath },
          { field: 'destinationPath', expected: completeUpdateData.destinationPath, actual: updatedTask.destinationPath },
          { field: 'syncType', expected: completeUpdateData.syncType, actual: updatedTask.syncType },
          { field: 'schedule', expected: completeUpdateData.schedule, actual: updatedTask.schedule }
        ];

        console.log('\n📊 FIELD-BY-FIELD VERIFICATION:');
        checks.forEach(check => {
          const status = check.expected === check.actual ? '✅' : '❌';
          console.log(`   ${status} ${check.field}: ${check.actual} ${check.expected !== check.actual ? `(expected: ${check.expected})` : ''}`);
        });
      }
    } catch (error) {
      console.log(`❌ Complete update failed: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
      if (error.response?.data) {
        console.log('📥 Error response data:', JSON.stringify(error.response.data, null, 2));
      }
    }

    console.log('\n📋 SUMMARY:');
    console.log('=' .repeat(50));
    console.log('✅ Test completed');
    console.log('💡 Check the results above to identify sync type update issues');
    console.log('🔧 If updates fail, check server logs for field mapping issues');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run debug
debugSyncTypeIssue();
