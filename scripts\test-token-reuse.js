const axios = require('axios');

async function testTokenReuse() {
  console.log('🔑 TESTING TOKEN REUSE BEHAVIOR\n');

  const desktopServerUrl = 'http://localhost:5000';
  const webServerUrl = 'http://localhost:5001';
  const testCredentials = {
    email: '<EMAIL>',
    password: 'password123'
  };

  // Test Desktop Server Token Reuse
  await testServerTokenReuse('Desktop', desktopServerUrl, testCredentials);
  
  // Test Web Server Token Reuse
  await testServerTokenReuse('Web', webServerUrl, testCredentials);
  
  // Test Cross-Server Token Behavior
  await testCrossServerTokens();
}

async function testServerTokenReuse(serverName, serverUrl, credentials) {
  console.log(`🖥️ TESTING ${serverName.toUpperCase()} SERVER TOKEN REUSE`);
  console.log('=' .repeat(50));

  try {
    // First login
    console.log('🔐 First login attempt...');
    const login1 = await axios.post(`${serverUrl}/api/auth/login`, credentials, { timeout: 5000 });
    const token1 = login1.data.token;
    console.log(`✅ First login successful`);
    console.log(`   🔑 Token 1: ${token1.substring(0, 30)}...`);

    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Second login (should reuse token)
    console.log('\n🔐 Second login attempt (should reuse token)...');
    const login2 = await axios.post(`${serverUrl}/api/auth/login`, credentials, { timeout: 5000 });
    const token2 = login2.data.token;
    console.log(`✅ Second login successful`);
    console.log(`   🔑 Token 2: ${token2.substring(0, 30)}...`);

    // Compare tokens
    if (token1 === token2) {
      console.log(`✅ ${serverName} server: TOKEN REUSE WORKING! Same token returned.`);
    } else {
      console.log(`❌ ${serverName} server: TOKEN REUSE FAILED! Different tokens returned.`);
    }

    // Third login (should still reuse)
    console.log('\n🔐 Third login attempt (should still reuse token)...');
    const login3 = await axios.post(`${serverUrl}/api/auth/login`, credentials, { timeout: 5000 });
    const token3 = login3.data.token;
    console.log(`✅ Third login successful`);
    console.log(`   🔑 Token 3: ${token3.substring(0, 30)}...`);

    if (token1 === token3) {
      console.log(`✅ ${serverName} server: CONSISTENT TOKEN REUSE! All tokens match.`);
    } else {
      console.log(`❌ ${serverName} server: INCONSISTENT TOKEN REUSE! Tokens don't match.`);
    }

    // Test token validation
    console.log('\n🔍 Testing token validation...');
    const verifyResponse = await axios.get(`${serverUrl}/api/auth/verify`, {
      headers: { Authorization: `Bearer ${token1}` },
      timeout: 5000
    });

    if (verifyResponse.data.valid) {
      console.log(`✅ ${serverName} server: Token validation successful`);
      console.log(`   👤 User: ${verifyResponse.data.user.email}`);
    } else {
      console.log(`❌ ${serverName} server: Token validation failed`);
    }

    // Test logout
    console.log('\n🚪 Testing logout...');
    await axios.post(`${serverUrl}/api/auth/logout`, {}, {
      headers: { Authorization: `Bearer ${token1}` },
      timeout: 5000
    });
    console.log(`✅ ${serverName} server: Logout successful`);

    // Test login after logout (should create new token)
    console.log('\n🔐 Login after logout (should create new token)...');
    const login4 = await axios.post(`${serverUrl}/api/auth/login`, credentials, { timeout: 5000 });
    const token4 = login4.data.token;
    console.log(`✅ Post-logout login successful`);
    console.log(`   🔑 Token 4: ${token4.substring(0, 30)}...`);

    if (token1 !== token4) {
      console.log(`✅ ${serverName} server: NEW TOKEN AFTER LOGOUT! Correct behavior.`);
    } else {
      console.log(`⚠️ ${serverName} server: SAME TOKEN AFTER LOGOUT! May indicate issue.`);
    }

  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log(`❌ ${serverName} server not running on ${serverUrl}`);
    } else {
      console.log(`❌ ${serverName} server test failed: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }
  }

  console.log(''); // Empty line for spacing
}

async function testCrossServerTokens() {
  console.log('🔄 TESTING CROSS-SERVER TOKEN BEHAVIOR');
  console.log('=' .repeat(50));

  const desktopServerUrl = 'http://localhost:5000';
  const webServerUrl = 'http://localhost:5001';
  const credentials = {
    email: '<EMAIL>',
    password: 'password123'
  };

  try {
    // Login to desktop server
    console.log('🖥️ Login to desktop server...');
    const desktopLogin = await axios.post(`${desktopServerUrl}/api/auth/login`, credentials, { timeout: 5000 });
    const desktopToken = desktopLogin.data.token;
    console.log(`✅ Desktop login successful`);
    console.log(`   🔑 Desktop token: ${desktopToken.substring(0, 30)}...`);

    // Login to web server
    console.log('\n🌐 Login to web server...');
    const webLogin = await axios.post(`${webServerUrl}/api/auth/login`, credentials, { timeout: 5000 });
    const webToken = webLogin.data.token;
    console.log(`✅ Web login successful`);
    console.log(`   🔑 Web token: ${webToken.substring(0, 30)}...`);

    // Compare tokens
    if (desktopToken === webToken) {
      console.log(`✅ CROSS-SERVER TOKEN CONSISTENCY! Same token used across servers.`);
    } else {
      console.log(`⚠️ DIFFERENT TOKENS ACROSS SERVERS! This is expected if using separate databases.`);
    }

    // Test desktop token on web server
    console.log('\n🔄 Testing desktop token on web server...');
    try {
      const crossVerify1 = await axios.get(`${webServerUrl}/api/auth/verify`, {
        headers: { Authorization: `Bearer ${desktopToken}` },
        timeout: 5000
      });
      
      if (crossVerify1.data.valid) {
        console.log(`✅ Desktop token works on web server! Shared session store.`);
      } else {
        console.log(`❌ Desktop token invalid on web server.`);
      }
    } catch (error) {
      console.log(`❌ Desktop token rejected by web server: ${error.response?.status}`);
    }

    // Test web token on desktop server
    console.log('\n🔄 Testing web token on desktop server...');
    try {
      const crossVerify2 = await axios.get(`${desktopServerUrl}/api/auth/verify`, {
        headers: { Authorization: `Bearer ${webToken}` },
        timeout: 5000
      });
      
      if (crossVerify2.data.valid) {
        console.log(`✅ Web token works on desktop server! Shared session store.`);
      } else {
        console.log(`❌ Web token invalid on desktop server.`);
      }
    } catch (error) {
      console.log(`❌ Web token rejected by desktop server: ${error.response?.status}`);
    }

  } catch (error) {
    console.log(`❌ Cross-server test failed: ${error.message}`);
  }
}

async function analyzeBehavior() {
  console.log('\n📊 TOKEN BEHAVIOR ANALYSIS');
  console.log('=' .repeat(50));
  
  console.log('✅ EXPECTED BEHAVIOR:');
  console.log('   🔄 Same user, same server: Reuse existing valid token');
  console.log('   🆕 No valid session: Create new token');
  console.log('   🚪 After logout: Create new token on next login');
  console.log('   🗑️ Expired sessions: Automatically cleaned up');
  console.log('   📊 Session limit: Maximum 10 sessions per user');
  console.log('');
  console.log('✅ BENEFITS OF TOKEN REUSE:');
  console.log('   - Prevents database bloat with duplicate tokens');
  console.log('   - Consistent authentication across app restarts');
  console.log('   - Better performance (fewer database writes)');
  console.log('   - Cleaner session management');
  console.log('');
  console.log('⚠️ IMPORTANT NOTES:');
  console.log('   - Desktop and web may use different databases');
  console.log('   - Cross-server token validation depends on shared database');
  console.log('   - Token reuse only works within same server instance');
  console.log('   - Logout invalidates the session, requiring new token');
}

// Run tests
testTokenReuse()
  .then(() => {
    analyzeBehavior();
    console.log('\n🎉 TOKEN REUSE TESTING COMPLETED!');
    console.log('💡 Check the results above to verify token reuse behavior');
  })
  .catch(error => {
    console.error('❌ Test failed:', error);
  });
