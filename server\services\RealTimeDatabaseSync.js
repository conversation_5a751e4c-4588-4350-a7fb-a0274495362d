const EventEmitter = require('events');
const { debounce } = require('lodash');

class RealTimeDatabaseSync extends EventEmitter {
  constructor(databaseSyncService) {
    super();
    this.databaseSyncService = databaseSyncService;
    this.isEnabled = false;
    this.pendingChanges = new Set();
    this.syncTimeout = null;
    
    // Debounced sync function - wait 2 seconds after last change
    this.debouncedSync = debounce(this.performRealTimeSync.bind(this), 2000);
    
    this.config = {
      enabled: false,
      debounceMs: 2000,
      maxBatchSize: 50,
      syncTables: ['users', 'sync_tasks', 'sync_history', 'desktop_clients', 'sessions'],
      excludeEvents: ['sync-completed'] // Prevent infinite loops
    };
  }

  enable() {
    if (this.isEnabled) {
      console.log('⚠️ Real-time database sync already enabled');
      return;
    }

    this.isEnabled = true;
    this.config.enabled = true;
    
    console.log('🎧 Real-time database sync enabled');
    console.log(`⏱️ Debounce delay: ${this.config.debounceMs}ms`);
    console.log(`📋 Monitored tables: ${this.config.syncTables.join(', ')}`);
    
    this.emit('enabled');
  }

  disable() {
    if (!this.isEnabled) {
      console.log('⚠️ Real-time database sync already disabled');
      return;
    }

    this.isEnabled = false;
    this.config.enabled = false;
    this.pendingChanges.clear();
    
    if (this.syncTimeout) {
      clearTimeout(this.syncTimeout);
      this.syncTimeout = null;
    }
    
    console.log('🔇 Real-time database sync disabled');
    this.emit('disabled');
  }

  // Handle database change events
  handleDatabaseChange(changeType, data) {
    if (!this.isEnabled) return;

    // Prevent infinite loops from sync completion events
    if (this.config.excludeEvents.includes(changeType)) {
      console.log(`🔄 Ignoring ${changeType} event to prevent infinite loops`);
      return;
    }

    // Check if this is a table we care about
    const table = data?.table;
    if (table && !this.config.syncTables.includes(table)) {
      console.log(`⏭️ Skipping real-time sync for table: ${table} (not in sync list)`);
      return;
    }

    console.log(`📝 Real-time change detected: ${changeType}`, {
      table: data?.table,
      id: data?.id,
      timestamp: new Date().toISOString()
    });

    // Add to pending changes
    if (table) {
      this.pendingChanges.add(table);
    }

    // Trigger debounced sync
    this.debouncedSync();
  }

  // Perform real-time sync for changed tables only
  async performRealTimeSync() {
    if (!this.isEnabled || this.pendingChanges.size === 0) {
      return;
    }

    const changesToSync = Array.from(this.pendingChanges);
    this.pendingChanges.clear();

    console.log(`🔄 Processing ${changesToSync.length} database changes...`);
    console.log(`🎯 Syncing affected tables: ${changesToSync.join(', ')}`);

    try {
      const startTime = Date.now();
      
      // Perform targeted sync for only the changed tables
      const syncResult = await this.databaseSyncService.syncSpecificTables(changesToSync);
      
      const duration = Date.now() - startTime;
      
      console.log(`✅ Real-time database sync completed in ${duration}ms`);
      console.log(`📊 Sync results:`, syncResult);
      
      this.emit('syncCompleted', {
        type: 'real-time',
        tables: changesToSync,
        duration,
        result: syncResult
      });

    } catch (error) {
      console.error('❌ Real-time database sync failed:', error);
      
      this.emit('syncFailed', {
        type: 'real-time',
        tables: changesToSync,
        error: error.message
      });

      // Retry after a delay
      setTimeout(() => {
        if (this.isEnabled && changesToSync.length > 0) {
          console.log('🔄 Retrying real-time sync...');
          changesToSync.forEach(table => this.pendingChanges.add(table));
          this.debouncedSync();
        }
      }, 5000);
    }
  }

  // Get current status
  getStatus() {
    return {
      enabled: this.isEnabled,
      pendingChanges: Array.from(this.pendingChanges),
      config: this.config,
      stats: {
        totalChanges: this.listenerCount('syncCompleted'),
        failedSyncs: this.listenerCount('syncFailed')
      }
    };
  }

  // Update configuration
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    
    // Recreate debounced function if delay changed
    if (newConfig.debounceMs) {
      this.debouncedSync = debounce(this.performRealTimeSync.bind(this), newConfig.debounceMs);
    }
    
    console.log('⚙️ Real-time sync config updated:', this.config);
    this.emit('configUpdated', this.config);
  }
}

module.exports = RealTimeDatabaseSync;
