const axios = require('axios');
const path = require('path');
const fs = require('fs');

async function checkDatabaseInfo() {
  console.log('🔍 Checking Database Information...');

  try {
    // Check environment variables
    console.log('\n📋 Environment Configuration:');
    console.log('- NODE_ENV:', process.env.NODE_ENV || 'not set');
    console.log('- DB_TYPE:', process.env.DB_TYPE || 'not set (defaults to sqlite)');
    console.log('- DB_HOST:', process.env.DB_HOST || 'not set');
    console.log('- DB_NAME:', process.env.DB_NAME || 'not set');

    // Check SQLite database file
    console.log('\n📁 SQLite Database File:');
    const dbPath = path.join(__dirname, '../data/syncmasterpro.db');
    console.log('- Database path:', dbPath);
    
    try {
      const stats = fs.statSync(dbPath);
      console.log('- File exists: ✅ Yes');
      console.log('- File size:', (stats.size / 1024).toFixed(2), 'KB');
      console.log('- Last modified:', stats.mtime.toISOString());
    } catch (error) {
      console.log('- File exists: ❌ No');
    }

    // Check server API
    console.log('\n🌐 Server API Check:');
    const baseURL = 'http://localhost:5000/api';
    
    try {
      const healthResponse = await axios.get(`${baseURL}/health`);
      console.log('- Server status: ✅ Running');
      console.log('- Server response:', healthResponse.data);
    } catch (error) {
      console.log('- Server status: ❌ Not responding');
      console.log('- Error:', error.message);
      return;
    }

    // Login and check database content
    console.log('\n🔐 Database Content Check:');
    try {
      const loginResponse = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      const token = loginResponse.data.token;
      const headers = { 'Authorization': `Bearer ${token}` };
      
      console.log('- Login: ✅ Success');

      // Get sync tasks
      const tasksResponse = await axios.get(`${baseURL}/sync/tasks`, { headers });
      console.log('- Total sync tasks:', tasksResponse.data.tasks.length);
      
      if (tasksResponse.data.tasks.length > 0) {
        console.log('- Sample task:', {
          id: tasksResponse.data.tasks[0].id,
          name: tasksResponse.data.tasks[0].name,
          status: tasksResponse.data.tasks[0].status,
          createdAt: tasksResponse.data.tasks[0].createdAt
        });
      }

      // Get sync history
      const historyResponse = await axios.get(`${baseURL}/sync/history`, { headers });
      console.log('- Total sync history entries:', historyResponse.data.history.length);

    } catch (error) {
      console.log('- Database access: ❌ Failed');
      console.log('- Error:', error.message);
    }

    // Check web vs desktop access
    console.log('\n🖥️ Web vs Desktop Database Access:');
    console.log('📊 Current Configuration:');
    console.log('- Both web and desktop use the SAME server (localhost:5000)');
    console.log('- Both use the SAME SQLite database file');
    console.log('- Database location: D:\\Project\\SyncMasterPro\\data\\syncmasterpro.db');
    console.log('- This means web and desktop share the same data');

    console.log('\n🔍 How to verify:');
    console.log('1. Create a sync task in web browser');
    console.log('2. Check if it appears in desktop app');
    console.log('3. Create a sync task in desktop app');
    console.log('4. Check if it appears in web browser');
    console.log('5. Both should show the same tasks');

    console.log('\n⚙️ To use different databases:');
    console.log('- Web: Set DB_TYPE=postgresql in .env');
    console.log('- Desktop: Keep DB_TYPE=sqlite (default)');
    console.log('- Or run separate server instances with different configs');

  } catch (error) {
    console.log('\n❌ Error occurred:');
    console.log('Error type:', error.constructor.name);
    console.log('Error message:', error.message);
  }
}

checkDatabaseInfo();
