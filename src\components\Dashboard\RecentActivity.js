import React from 'react';
import { useSync } from '../../contexts/SyncContext';
import { useLanguage } from '../../contexts/LanguageContext';

const RecentActivity = () => {
  const { syncHistory } = useSync();
  const { t } = useLanguage();

  // Get recent activities (last 8 for dashboard)
  const recentActivities = syncHistory.slice(0, 8);

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircleIcon className="w-5 h-5 text-red-500" />;
      case 'running':
        return <ClockIcon className="w-5 h-5 text-blue-500 animate-spin" />;
      default:
        return <InformationCircleIcon className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (activity) => {
    switch (activity.status) {
      case 'completed':
        return `Synced ${activity.filesProcessed || 0} files`;
      case 'error':
        return `${t('failed')}: ${activity.error || 'Unknown error'}`;
      case 'running':
        return `${t('syncing')}...`;
      default:
        return 'Unknown status';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t('recentActivity')}</h2>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {syncHistory.length} total activities
          </span>
        </div>
      </div>
      
      <div className="p-6">
        {recentActivities.length === 0 ? (
          <div className="text-center py-8">
            <ClockIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">No recent activity</p>
            <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
              Sync activities will appear here
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  {getStatusIcon(activity.status)}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {t('task')} #{activity.taskId}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {formatTime(activity.timestamp)}
                    </p>
                  </div>

                  <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {getStatusText(activity)}
                  </p>

                  {activity.duration && (
                    <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                      {t('duration')}: {Math.round(activity.duration / 1000)}s
                    </p>
                  )}
                </div>
              </div>
            ))}

            {syncHistory.length > 8 && (
              <div className="pt-4 border-t border-gray-100 dark:border-gray-700 text-center">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Showing {recentActivities.length} of {syncHistory.length} recent activities
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Icon components
const CheckCircleIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const XCircleIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
);

const ClockIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
);

const InformationCircleIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
);

export default RecentActivity;
