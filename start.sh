#!/bin/bash

echo "========================================"
echo "    SyncMasterPro - Quick Start"
echo "========================================"
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    echo
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "ERROR: npm is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    echo
    exit 1
fi

echo "Node.js and npm are installed ✓"
echo

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    echo "This may take a few minutes..."
    echo
    npm install
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to install dependencies"
        echo
        exit 1
    fi
    echo "Dependencies installed ✓"
    echo
fi

# Run setup if needed
if [ ! -f ".env" ]; then
    echo "Running initial setup..."
    npm run setup
    if [ $? -ne 0 ]; then
        echo "ERROR: Setup failed"
        echo
        exit 1
    fi
    echo "Setup completed ✓"
    echo
fi

echo "Starting SyncMasterPro..."
echo
echo "The application will open in a few moments."
echo "Press Ctrl+C to stop the application."
echo

# Start the application
npm start
