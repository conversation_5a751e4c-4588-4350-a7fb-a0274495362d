#!/usr/bin/env node

/**
 * Test the checksum fix for data type differences
 */

const axios = require('axios');

console.log('🔧 Testing Checksum Fix for Data Type Differences');
console.log('================================================');

async function login() {
  console.log('\n1. 🔐 Logging in...');
  
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Login successful');
      return response.data.token;
    } else {
      console.log('❌ Login failed:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return null;
  }
}

async function testDataComparison(token) {
  console.log('\n2. 🔍 Testing Data Comparison with Fixed Checksums...');
  
  try {
    const response = await axios.get('http://localhost:5001/api/database-sync/compare', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Data comparison completed');
      const comparison = response.data.comparison;
      
      console.log('📊 Comparison Results:');
      console.log(`   Overall Consistent: ${comparison.isConsistent ? '✅' : '❌'}`);
      console.log(`   Timestamp: ${comparison.timestamp}`);
      
      let allTablesConsistent = true;
      
      Object.entries(comparison.tables).forEach(([table, data]) => {
        if (data.error) {
          console.log(`   ${table}: ❌ Error - ${data.error}`);
          allTablesConsistent = false;
        } else {
          const status = data.isConsistent ? '✅' : '❌';
          console.log(`   ${table}: SQLite(${data.sqliteCount}) vs PostgreSQL(${data.pgCount}) - ${status}`);
          
          if (!data.isConsistent) {
            allTablesConsistent = false;
            console.log(`      SQLite checksum: ${data.sqliteChecksum.substring(0, 12)}...`);
            console.log(`      PostgreSQL checksum: ${data.pgChecksum.substring(0, 12)}...`);
          }
        }
      });
      
      if (comparison.differences.length > 0) {
        console.log('\n⚠️ Differences found:');
        comparison.differences.forEach(diff => {
          console.log(`   - ${diff.table}: ${diff.issue}`);
        });
      }
      
      return {
        isConsistent: comparison.isConsistent,
        allTablesConsistent,
        differences: comparison.differences
      };
    } else {
      console.log('❌ Data comparison failed:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ Data comparison error:', error.message);
    return null;
  }
}

async function performSyncAndCheck(token) {
  console.log('\n3. 🔄 Performing Sync and Re-checking...');
  
  try {
    // Perform sync
    console.log('   Performing bidirectional sync...');
    const syncResponse = await axios.post('http://localhost:5001/api/database-sync/sync', {
      direction: 'bidirectional'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000,
      validateStatus: () => true
    });
    
    if (syncResponse.status === 200) {
      const syncResult = syncResponse.data.results;
      console.log(`   ✅ Sync completed in ${syncResult.duration}ms`);
      
      // Check if sync was skipped
      if (syncResult.skipped) {
        console.log(`   🎯 Smart sync: ${syncResult.reason}`);
      }
      
      // Check consistency after sync
      console.log('   Checking consistency after sync...');
      const postSyncComparison = await testDataComparison(token);
      
      return {
        syncCompleted: true,
        syncSkipped: syncResult.skipped,
        postSyncConsistent: postSyncComparison ? postSyncComparison.isConsistent : false
      };
    } else {
      console.log('❌ Sync failed:', syncResponse.status);
      return { syncCompleted: false };
    }
  } catch (error) {
    console.log('❌ Sync error:', error.message);
    return { syncCompleted: false };
  }
}

async function testMultipleComparisons(token) {
  console.log('\n4. 🔄 Testing Multiple Comparisons for Consistency...');
  
  const results = [];
  
  for (let i = 1; i <= 3; i++) {
    console.log(`   Comparison ${i}/3...`);
    const comparison = await testDataComparison(token);
    
    if (comparison) {
      results.push({
        attempt: i,
        isConsistent: comparison.isConsistent,
        differences: comparison.differences.length
      });
    }
    
    // Wait 2 seconds between comparisons
    if (i < 3) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log('\n📊 Multiple Comparison Results:');
  results.forEach(result => {
    console.log(`   Attempt ${result.attempt}: ${result.isConsistent ? '✅' : '❌'} (${result.differences} differences)`);
  });
  
  const allConsistent = results.every(r => r.isConsistent);
  console.log(`   Overall: ${allConsistent ? '✅ All consistent' : '❌ Some inconsistent'}`);
  
  return allConsistent;
}

async function main() {
  try {
    console.log('🚀 Starting checksum fix testing...\n');
    
    // Login
    const token = await login();
    if (!token) {
      console.log('❌ Cannot proceed without authentication');
      return;
    }
    
    // Test initial data comparison
    const initialComparison = await testDataComparison(token);
    
    // Perform sync and check again
    const syncResult = await performSyncAndCheck(token);
    
    // Test multiple comparisons for consistency
    const multipleConsistent = await testMultipleComparisons(token);
    
    console.log('\n📊 CHECKSUM FIX TEST RESULTS:');
    console.log('=============================');
    
    if (initialComparison) {
      console.log(`Initial Comparison: ${initialComparison.isConsistent ? '✅ Consistent' : '❌ Inconsistent'}`);
      if (initialComparison.differences.length > 0) {
        console.log(`   Differences: ${initialComparison.differences.length}`);
      }
    }
    
    if (syncResult.syncCompleted) {
      console.log(`Sync Completed: ✅`);
      console.log(`Smart Sync Skipped: ${syncResult.syncSkipped ? '✅' : '❌'}`);
      console.log(`Post-Sync Consistent: ${syncResult.postSyncConsistent ? '✅' : '❌'}`);
    }
    
    console.log(`Multiple Comparisons: ${multipleConsistent ? '✅ All consistent' : '❌ Some inconsistent'}`);
    
    console.log('\n🎯 CHECKSUM FIX ASSESSMENT:');
    
    if (multipleConsistent && syncResult.postSyncConsistent) {
      console.log('✅ CHECKSUM FIX SUCCESSFUL!');
      console.log('   - Data type normalization working');
      console.log('   - Consistent checksums across comparisons');
      console.log('   - Smart sync can now work properly');
      console.log('   - Database consistency verified');
    } else if (initialComparison && !initialComparison.isConsistent && syncResult.postSyncConsistent) {
      console.log('⚠️ PARTIAL SUCCESS');
      console.log('   - Sync fixes inconsistencies');
      console.log('   - But checksums may still have minor differences');
      console.log('   - System is functional but may need fine-tuning');
    } else {
      console.log('❌ CHECKSUM FIX NEEDS MORE WORK');
      console.log('   - Data type differences still causing issues');
      console.log('   - May need additional normalization logic');
      console.log('   - Check server logs for debug information');
    }
    
    console.log('\n💡 Next Steps:');
    if (multipleConsistent) {
      console.log('   - Checksum fix is working correctly');
      console.log('   - Smart sync will now skip unnecessary syncs');
      console.log('   - System will maintain data consistency automatically');
    } else {
      console.log('   - Check server logs for debug output');
      console.log('   - Look for data type normalization issues');
      console.log('   - May need to add more field-specific normalization');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
