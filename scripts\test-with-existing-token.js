#!/usr/bin/env node

/**
 * Test client registration with existing token from desktop app
 */

const axios = require('axios');

console.log('🧪 Testing with Existing Desktop Token');
console.log('======================================');

async function getCurrentUserFromDesktop() {
  console.log('\n1. 🔍 Getting current user from desktop server...');
  
  try {
    // Try to get current user info (this should work since user is logged in)
    const response = await axios.get('http://localhost:5002/api/auth/verify', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📝 Desktop Auth Verify Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Desktop auth verify successful!');
      console.log('👤 Current User:', response.data.user.name);
      return response.data;
    } else {
      console.log('❌ Desktop auth verify failed:', response.data);
    }
  } catch (error) {
    console.log('❌ Desktop auth verify error:', error.message);
  }
  
  return null;
}

async function testClientRegistrationDirect() {
  console.log('\n2. 📡 Testing direct client registration...');
  
  const mockClient = {
    clientId: 'test-client-' + Date.now(),
    hostname: 'TEST-DESKTOP',
    platform: 'win32',
    version: '1.0.0',
    userId: 1  // Assume user ID 1
  };
  
  try {
    const response = await axios.post('http://localhost:5001/api/clients/register', mockClient, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Registration Status:', response.status);
    console.log('📝 Registration Response:', response.data);
    
    if (response.status === 200 || response.status === 201) {
      console.log('✅ Client registration successful without auth!');
      return true;
    } else {
      console.log('❌ Client registration failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Registration error:', error.message);
    return false;
  }
}

async function checkClientRegistrationEndpoint() {
  console.log('\n3. 🔍 Checking client registration endpoint...');
  
  try {
    // Check if endpoint exists
    const response = await axios.get('http://localhost:5001/api/clients', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📝 Clients API Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Clients API accessible!');
      console.log('📊 Current Clients:', response.data);
      return true;
    } else {
      console.log('❌ Clients API failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Clients API error:', error.message);
    return false;
  }
}

async function testWithMockToken() {
  console.log('\n4. 🔑 Testing with mock token...');
  
  // Create a simple mock token (this won't work but will show the error)
  const mockToken = 'Bearer mock-token-for-testing';
  
  const mockClient = {
    clientId: 'test-client-' + Date.now(),
    hostname: 'TEST-DESKTOP',
    platform: 'win32',
    version: '1.0.0',
    userId: 1
  };
  
  try {
    const response = await axios.post('http://localhost:5001/api/clients/register', mockClient, {
      headers: {
        'Authorization': mockToken,
        'Content-Type': 'application/json'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Mock Token Registration Status:', response.status);
    console.log('📝 Mock Token Registration Response:', response.data);
    
    return response.status;
  } catch (error) {
    console.log('❌ Mock token registration error:', error.message);
    return null;
  }
}

async function main() {
  try {
    console.log('🚀 Starting token test...\n');
    
    // Check current desktop user
    const currentUser = await getCurrentUserFromDesktop();
    
    // Test direct registration (no auth)
    const directSuccess = await testClientRegistrationDirect();
    
    // Check clients endpoint
    const clientsApiWorking = await checkClientRegistrationEndpoint();
    
    // Test with mock token
    const mockTokenStatus = await testWithMockToken();
    
    console.log('\n📊 Test Summary:');
    console.log('================');
    console.log('- Desktop User Check:', currentUser ? '✅ Success' : '❌ Failed');
    console.log('- Direct Registration:', directSuccess ? '✅ Success' : '❌ Failed');
    console.log('- Clients API:', clientsApiWorking ? '✅ Working' : '❌ Failed');
    console.log('- Mock Token Status:', mockTokenStatus || 'Error');
    
    if (directSuccess) {
      console.log('\n🎉 SUCCESS! Client registration works without auth!');
      console.log('🔍 The issue is authentication middleware');
      console.log('💡 Check if client registration endpoint should require auth');
    } else if (clientsApiWorking) {
      console.log('\n⚠️ Clients API works but registration fails');
      console.log('💡 Check client registration implementation');
    } else {
      console.log('\n❌ Web server has issues with client APIs');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
