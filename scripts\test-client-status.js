const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

async function testClientStatus() {
  console.log('🧪 Testing Client Status Logic\n');

  try {
    // Find SQLite database file
    const possiblePaths = [
      path.join(__dirname, '..', 'data', 'syncmasterpro.db'),
      path.join(__dirname, '..', 'database', 'syncmaster.db'),
      path.join(__dirname, '..', 'server', 'database', 'syncmaster.db'),
      path.join(__dirname, '..', 'syncmaster.db')
    ];

    let dbPath = null;
    for (const testPath of possiblePaths) {
      if (fs.existsSync(testPath)) {
        dbPath = testPath;
        break;
      }
    }

    if (!dbPath) {
      console.log('❌ SQLite database file not found');
      return;
    }

    console.log('📁 Database path:', dbPath);

    // Connect to SQLite database
    const db = new Database(dbPath);
    console.log('✅ Connected to SQLite database');

    // Check desktop_clients table
    console.log('\n📊 CURRENT CLIENT STATUS:');
    console.log('=' .repeat(60));

    const clients = db.prepare(`
      SELECT 
        client_id,
        user_id,
        hostname,
        status,
        last_seen,
        created_at,
        metadata
      FROM desktop_clients 
      ORDER BY last_seen DESC
    `).all();

    if (clients.length === 0) {
      console.log('📭 No clients found in database');
      console.log('💡 Login to desktop app first to create client record');
    } else {
      clients.forEach((client, index) => {
        console.log(`${index + 1}. CLIENT: ${client.client_id}`);
        console.log(`   👤 User ID: ${client.user_id}`);
        console.log(`   🏠 Hostname: ${client.hostname}`);
        console.log(`   📊 Status: ${client.status === 'online' ? '🟢 ONLINE' : '🔴 OFFLINE'}`);
        console.log(`   👁️ Last Seen: ${client.last_seen}`);
        console.log(`   📅 Created: ${client.created_at}`);
        
        // Parse metadata if available
        try {
          const metadata = JSON.parse(client.metadata || '{}');
          if (metadata.previousStatus) {
            console.log(`   🔄 Previous Status: ${metadata.previousStatus}`);
          }
          if (metadata.lastUpdate) {
            console.log(`   🕐 Last Update: ${metadata.lastUpdate}`);
          }
        } catch (e) {
          // Ignore metadata parsing errors
        }
        console.log('');
      });
    }

    // Check sessions table
    console.log('\n🔐 CURRENT SESSIONS:');
    console.log('=' .repeat(60));

    const sessions = db.prepare(`
      SELECT 
        s.user_id,
        u.email,
        COUNT(*) as session_count,
        COUNT(CASE WHEN s.expires_at > datetime('now') THEN 1 END) as active_count
      FROM sessions s
      LEFT JOIN users u ON s.user_id = u.id
      GROUP BY s.user_id, u.email
      ORDER BY s.user_id
    `).all();

    if (sessions.length === 0) {
      console.log('📭 No sessions found');
    } else {
      sessions.forEach(session => {
        console.log(`👤 ${session.email || 'Unknown'} (ID: ${session.user_id})`);
        console.log(`   📋 Total sessions: ${session.session_count}`);
        console.log(`   🟢 Active sessions: ${session.active_count}`);
        console.log('');
      });
    }

    // Test scenarios
    console.log('\n🧪 TEST SCENARIOS:');
    console.log('=' .repeat(60));
    
    console.log('✅ EXPECTED BEHAVIOR:');
    console.log('1. 🔐 Login → Client status = "online"');
    console.log('2. 🚪 Logout → Client status = "offline"');
    console.log('3. 🔐 Login again → Client status = "online" (SAME client_id)');
    console.log('');
    
    console.log('🔍 DEBUGGING STEPS:');
    console.log('1. Check client registration logs in server console');
    console.log('2. Verify socket connection logs');
    console.log('3. Check database update results');
    console.log('4. Monitor real-time sync notifications');
    console.log('');
    
    console.log('📋 MANUAL TEST:');
    console.log('1. Start desktop server: npm run start-desktop');
    console.log('2. Start web server: npm run start-web-management');
    console.log('3. Login to desktop app');
    console.log('4. Check web-ui → Client Management (should show online)');
    console.log('5. Logout from desktop app');
    console.log('6. Check web-ui → Client Management (should show offline)');
    console.log('7. Login again to desktop app');
    console.log('8. Check web-ui → Client Management (should show online again)');
    console.log('');
    
    console.log('🔧 TROUBLESHOOTING:');
    console.log('- If status stuck on offline → Check client registration logic');
    console.log('- If multiple clients created → Check client ID persistence');
    console.log('- If no status updates → Check socket connection');
    console.log('- If web not updating → Check real-time sync');

    // Close database
    db.close();
    console.log('\n✅ Test completed');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run test
testClientStatus();
