const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

async function setupAdminer() {
  console.log('🗄️ Setting up Adminer for SyncMasterPro\n');

  try {
    // 1. Find SQLite database files
    console.log('1. 🔍 Searching for SQLite database files...');
    
    const searchPaths = [
      './database',
      './data', 
      './server/database',
      './src/database',
      '.'
    ];

    const dbFiles = [];
    
    for (const searchPath of searchPaths) {
      if (fs.existsSync(searchPath)) {
        const files = fs.readdirSync(searchPath, { withFileTypes: true });
        files.forEach(file => {
          if (file.isFile() && file.name.endsWith('.db')) {
            const fullPath = path.join(searchPath, file.name);
            const stats = fs.statSync(fullPath);
            dbFiles.push({
              name: file.name,
              path: fullPath,
              size: stats.size,
              modified: stats.mtime
            });
          }
        });
      }
    }

    if (dbFiles.length === 0) {
      console.log('❌ No SQLite database files found');
      console.log('💡 Database files will be created when you start the servers');
      console.log('   Run: npm run start-desktop');
    } else {
      console.log(`✅ Found ${dbFiles.length} SQLite database file(s):`);
      dbFiles.forEach((db, index) => {
        console.log(`   ${index + 1}. ${db.name}`);
        console.log(`      📁 Path: ${db.path}`);
        console.log(`      📊 Size: ${db.size} bytes`);
        console.log(`      📅 Modified: ${db.modified.toLocaleString()}`);
        console.log('');
      });
    }

    // 2. Create tools directory
    console.log('2. 📁 Creating tools directory...');
    const toolsDir = './tools';
    if (!fs.existsSync(toolsDir)) {
      fs.mkdirSync(toolsDir);
      console.log('✅ Tools directory created');
    } else {
      console.log('✅ Tools directory already exists');
    }

    // 3. Check if Adminer exists
    const adminerPath = path.join(toolsDir, 'adminer.php');
    console.log('\n3. 🌐 Checking Adminer...');
    
    if (!fs.existsSync(adminerPath)) {
      console.log('📥 Downloading Adminer...');
      try {
        // Download Adminer using curl or wget
        try {
          execSync(`curl -L https://www.adminer.org/latest.php -o ${adminerPath}`, { stdio: 'inherit' });
          console.log('✅ Adminer downloaded successfully');
        } catch (curlError) {
          try {
            execSync(`wget https://www.adminer.org/latest.php -O ${adminerPath}`, { stdio: 'inherit' });
            console.log('✅ Adminer downloaded successfully');
          } catch (wgetError) {
            console.log('❌ Failed to download Adminer automatically');
            console.log('💡 Please download manually from: https://www.adminer.org/latest.php');
            console.log(`   Save as: ${adminerPath}`);
          }
        }
      } catch (error) {
        console.log('❌ Download failed:', error.message);
      }
    } else {
      console.log('✅ Adminer already exists');
    }

    // 4. Create start script
    console.log('\n4. 📝 Creating start script...');
    
    const startScript = `#!/bin/bash
echo "🚀 Starting Adminer for SyncMasterPro..."
echo "📁 Database files location: $(pwd)"
echo "🌐 Adminer will be available at: http://localhost:8080/adminer.php"
echo ""
echo "📋 SQLite Connection Info:"
echo "   System: SQLite 3"
echo "   Database: Select your .db file from the project directory"
echo "   Username: (leave empty)"
echo "   Password: (leave empty)"
echo ""
echo "🔍 Available database files:"
find . -name "*.db" -type f 2>/dev/null | head -10
echo ""
echo "⚡ Starting PHP server..."
cd tools && php -S localhost:8080
`;

    fs.writeFileSync('./start-adminer.sh', startScript);
    
    // Make executable on Unix systems
    try {
      execSync('chmod +x start-adminer.sh');
    } catch (error) {
      // Ignore on Windows
    }

    // Create Windows batch file
    const batchScript = `@echo off
echo 🚀 Starting Adminer for SyncMasterPro...
echo 📁 Database files location: %cd%
echo 🌐 Adminer will be available at: http://localhost:8080/adminer.php
echo.
echo 📋 SQLite Connection Info:
echo    System: SQLite 3
echo    Database: Select your .db file from the project directory
echo    Username: (leave empty)
echo    Password: (leave empty)
echo.
echo 🔍 Available database files:
dir /s /b *.db 2>nul
echo.
echo ⚡ Starting PHP server...
cd tools && php -S localhost:8080
pause
`;

    fs.writeFileSync('./start-adminer.bat', batchScript);
    console.log('✅ Start scripts created');

    // 5. Create package.json script
    console.log('\n5. 📦 Adding npm script...');
    
    try {
      const packageJsonPath = './package.json';
      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        
        if (!packageJson.scripts) {
          packageJson.scripts = {};
        }
        
        packageJson.scripts['adminer'] = 'cd tools && php -S localhost:8080';
        
        fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
        console.log('✅ Added "npm run adminer" script');
      }
    } catch (error) {
      console.log('⚠️ Could not update package.json:', error.message);
    }

    // 6. Instructions
    console.log('\n🎉 ADMINER SETUP COMPLETED!\n');
    
    console.log('📋 HOW TO USE:');
    console.log('=' .repeat(50));
    
    console.log('\n🚀 START ADMINER:');
    console.log('   Option 1: npm run adminer');
    console.log('   Option 2: ./start-adminer.sh (Linux/Mac)');
    console.log('   Option 3: start-adminer.bat (Windows)');
    console.log('   Option 4: cd tools && php -S localhost:8080');
    
    console.log('\n🌐 ACCESS ADMINER:');
    console.log('   URL: http://localhost:8080/adminer.php');
    
    console.log('\n🔐 SQLITE CONNECTION:');
    console.log('   System: SQLite 3');
    console.log('   Database: Browse and select your .db file');
    console.log('   Username: (leave empty)');
    console.log('   Password: (leave empty)');
    
    if (dbFiles.length > 0) {
      console.log('\n📁 YOUR DATABASE FILES:');
      dbFiles.forEach(db => {
        console.log(`   - ${db.path}`);
      });
    }
    
    console.log('\n💡 TIPS:');
    console.log('   - Start your servers first to create database files');
    console.log('   - Use Adminer to view tables: users, sessions, desktop_clients, sync_tasks');
    console.log('   - You can edit data directly in Adminer');
    console.log('   - Export/Import database using Adminer tools');

  } catch (error) {
    console.error('❌ Setup failed:', error);
  }
}

// Run setup
setupAdminer();
