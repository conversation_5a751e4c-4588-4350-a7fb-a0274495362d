#!/usr/bin/env node

/**
 * Test database sync behavior when data is deleted
 */

const axios = require('axios');
const Database = require('better-sqlite3');
const { Pool } = require('pg');
const path = require('path');

console.log('🗑️ Testing Database Deletion Sync Behavior');
console.log('==========================================');

async function login() {
  console.log('\n1. 🔐 Logging in...');
  
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Login successful');
      return response.data.token;
    } else {
      console.log('❌ Login failed:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return null;
  }
}

async function checkInitialData() {
  console.log('\n2. 📊 Checking initial data...');
  
  // Connect to both databases
  const sqlitePath = path.join(__dirname, '../data/syncmasterpro.db');
  const sqlite = new Database(sqlitePath);
  
  const pgPool = new Pool({
    host: '*************',
    port: 5432,
    database: 'syncmasterpro',
    user: 'pi',
    password: 'ubuntu'
  });
  
  try {
    // Check users
    const sqliteUsers = sqlite.prepare('SELECT COUNT(*) as count, GROUP_CONCAT(email) as emails FROM users').get();
    const pgUsers = await pgPool.query('SELECT COUNT(*) as count, STRING_AGG(email, \', \') as emails FROM users');
    
    console.log(`👥 Users - SQLite: ${sqliteUsers.count} (${sqliteUsers.emails})`);
    console.log(`👥 Users - PostgreSQL: ${pgUsers.rows[0].count} (${pgUsers.rows[0].emails})`);
    
    // Check tasks
    const sqliteTasks = sqlite.prepare('SELECT COUNT(*) as count, GROUP_CONCAT(name) as names FROM sync_tasks').get();
    const pgTasks = await pgPool.query('SELECT COUNT(*) as count, STRING_AGG(name, \', \') as names FROM sync_tasks');
    
    console.log(`🔄 Tasks - SQLite: ${sqliteTasks.count} (${sqliteTasks.names || 'none'})`);
    console.log(`🔄 Tasks - PostgreSQL: ${pgTasks.rows[0].count} (${pgTasks.rows[0].names || 'none'})`);
    
    return {
      sqlite: {
        users: sqliteUsers.count,
        tasks: sqliteTasks.count,
        userEmails: sqliteUsers.emails,
        taskNames: sqliteTasks.names
      },
      postgresql: {
        users: parseInt(pgUsers.rows[0].count),
        tasks: parseInt(pgTasks.rows[0].count),
        userEmails: pgUsers.rows[0].emails,
        taskNames: pgTasks.rows[0].names
      }
    };
    
  } finally {
    sqlite.close();
    await pgPool.end();
  }
}

async function createTestData() {
  console.log('\n3. 🔧 Creating test data in desktop (SQLite)...');
  
  const sqlitePath = path.join(__dirname, '../data/syncmasterpro.db');
  const sqlite = new Database(sqlitePath);
  
  try {
    // Create a test task
    const insertTask = sqlite.prepare(`
      INSERT INTO sync_tasks (user_id, name, source_path, destination_path, sync_type, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const now = new Date().toISOString();
    const result = insertTask.run(
      1, // user_id
      'TEST DELETE TASK',
      'C:\\temp\\test-delete-source',
      'C:\\temp\\test-delete-dest',
      'bidirectional',
      'idle',
      now,
      now
    );
    
    console.log(`✅ Created test task with ID: ${result.lastInsertRowid}`);
    return result.lastInsertRowid;
    
  } finally {
    sqlite.close();
  }
}

async function syncDesktopToWeb(token) {
  console.log('\n4. 🔄 Syncing Desktop → Web...');
  
  try {
    const response = await axios.post('http://localhost:5001/api/database-sync/sync', {
      direction: 'sqlite-to-pg'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Desktop → Web sync completed');
      return response.data;
    } else {
      console.log('❌ Desktop → Web sync failed:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ Desktop → Web sync error:', error.message);
    return null;
  }
}

async function deleteFromDesktop(taskId) {
  console.log('\n5. 🗑️ Deleting test data from desktop (SQLite)...');
  
  const sqlitePath = path.join(__dirname, '../data/syncmasterpro.db');
  const sqlite = new Database(sqlitePath);
  
  try {
    const deleteTask = sqlite.prepare('DELETE FROM sync_tasks WHERE id = ?');
    const result = deleteTask.run(taskId);
    
    console.log(`✅ Deleted ${result.changes} task(s) from desktop`);
    return result.changes > 0;
    
  } finally {
    sqlite.close();
  }
}

async function syncAfterDeletion(token) {
  console.log('\n6. 🔄 Syncing after deletion (Desktop → Web)...');
  
  try {
    const response = await axios.post('http://localhost:5001/api/database-sync/sync', {
      direction: 'sqlite-to-pg'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Post-deletion sync completed');
      return response.data;
    } else {
      console.log('❌ Post-deletion sync failed:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ Post-deletion sync error:', error.message);
    return null;
  }
}

async function checkFinalData() {
  console.log('\n7. 📊 Checking final data after deletion and sync...');
  
  // Connect to both databases
  const sqlitePath = path.join(__dirname, '../data/syncmasterpro.db');
  const sqlite = new Database(sqlitePath);
  
  const pgPool = new Pool({
    host: '*************',
    port: 5432,
    database: 'syncmasterpro',
    user: 'pi',
    password: 'ubuntu'
  });
  
  try {
    // Check tasks
    const sqliteTasks = sqlite.prepare('SELECT COUNT(*) as count, GROUP_CONCAT(name) as names FROM sync_tasks').get();
    const pgTasks = await pgPool.query('SELECT COUNT(*) as count, STRING_AGG(name, \', \') as names FROM sync_tasks');
    
    console.log(`🔄 Tasks - SQLite: ${sqliteTasks.count} (${sqliteTasks.names || 'none'})`);
    console.log(`🔄 Tasks - PostgreSQL: ${pgTasks.rows[0].count} (${pgTasks.rows[0].names || 'none'})`);
    
    // Check if test task still exists in PostgreSQL
    const testTaskInPg = await pgPool.query('SELECT COUNT(*) as count FROM sync_tasks WHERE name = $1', ['TEST DELETE TASK']);
    console.log(`🔍 Test task in PostgreSQL: ${testTaskInPg.rows[0].count > 0 ? 'EXISTS' : 'DELETED'}`);
    
    return {
      sqlite: {
        tasks: sqliteTasks.count,
        taskNames: sqliteTasks.names
      },
      postgresql: {
        tasks: parseInt(pgTasks.rows[0].count),
        taskNames: pgTasks.rows[0].names,
        testTaskExists: testTaskInPg.rows[0].count > 0
      }
    };
    
  } finally {
    sqlite.close();
    await pgPool.end();
  }
}

async function main() {
  try {
    console.log('🚀 Starting database deletion sync test...\n');
    
    // Login
    const token = await login();
    if (!token) {
      console.log('❌ Cannot proceed without authentication');
      return;
    }
    
    // Check initial data
    const initialData = await checkInitialData();
    
    // Create test data in desktop
    const testTaskId = await createTestData();
    
    // Sync desktop to web
    const syncResult1 = await syncDesktopToWeb(token);
    
    // Check data after first sync
    console.log('\n📊 Data after first sync:');
    const afterFirstSync = await checkInitialData();
    
    // Delete from desktop
    const deleteSuccess = await deleteFromDesktop(testTaskId);
    
    // Sync after deletion
    const syncResult2 = await syncAfterDeletion(token);
    
    // Check final data
    const finalData = await checkFinalData();
    
    console.log('\n📊 DELETION SYNC TEST RESULTS:');
    console.log('==============================');
    console.log('Initial Data:');
    console.log(`  - SQLite Tasks: ${initialData.sqlite.tasks}`);
    console.log(`  - PostgreSQL Tasks: ${initialData.postgresql.tasks}`);
    
    console.log('\nAfter Creating Test Task & Sync:');
    console.log(`  - SQLite Tasks: ${afterFirstSync.sqlite.tasks}`);
    console.log(`  - PostgreSQL Tasks: ${afterFirstSync.postgresql.tasks}`);
    
    console.log('\nAfter Deleting from Desktop & Sync:');
    console.log(`  - SQLite Tasks: ${finalData.sqlite.tasks}`);
    console.log(`  - PostgreSQL Tasks: ${finalData.postgresql.tasks}`);
    console.log(`  - Test Task in PostgreSQL: ${finalData.postgresql.testTaskExists ? 'STILL EXISTS' : 'DELETED'}`);
    
    console.log('\n🎯 CONCLUSION:');
    if (finalData.postgresql.testTaskExists) {
      console.log('❌ DELETION DOES NOT SYNC');
      console.log('   When you delete data from desktop (SQLite),');
      console.log('   it DOES NOT get deleted from web (PostgreSQL)');
      console.log('   The sync only adds/updates records, not deletes them');
    } else {
      console.log('✅ DELETION SYNCS');
      console.log('   When you delete data from desktop (SQLite),');
      console.log('   it DOES get deleted from web (PostgreSQL)');
    }
    
    console.log('\n💡 Database Sync Behavior:');
    console.log('   - Current sync uses INSERT OR REPLACE / ON CONFLICT DO UPDATE');
    console.log('   - This means it only adds or updates existing records');
    console.log('   - Records deleted from source are NOT deleted from destination');
    console.log('   - Web database may contain "orphaned" records from desktop');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
