const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

async function checkSQLiteClients() {
  console.log('🔍 Checking SQLite Database Clients\n');

  try {
    // Find SQLite database file
    const possiblePaths = [
      path.join(__dirname, '..', 'database', 'syncmaster.db'),
      path.join(__dirname, '..', 'server', 'database', 'syncmaster.db'),
      path.join(__dirname, '..', 'syncmaster.db'),
      path.join(__dirname, '..', 'data', 'syncmaster.db')
    ];

    let dbPath = null;
    for (const testPath of possiblePaths) {
      if (fs.existsSync(testPath)) {
        dbPath = testPath;
        break;
      }
    }

    if (!dbPath) {
      console.log('❌ SQLite database file not found in any of these locations:');
      possiblePaths.forEach(p => console.log(`   - ${p}`));
      return;
    }

    console.log('📁 Database path:', dbPath);

    // Connect to SQLite database
    const db = new Database(dbPath, { readonly: true });
    console.log('✅ Connected to SQLite database');

    // Check if desktop_clients table exists
    const tables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='desktop_clients'
    `).all();

    if (tables.length === 0) {
      console.log('❌ desktop_clients table not found');
      
      // List all tables
      const allTables = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table'
      `).all();
      
      console.log('\n📋 Available tables:');
      allTables.forEach(table => {
        console.log(`   - ${table.name}`);
      });
      
      db.close();
      return;
    }

    // Check desktop_clients table
    console.log('\n📊 DESKTOP CLIENTS TABLE:');
    console.log('=' .repeat(80));

    const rows = db.prepare(`
      SELECT 
        id,
        client_id,
        user_id,
        hostname,
        status,
        metadata,
        last_seen,
        created_at
      FROM desktop_clients 
      ORDER BY created_at DESC
    `).all();

    console.log(`📋 Total clients found: ${rows.length}\n`);

    if (rows.length === 0) {
      console.log('📭 No clients found in database');
    } else {
      rows.forEach((row, index) => {
        console.log(`${index + 1}. CLIENT RECORD:`);
        console.log(`   🆔 ID: ${row.id}`);
        console.log(`   🖥️ Client ID: ${row.client_id}`);
        console.log(`   👤 User ID: ${row.user_id}`);
        console.log(`   🏠 Hostname: ${row.hostname}`);
        console.log(`   📊 Status: ${row.status}`);
        console.log(`   📝 Metadata: ${row.metadata || 'null'}`);
        console.log(`   👁️ Last Seen: ${row.last_seen}`);
        console.log(`   📅 Created: ${row.created_at}`);
        console.log('');
      });
    }

    // Summary by status
    console.log('📊 SUMMARY BY STATUS:');
    console.log('-'.repeat(40));
    
    const statusCounts = {};
    rows.forEach(row => {
      statusCounts[row.status] = (statusCounts[row.status] || 0) + 1;
    });

    Object.entries(statusCounts).forEach(([status, count]) => {
      const emoji = status === 'online' ? '🟢' : status === 'offline' ? '🔴' : '⚪';
      console.log(`   ${emoji} ${status}: ${count} clients`);
    });

    // Summary by user
    console.log('\n👥 SUMMARY BY USER:');
    console.log('-'.repeat(40));
    
    const userCounts = {};
    rows.forEach(row => {
      userCounts[row.user_id] = (userCounts[row.user_id] || 0) + 1;
    });

    Object.entries(userCounts).forEach(([userId, count]) => {
      console.log(`   👤 User ${userId}: ${count} clients`);
    });

    // Check for duplicates
    console.log('\n🔍 DUPLICATE CHECK:');
    console.log('-'.repeat(40));
    
    const clientIdCounts = {};
    rows.forEach(row => {
      clientIdCounts[row.client_id] = (clientIdCounts[row.client_id] || 0) + 1;
    });

    const duplicates = Object.entries(clientIdCounts).filter(([_, count]) => count > 1);
    
    if (duplicates.length === 0) {
      console.log('   ✅ No duplicate client IDs found');
    } else {
      console.log('   ⚠️ Duplicate client IDs found:');
      duplicates.forEach(([clientId, count]) => {
        console.log(`      🔄 ${clientId}: ${count} records`);
      });
    }

    // Check users table for reference
    console.log('\n👤 USERS TABLE REFERENCE:');
    console.log('-'.repeat(40));
    
    try {
      const userRows = db.prepare(`
        SELECT id, email, name, created_at 
        FROM users 
        ORDER BY id
      `).all();

      userRows.forEach(user => {
        const clientCount = userCounts[user.id] || 0;
        console.log(`   👤 ${user.id}: ${user.email} (${user.name}) - ${clientCount} clients`);
      });
    } catch (error) {
      console.log('   ⚠️ Users table not accessible:', error.message);
    }

    // Close database
    db.close();
    console.log('\n✅ Database check completed');

  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

// Run check
checkSQLiteClients();
