#!/usr/bin/env node

/**
 * Check database sync service status and configuration
 */

const axios = require('axios');

console.log('🔍 Checking Database Sync Service');
console.log('=================================');

async function checkSyncServiceStatus() {
  console.log('\n1. 📊 Checking sync service status...');
  
  try {
    const response = await axios.get('http://localhost:5001/api/sync/status', {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Status Response:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Sync service accessible');
      console.log('📊 Service Status:', JSON.stringify(response.data, null, 2));
      return response.data;
    } else {
      console.log('❌ Sync service status failed:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Sync service error:', error.message);
    return null;
  }
}

async function enableAutoSync() {
  console.log('\n2. 🔧 Enabling auto sync...');
  
  try {
    const response = await axios.post('http://localhost:5001/api/sync/config', {
      enableAutoSync: true,
      syncDirection: 'sqlite-to-pg', // Desktop is primary source
      syncIntervalMinutes: 5, // Sync every 5 minutes
      conflictResolution: 'sqlite_wins' // Desktop data wins
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Config Response:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Auto sync enabled');
      console.log('📊 New Config:', JSON.stringify(response.data, null, 2));
      return true;
    } else {
      console.log('❌ Enable auto sync failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Enable auto sync error:', error.message);
    return false;
  }
}

async function triggerManualSync() {
  console.log('\n3. 🔄 Triggering manual sync (SQLite → PostgreSQL)...');
  
  try {
    const response = await axios.post('http://localhost:5001/api/sync/sync', {
      direction: 'sqlite-to-pg'
    }, {
      timeout: 60000, // 1 minute timeout
      validateStatus: () => true
    });
    
    console.log('📝 Sync Response:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Manual sync completed');
      console.log('📊 Sync Results:', JSON.stringify(response.data, null, 2));
      return response.data;
    } else {
      console.log('❌ Manual sync failed:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Manual sync error:', error.message);
    return null;
  }
}

async function startAutoSync() {
  console.log('\n4. 🚀 Starting auto sync...');
  
  try {
    const response = await axios.post('http://localhost:5001/api/sync/start', {}, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Start Response:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Auto sync started');
      console.log('📊 Status:', JSON.stringify(response.data, null, 2));
      return true;
    } else {
      console.log('❌ Start auto sync failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Start auto sync error:', error.message);
    return false;
  }
}

async function checkFinalStatus() {
  console.log('\n5. 📊 Checking final status...');
  
  try {
    const response = await axios.get('http://localhost:5001/api/sync/status', {
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Final status retrieved');
      console.log('📊 Final Status:', JSON.stringify(response.data, null, 2));
      return response.data;
    } else {
      console.log('❌ Final status failed');
      return null;
    }
  } catch (error) {
    console.log('❌ Final status error:', error.message);
    return null;
  }
}

async function main() {
  try {
    console.log('🚀 Starting sync service check...\n');
    
    // Check current status
    const initialStatus = await checkSyncServiceStatus();
    
    // Enable auto sync with proper configuration
    const configSuccess = await enableAutoSync();
    
    // Trigger manual sync to get data in sync
    const syncResults = await triggerManualSync();
    
    // Start auto sync
    const autoSyncStarted = await startAutoSync();
    
    // Check final status
    const finalStatus = await checkFinalStatus();
    
    console.log('\n📊 Summary:');
    console.log('===========');
    console.log('- Service Accessible:', initialStatus ? '✅' : '❌');
    console.log('- Configuration Updated:', configSuccess ? '✅' : '❌');
    console.log('- Manual Sync:', syncResults ? '✅' : '❌');
    console.log('- Auto Sync Started:', autoSyncStarted ? '✅' : '❌');
    console.log('- Final Status:', finalStatus ? '✅' : '❌');
    
    if (finalStatus && finalStatus.isRunning) {
      console.log('\n🎉 SUCCESS! Database sync service is now running!');
      console.log('🔍 Configuration:');
      console.log(`   - Direction: ${finalStatus.config?.syncDirection || 'unknown'}`);
      console.log(`   - Interval: ${finalStatus.config?.syncIntervalMinutes || 'unknown'} minutes`);
      console.log(`   - Auto Sync: ${finalStatus.config?.enableAutoSync ? 'Enabled' : 'Disabled'}`);
      console.log(`   - Last Sync: ${finalStatus.lastSyncTime || 'Never'}`);
      console.log('');
      console.log('📋 Desktop data will now sync to web database every 5 minutes');
      console.log('🎯 Web manager will show real desktop data');
    } else {
      console.log('\n❌ Database sync service has issues');
      console.log('💡 Check web server logs for more details');
    }
    
  } catch (error) {
    console.error('\n❌ Check failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
