import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { useSync } from '../../contexts/SyncContext';
import { useNotification } from '../../contexts/NotificationContext';

const NotificationPanel = ({ isOpen, onClose }) => {
  const { t } = useLanguage();
  const { activeSyncs } = useSync();
  const { notifications: realNotifications } = useNotification();
  const [displayNotifications, setDisplayNotifications] = useState([]);

  // Convert real notifications to display format and add sync notifications
  useEffect(() => {
    const convertedNotifications = [];

    // Add real notifications from NotificationContext
    realNotifications.forEach(notification => {
      convertedNotifications.push({
        id: notification.id,
        type: notification.type,
        title: getNotificationTitle(notification.type),
        message: notification.message,
        time: new Date(notification.timestamp),
        read: false // Real notifications are always new
      });
    });

    // Add active sync notifications
    if (activeSyncs.size > 0) {
      const activeNotification = {
        id: 'active-sync',
        type: 'info',
        title: t('syncInProgress'),
        message: `${activeSyncs.size} ${t('tasksRunning')}`,
        time: new Date(),
        read: false
      };
      convertedNotifications.unshift(activeNotification);
    }

    // Sort by time (newest first)
    convertedNotifications.sort((a, b) => b.time - a.time);

    setDisplayNotifications(convertedNotifications);
  }, [realNotifications, activeSyncs, t]);

  // Helper function to get notification title based on type
  const getNotificationTitle = (type) => {
    switch (type) {
      case 'success':
        return t('success');
      case 'error':
        return t('error');
      case 'warning':
        return t('warning');
      case 'info':
      default:
        return t('info');
    }
  };

  const markAsRead = (id) => {
    setDisplayNotifications(prev =>
      prev.map(notif =>
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const markAllAsRead = () => {
    setDisplayNotifications(prev =>
      prev.map(notif => ({ ...notif, read: true }))
    );
  };

  const clearAll = () => {
    setDisplayNotifications([]);
  };

  const getTimeAgo = (time) => {
    const now = new Date();
    const diff = now - time;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return t('justNow');
    if (minutes < 60) return `${minutes}${t('minutesAgo')}`;
    if (hours < 24) return `${hours}${t('hoursAgo')}`;
    return `${days}${t('daysAgo')}`;
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
      default:
        return 'ℹ️';
    }
  };

  const getNotificationColor = (type) => {
    switch (type) {
      case 'success':
        return 'text-green-600 dark:text-green-400';
      case 'error':
        return 'text-red-600 dark:text-red-400';
      case 'warning':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'info':
      default:
        return 'text-blue-600 dark:text-blue-400';
    }
  };

  const unreadCount = displayNotifications.filter(n => !n.read).length;

  if (!isOpen) return null;

  return (
    <div className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50 max-h-96 overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {t('notifications')}
          {unreadCount > 0 && (
            <span className="ml-2 px-2 py-1 text-xs bg-red-500 text-white rounded-full">
              {unreadCount}
            </span>
          )}
        </h3>
        <button
          onClick={onClose}
          className="p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <XIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      {/* Actions */}
      {displayNotifications.length > 0 && (
        <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-750">
          <button
            onClick={markAllAsRead}
            className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
            disabled={unreadCount === 0}
          >
            {t('markAllRead')}
          </button>
          <button
            onClick={clearAll}
            className="text-sm text-red-600 dark:text-red-400 hover:underline"
          >
            {t('clearAll')}
          </button>
        </div>
      )}

      {/* Notifications List */}
      <div className="max-h-64 overflow-y-auto">
        {displayNotifications.length === 0 ? (
          <div className="p-6 text-center">
            <div className="text-gray-400 dark:text-gray-500 text-4xl mb-2">🔔</div>
            <p className="text-gray-500 dark:text-gray-400">{t('noNotifications')}</p>
          </div>
        ) : (
          displayNotifications.map((notification) => (
            <div
              key={notification.id}
              onClick={() => markAsRead(notification.id)}
              className={`p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors ${
                !notification.read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
              }`}
            >
              <div className="flex items-start space-x-3">
                <span className="text-lg flex-shrink-0 mt-0.5">
                  {getNotificationIcon(notification.type)}
                </span>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className={`text-sm font-medium ${getNotificationColor(notification.type)}`}>
                      {notification.title}
                    </p>
                    {!notification.read && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {notification.message}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {getTimeAgo(notification.time)}
                  </p>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

// Icon component
const XIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
  </svg>
);

export default NotificationPanel;
