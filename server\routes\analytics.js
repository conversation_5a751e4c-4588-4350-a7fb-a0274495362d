const express = require('express');
const router = express.Router();
const AnalyticsService = require('../services/AnalyticsService');
const authenticateToken = require('../middleware/auth');

// Lazy initialization of service
let analyticsService = null;
const getAnalyticsService = () => {
  if (!analyticsService) {
    analyticsService = new AnalyticsService();
  }
  return analyticsService;
};

// Get sync analytics
router.get('/sync', authenticateToken, async (req, res) => {
  try {
    const { timeRange = '30d' } = req.query;
    
    const analytics = await getAnalyticsService().getSyncAnalytics(req.userId, timeRange);
    
    res.json({
      success: true,
      analytics
    });
  } catch (error) {
    console.error('Failed to get sync analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get sync analytics'
    });
  }
});

// Generate comprehensive report
router.post('/reports', authenticateToken, async (req, res) => {
  try {
    const { reportType = 'comprehensive', timeRange = '30d' } = req.body;
    
    const report = await getAnalyticsService().generateReport(req.userId, reportType, timeRange);
    
    res.json({
      success: true,
      report
    });
  } catch (error) {
    console.error('Failed to generate report:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate report'
    });
  }
});

// Get saved reports
router.get('/reports', authenticateToken, async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const reports = await getAnalyticsService().getReports(req.userId, parseInt(limit));
    
    res.json({
      success: true,
      reports,
      count: reports.length
    });
  } catch (error) {
    console.error('Failed to get reports:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get reports'
    });
  }
});

// Get dashboard summary
router.get('/dashboard', authenticateToken, async (req, res) => {
  try {
    const timeRange = '7d'; // Last 7 days for dashboard
    const analytics = await getAnalyticsService().getSyncAnalytics(req.userId, timeRange);
    
    // Create dashboard summary
    const dashboard = {
      timeRange: analytics.timeRange,
      period: analytics.period,
      summary: {
        totalSyncs: analytics.sync.totalSyncs,
        successRate: `${analytics.sync.successRate}%`,
        totalFiles: analytics.sync.totalFilesProcessed,
        totalDataMB: analytics.sync.totalBytesTransferredMB,
        avgSpeed: `${analytics.performance.avgTransferSpeedMBps} MB/s`,
        pendingConflicts: analytics.conflicts.pendingConflicts,
        storageUsedMB: analytics.storage.totalVersionSizeMB
      },
      trends: {
        dailyActivity: analytics.transfers.dailyTransfers.slice(0, 7),
        hourlyPattern: analytics.transfers.hourlyPattern,
        topConflictStrategy: analytics.conflicts.resolutionStrategies[0]?.strategy || 'none'
      },
      alerts: {
        lowSuccessRate: parseFloat(analytics.sync.successRate) < 95,
        highConflictRate: analytics.conflicts.totalConflicts > analytics.sync.totalSyncs * 0.1,
        storageWarning: parseFloat(analytics.storage.totalVersionSizeMB) > 1000
      }
    };
    
    res.json({
      success: true,
      dashboard
    });
  } catch (error) {
    console.error('Failed to get dashboard data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get dashboard data'
    });
  }
});

// Get performance metrics
router.get('/performance', authenticateToken, async (req, res) => {
  try {
    const { timeRange = '30d' } = req.query;
    const analytics = await getAnalyticsService().getSyncAnalytics(req.userId, timeRange);

    const performance = {
      timeRange: analytics.timeRange,
      metrics: analytics.performance,
      trends: analytics.transfers,
      recommendations: analytics.performance.avgTransferSpeedMBps < 1 ? [
        {
          type: 'performance',
          title: 'Optimize Transfer Speed',
          description: 'Consider increasing bandwidth limits or checking network conditions',
          priority: 'medium'
        }
      ] : []
    };
    
    res.json({
      success: true,
      performance
    });
  } catch (error) {
    console.error('Failed to get performance metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get performance metrics'
    });
  }
});

// Get storage analytics
router.get('/storage', authenticateToken, async (req, res) => {
  try {
    const { timeRange = '30d' } = req.query;
    const analytics = await getAnalyticsService().getSyncAnalytics(req.userId, timeRange);

    const storage = {
      timeRange: analytics.timeRange,
      statistics: analytics.storage,
      recommendations: []
    };
    
    // Add storage recommendations
    if (parseFloat(analytics.storage.totalVersionSizeMB) > 1000) {
      storage.recommendations.push({
        type: 'storage',
        title: 'Version Storage Cleanup',
        description: `${analytics.storage.totalVersionSizeMB} MB used for file versions. Consider cleanup.`,
        priority: 'medium'
      });
    }
    
    if (analytics.storage.totalVersions > 1000) {
      storage.recommendations.push({
        type: 'storage',
        title: 'Reduce Version Retention',
        description: `${analytics.storage.totalVersions} versions stored. Consider reducing retention policy.`,
        priority: 'low'
      });
    }
    
    res.json({
      success: true,
      storage
    });
  } catch (error) {
    console.error('Failed to get storage analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get storage analytics'
    });
  }
});

// Get conflict analytics
router.get('/conflicts', authenticateToken, async (req, res) => {
  try {
    const { timeRange = '30d' } = req.query;
    const analytics = await getAnalyticsService().getSyncAnalytics(req.userId, timeRange);

    const conflicts = {
      timeRange: analytics.timeRange,
      statistics: analytics.conflicts,
      insights: {
        mostUsedStrategy: analytics.conflicts.resolutionStrategies[0]?.strategy || 'none',
        resolutionEfficiency: analytics.conflicts.resolutionRate,
        conflictTrend: analytics.conflicts.totalConflicts > 0 ? 'needs_attention' : 'good'
      },
      recommendations: []
    };
    
    // Add conflict recommendations
    if (parseFloat(analytics.conflicts.resolutionRate) < 80) {
      conflicts.recommendations.push({
        type: 'conflicts',
        title: 'Improve Conflict Resolution',
        description: 'Low resolution rate detected. Consider reviewing conflict strategies.',
        priority: 'high'
      });
    }
    
    if (analytics.conflicts.pendingConflicts > 10) {
      conflicts.recommendations.push({
        type: 'conflicts',
        title: 'Resolve Pending Conflicts',
        description: `${analytics.conflicts.pendingConflicts} conflicts pending resolution.`,
        priority: 'medium'
      });
    }
    
    res.json({
      success: true,
      conflicts
    });
  } catch (error) {
    console.error('Failed to get conflict analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get conflict analytics'
    });
  }
});

// Export analytics data
router.get('/export', authenticateToken, async (req, res) => {
  try {
    const { timeRange = '30d', format = 'json' } = req.query;
    
    const analytics = await getAnalyticsService().getSyncAnalytics(req.userId, timeRange);
    
    if (format === 'csv') {
      // Convert to CSV format
      const csvData = this.convertToCSV(analytics);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename=sync-analytics-${timeRange}.csv`);
      res.send(csvData);
    } else {
      // JSON format
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename=sync-analytics-${timeRange}.json`);
      res.json({
        exportedAt: new Date().toISOString(),
        timeRange,
        analytics
      });
    }
  } catch (error) {
    console.error('Failed to export analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to export analytics'
    });
  }
});

// Get real-time statistics
router.get('/realtime', authenticateToken, async (req, res) => {
  try {
    const realtimeStats = await getAnalyticsService().getSyncAnalytics(req.userId, '1d');
    
    const realtime = {
      timestamp: new Date().toISOString(),
      todayStats: {
        syncs: realtimeStats.sync.totalSyncs,
        successRate: realtimeStats.sync.successRate,
        filesProcessed: realtimeStats.sync.totalFilesProcessed,
        dataTransferredMB: realtimeStats.sync.totalBytesTransferredMB
      },
      currentStatus: {
        runningSyncs: realtimeStats.sync.runningSyncs,
        pendingConflicts: realtimeStats.conflicts.pendingConflicts,
        avgSpeed: realtimeStats.performance.avgTransferSpeedMBps
      },
      lastHourActivity: realtimeStats.transfers.hourlyPattern.slice(-1)[0] || {
        hour: new Date().getHours(),
        syncCount: 0,
        avgDuration: 0
      }
    };
    
    res.json({
      success: true,
      realtime
    });
  } catch (error) {
    console.error('Failed to get realtime statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get realtime statistics'
    });
  }
});

// Helper function to convert analytics to CSV
function convertToCSV(analytics) {
  const rows = [];

  // Headers
  rows.push('Date,Syncs,Files,Data(MB),Success Rate,Conflicts');

  // Daily data
  analytics.transfers.dailyTransfers.forEach(day => {
    rows.push([
      day.date,
      day.syncCount,
      day.filesCount,
      day.bytesTransferredMB,
      analytics.sync.successRate,
      0 // conflicts per day would need separate query
    ].join(','));
  });

  return rows.join('\n');
}

module.exports = router;
