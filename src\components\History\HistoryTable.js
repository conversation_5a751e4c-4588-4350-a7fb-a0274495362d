import React, { useState } from 'react';
import { useNotification } from '../../contexts/NotificationContext';
import { useSync } from '../../contexts/SyncContext';
import { useLanguage } from '../../contexts/LanguageContext';

const HistoryTable = ({
  paginatedHistory,
  sortField,
  sortDirection,
  onSort,
  onViewDetail
}) => {
  const { addNotification } = useNotification();
  const { startSync, syncTasks, deleteHistoryEntry } = useSync();
  const { t } = useLanguage();

  // Format file size
  const formatFileSize = (bytes) => {
    if (!bytes || bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format duration
  const formatDuration = (ms) => {
    if (!ms || ms === 0) return '0s';
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return (
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
        );
      case 'error':
        return (
          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
        );
      case 'running':
        return (
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
        );
      default:
        return (
          <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
        );
    }
  };

  // Get status badge
  const getStatusBadge = (status) => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";

    switch (status) {
      case 'completed':
        return (
          <span className={`${baseClasses} bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200`}>
            {t('completed')}
          </span>
        );
      case 'error':
        return (
          <span className={`${baseClasses} bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200`}>
            {t('failed')}
          </span>
        );
      case 'running':
        return (
          <span className={`${baseClasses} bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200`}>
            {t('syncing')}
          </span>
        );
      default:
        return (
          <span className={`${baseClasses} bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200`}>
            Unknown
          </span>
        );
    }
  };

  // Handle retry sync
  const handleRetrySync = async (entry) => {
    try {
      const task = syncTasks.find(t => t.id === entry.taskId);
      if (!task) {
        addNotification('Task not found. Cannot retry sync.', 'error');
        return;
      }

      addNotification('Retrying sync...', 'info');
      const result = await startSync(task.id);
      
      if (result.success) {
        addNotification('Sync retry started successfully', 'success');
      } else {
        addNotification('Failed to retry sync: ' + result.error, 'error');
      }
    } catch (error) {
      console.error('Error retrying sync:', error);
      addNotification('Failed to retry sync', 'error');
    }
  };

  // Handle copy details
  const handleCopyDetails = (entry) => {
    const details = `Task #${entry.taskId} - ${entry.status} - ${new Date(entry.timestamp).toLocaleString()}`;
    navigator.clipboard.writeText(details);
    addNotification('Details copied to clipboard', 'success');
  };

  // Handle delete entry
  const handleDeleteEntry = async (entry) => {
    if (window.confirm(`Are you sure you want to delete this history entry?\n\nTask #${entry.taskId} - ${entry.status}\n${new Date(entry.timestamp).toLocaleString()}`)) {
      try {
        const result = await deleteHistoryEntry(entry.id);
        if (result.success) {
          addNotification(result.message, 'success');
        } else {
          addNotification(result.error, 'error');
        }
      } catch (error) {
        console.error('Error deleting history entry:', error);
        addNotification('Failed to delete history entry', 'error');
      }
    }
  };

  // Sortable header component
  const SortableHeader = ({ field, children, className = "" }) => (
    <th
      onClick={() => onSort(field)}
      className={`px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${className}`}
    >
      <div className="flex items-center space-x-1">
        <span>{children}</span>
        {sortField === field && (
          <span className="text-blue-500 dark:text-blue-400">
            {sortDirection === 'asc' ? '↑' : '↓'}
          </span>
        )}
      </div>
    </th>
  );

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-700">
          <tr>
            <SortableHeader field="taskId">{t('task')}</SortableHeader>
            <SortableHeader field="status">{t('status')}</SortableHeader>
            <SortableHeader field="timestamp">Date & Time</SortableHeader>
            <SortableHeader field="filesProcessed">Files</SortableHeader>
            <SortableHeader field="totalSize">Size</SortableHeader>
            <SortableHeader field="duration">{t('duration')}</SortableHeader>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              {t('actions')}
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          {paginatedHistory.map((entry) => (
            <tr key={entry.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
              {/* Task Column */}
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="flex-shrink-0 mr-3">
                    {getStatusIcon(entry.status)}
                  </div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {t('task')} #{entry.taskId}
                  </div>
                </div>
              </td>

              {/* Status Column */}
              <td className="px-6 py-4 whitespace-nowrap">
                {getStatusBadge(entry.status)}
              </td>

              {/* Date & Time Column */}
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900 dark:text-white">
                  {new Date(entry.timestamp).toLocaleDateString()}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {new Date(entry.timestamp).toLocaleTimeString()}
                </div>
              </td>

              {/* Files Column */}
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900 dark:text-white">
                  {(entry.filesProcessed || 0).toLocaleString()}
                </div>
                {entry.filesAdded !== undefined && (
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    +{entry.filesAdded} ~{entry.filesUpdated || 0} -{entry.filesDeleted || 0}
                  </div>
                )}
              </td>

              {/* Size Column */}
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900 dark:text-white">
                  {formatFileSize(entry.totalSize || 0)}
                </div>
                {entry.bytesTransferred && entry.bytesTransferred !== entry.totalSize && (
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Transferred: {formatFileSize(entry.bytesTransferred)}
                  </div>
                )}
              </td>

              {/* Duration Column */}
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {formatDuration(entry.duration || 0)}
              </td>

              {/* Actions Column */}
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => onViewDetail(entry)}
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition-colors duration-150"
                    title={t('viewDetails')}
                  >
                    {t('view')}
                  </button>
                  <button
                    onClick={() => handleCopyDetails(entry)}
                    className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300 transition-colors duration-150"
                    title="Copy Details"
                  >
                    Copy
                  </button>
                  {entry.status === 'error' && (
                    <button
                      onClick={() => handleRetrySync(entry)}
                      className="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 transition-colors duration-150"
                      title="Retry Sync"
                    >
                      Retry
                    </button>
                  )}
                  <button
                    onClick={() => handleDeleteEntry(entry)}
                    className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition-colors duration-150"
                    title={`${t('delete')} Entry`}
                  >
                    {t('delete')}
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {/* Error Details Row (if any entry has error) */}
      {paginatedHistory.some(entry => entry.error) && (
        <div className="mt-4">
          {paginatedHistory
            .filter(entry => entry.error)
            .map(entry => (
              <div key={`error-${entry.id}`} className="mb-2 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                  </div>
                  <div className="ml-3">
                    <div className="text-sm font-medium text-red-800 dark:text-red-200">
                      {t('task')} #{entry.taskId} {t('failed')}:
                    </div>
                    <div className="text-sm text-red-700 dark:text-red-300 mt-1">
                      {entry.error}
                    </div>
                  </div>
                </div>
              </div>
            ))
          }
        </div>
      )}
    </div>
  );
};

export default HistoryTable;
