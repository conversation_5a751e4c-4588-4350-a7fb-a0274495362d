#!/usr/bin/env node

/**
 * Fix password hash for web server authentication
 */

const { Pool } = require('pg');
const bcrypt = require('../node_modules/bcrypt');

console.log('🔧 Fixing Web Server Password Hash');
console.log('==================================');

async function fixPasswordHash() {
  console.log('\n1. 🐘 Connecting to PostgreSQL...');
  
  const pool = new Pool({
    host: '*************',
    port: 5432,
    database: 'syncmasterpro',
    user: 'pi',
    password: 'ubuntu'
  });

  try {
    const client = await pool.connect();
    console.log('✅ Connected to PostgreSQL');
    
    // Check current user
    console.log('\n2. 👤 Checking current admin user...');
    const currentUser = await client.query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    
    if (currentUser.rows.length === 0) {
      console.log('❌ Admin user not found');
      return;
    }
    
    const user = currentUser.rows[0];
    console.log(`✅ Found user: ${user.name} (${user.email})`);
    console.log(`📝 Current password hash: ${user.password.substring(0, 20)}...`);
    
    // Generate new password hash
    console.log('\n3. 🔐 Generating new password hash...');
    const newPassword = 'admin';
    const saltRounds = 12;
    const newHash = await bcrypt.hash(newPassword, saltRounds);
    
    console.log(`📝 New password hash: ${newHash.substring(0, 20)}...`);
    
    // Update password hash
    console.log('\n4. 💾 Updating password hash...');
    await client.query(
      'UPDATE users SET password = $1, updated_at = CURRENT_TIMESTAMP WHERE email = $2',
      [newHash, '<EMAIL>']
    );
    
    console.log('✅ Password hash updated successfully');
    
    // Test the new hash
    console.log('\n5. 🧪 Testing new password hash...');
    const testResult = await bcrypt.compare('admin', newHash);
    
    if (testResult) {
      console.log('✅ Password hash test passed');
    } else {
      console.log('❌ Password hash test failed');
    }
    
    // Clean up old sessions
    console.log('\n6. 🧹 Cleaning up old sessions...');
    const deleteResult = await client.query('DELETE FROM sessions WHERE user_id = $1', [user.id]);
    console.log(`✅ Deleted ${deleteResult.rowCount} old sessions`);
    
    client.release();
    await pool.end();
    
  } catch (error) {
    console.error('❌ PostgreSQL error:', error.message);
    await pool.end();
    throw error;
  }
}

async function testLogin() {
  console.log('\n7. 🔐 Testing web server login...');
  
  const axios = require('axios');
  
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Login Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Web server login successful!');
      console.log('👤 User:', response.data.user.name);
      console.log('🔑 Token length:', response.data.token.length);
      return true;
    } else {
      console.log('❌ Web server login failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Web server login error:', error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting password fix...\n');
    
    // Fix password hash
    await fixPasswordHash();
    
    // Test login
    const loginSuccess = await testLogin();
    
    console.log('\n📊 Summary:');
    console.log('===========');
    console.log('- Password Hash Update:', '✅ Success');
    console.log('- Web Server Login:', loginSuccess ? '✅ Success' : '❌ Failed');
    
    if (loginSuccess) {
      console.log('\n🎉 SUCCESS! Web server authentication is now working!');
      console.log('🔍 You can now login to web <NAME_EMAIL> / admin');
    } else {
      console.log('\n❌ Web server authentication still has issues');
      console.log('💡 Check web server logs for more details');
    }
    
  } catch (error) {
    console.error('\n❌ Fix failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
