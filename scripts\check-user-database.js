#!/usr/bin/env node

/**
 * Check user database on both servers
 */

const axios = require('axios');

console.log('🧪 Checking User Database');
console.log('=========================');

async function checkDesktopUsers() {
  console.log('\n1. 🖥️ Checking desktop server users...');
  
  try {
    // Try to get users without auth first
    const response = await axios.get('http://localhost:5002/api/users', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📝 Desktop Users API Status:', response.status);
    
    if (response.status === 200) {
      console.log('📊 Desktop Users:', response.data);
    } else {
      console.log('📊 Desktop Users Response:', response.data);
    }
  } catch (error) {
    console.log('❌ Desktop users check error:', error.message);
  }
}

async function checkWebUsers() {
  console.log('\n2. 🌐 Checking web server users...');
  
  try {
    const response = await axios.get('http://localhost:5001/api/users', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📝 Web Users API Status:', response.status);
    
    if (response.status === 200) {
      console.log('📊 Web Users:', response.data);
    } else {
      console.log('📊 Web Users Response:', response.data);
    }
  } catch (error) {
    console.log('❌ Web users check error:', error.message);
  }
}

async function testDesktopLogin() {
  console.log('\n3. 🔐 Testing desktop login with different credentials...');
  
  const testCredentials = [
    { email: '<EMAIL>', password: 'admin' },
    { email: 'admin', password: 'admin' },
    { email: '<EMAIL>', password: 'password' }
  ];
  
  for (const creds of testCredentials) {
    try {
      console.log(`\n   Testing: ${creds.email} / ${creds.password}`);
      
      const response = await axios.post('http://localhost:5002/api/auth/login', creds, {
        timeout: 5000,
        validateStatus: () => true
      });
      
      console.log(`   Status: ${response.status}`);
      
      if (response.status === 200) {
        console.log('   ✅ Login successful!');
        console.log('   👤 User:', response.data.user.name);
        return { token: response.data.token, user: response.data.user };
      } else {
        console.log('   ❌ Login failed:', response.data.message || response.data.error);
      }
    } catch (error) {
      console.log('   ❌ Login error:', error.message);
    }
  }
  
  return null;
}

async function testWebLogin() {
  console.log('\n4. 🌐 Testing web login...');
  
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📝 Web Login Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Web login successful!');
      console.log('👤 User:', response.data.user.name);
      return { token: response.data.token, user: response.data.user };
    } else {
      console.log('❌ Web login failed:', response.data);
    }
  } catch (error) {
    console.log('❌ Web login error:', error.message);
  }
  
  return null;
}

async function main() {
  try {
    console.log('🚀 Starting database check...\n');
    
    // Check users on both servers
    await checkDesktopUsers();
    await checkWebUsers();
    
    // Test login on both servers
    const desktopAuth = await testDesktopLogin();
    const webAuth = await testWebLogin();
    
    console.log('\n📊 Summary:');
    console.log('===========');
    console.log('- Desktop Login:', desktopAuth ? '✅ Success' : '❌ Failed');
    console.log('- Web Login:', webAuth ? '✅ Success' : '❌ Failed');
    
    if (desktopAuth && webAuth) {
      console.log('\n🎉 Both servers working! Now test client registration...');
    } else if (!desktopAuth && webAuth) {
      console.log('\n⚠️ Desktop server has authentication issues');
      console.log('💡 Check desktop database or user sync');
    } else if (desktopAuth && !webAuth) {
      console.log('\n⚠️ Web server has authentication issues');
    } else {
      console.log('\n❌ Both servers have authentication issues');
    }
    
  } catch (error) {
    console.error('\n❌ Check failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
