const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const EventEmitter = require('events');

class FileSyncEngine extends EventEmitter {
  constructor(io = null) {
    super();
    this.activeSyncs = new Map();
    this.syncStats = new Map();
    this.io = io; // Socket.IO instance for real-time updates
  }

  async startSync(task) {
    console.log(`🔄 Starting sync for task: ${task.name}`);
    
    if (this.activeSyncs.has(task.id)) {
      throw new Error('Sync already running for this task');
    }

    const syncContext = {
      task,
      startTime: Date.now(),
      status: 'running',
      filesProcessed: 0,
      filesTotal: 0,
      bytesTransferred: 0,
      errors: [],
      cancelled: false
    };

    this.activeSyncs.set(task.id, syncContext);
    this.emit('syncStarted', { taskId: task.id, task });

    // Emit real-time update
    if (this.io) {
      this.io.emit('sync-started', {
        taskId: task.id,
        taskName: task.name,
        status: 'running',
        timestamp: new Date().toISOString()
      });
    }

    try {
      // Validate paths
      await this.validatePaths(task);
      
      // Scan files
      this.emit('syncProgress', {
        taskId: task.id,
        status: 'scanning',
        message: 'Scanning files...'
      });

      // Emit real-time scanning update
      if (this.io) {
        this.io.emit('sync-progress', {
          taskId: task.id,
          status: 'scanning',
          message: 'Scanning files...',
          progress: 0,
          timestamp: new Date().toISOString()
        });
      }
      
      const sourceFiles = await this.scanDirectory(task.source_path, task);
      const destFiles = await this.scanDirectory(task.destination_path, task);
      
      syncContext.filesTotal = sourceFiles.length;
      
      // Perform sync based on type
      await this.performSync(task, sourceFiles, destFiles, syncContext);
      
      // Complete sync
      syncContext.status = 'completed';
      syncContext.endTime = Date.now();
      syncContext.duration = syncContext.endTime - syncContext.startTime;
      
      this.emit('syncCompleted', {
        taskId: task.id,
        stats: this.getSyncStats(syncContext)
      });

      // Emit real-time completion update
      if (this.io) {
        this.io.emit('sync-completed', {
          taskId: task.id,
          taskName: task.name,
          status: 'completed',
          stats: this.getSyncStats(syncContext),
          timestamp: new Date().toISOString()
        });
      }

      console.log(`✅ Sync completed for task: ${task.name}`);
      
    } catch (error) {
      syncContext.status = 'error';
      syncContext.error = error.message;
      
      this.emit('syncError', {
        taskId: task.id,
        error: error.message
      });

      // Emit real-time error update
      if (this.io) {
        this.io.emit('sync-error', {
          taskId: task.id,
          taskName: task.name,
          status: 'error',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }

      console.error(`❌ Sync failed for task: ${task.name}`, error);
      throw error;
      
    } finally {
      this.activeSyncs.delete(task.id);
    }
  }

  async stopSync(taskId) {
    const syncContext = this.activeSyncs.get(taskId);
    if (syncContext) {
      syncContext.cancelled = true;
      syncContext.status = 'cancelled';
      
      this.emit('syncCancelled', { taskId });
      this.activeSyncs.delete(taskId);
      
      console.log(`🛑 Sync cancelled for task: ${taskId}`);
    }
  }

  async validatePaths(task) {
    try {
      const sourceStat = await fs.stat(task.source_path);
      if (!sourceStat.isDirectory()) {
        throw new Error('Source path is not a directory');
      }
    } catch (error) {
      throw new Error(`Source path invalid: ${error.message}`);
    }

    try {
      await fs.access(task.destination_path);
    } catch (error) {
      // Create destination directory if it doesn't exist
      await fs.mkdir(task.destination_path, { recursive: true });
    }
  }

  async scanDirectory(dirPath, task) {
    const files = [];
    
    try {
      const items = await fs.readdir(dirPath);
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        
        try {
          const stat = await fs.stat(fullPath);
          
          if (stat.isDirectory()) {
            // Recursively scan subdirectories
            const subFiles = await this.scanDirectory(fullPath, task);
            files.push(...subFiles);
          } else {
            // Check if file should be included
            if (this.shouldIncludeFile(fullPath, task)) {
              files.push({
                path: fullPath,
                relativePath: path.relative(dirPath, fullPath),
                size: stat.size,
                mtime: stat.mtime,
                birthtime: stat.birthtime, // File creation time
                checksum: null // Will be calculated when needed
              });
            }
          }
        } catch (error) {
          console.warn(`⚠️ Cannot access ${fullPath}: ${error.message}`);
        }
      }
    } catch (error) {
      console.error(`❌ Cannot scan directory ${dirPath}: ${error.message}`);
    }
    
    return files;
  }

  shouldIncludeFile(filePath, task) {
    const fileName = path.basename(filePath);
    const filters = task.filters || [];
    
    // Check exclude patterns
    for (const filter of filters) {
      if (this.matchPattern(fileName, filter)) {
        return false;
      }
    }
    
    return true;
  }

  matchPattern(fileName, pattern) {
    // Simple pattern matching (supports * wildcard)
    const regex = new RegExp(
      '^' + pattern.replace(/\*/g, '.*').replace(/\?/g, '.') + '$',
      'i'
    );
    return regex.test(fileName);
  }

  async performSync(task, sourceFiles, destFiles, syncContext) {
    const destFileMap = new Map();
    destFiles.forEach(file => {
      destFileMap.set(file.relativePath, file);
    });

    // Handle both field name formats
    const syncType = task.syncType || task.sync_type;

    switch (syncType) {
      case 'bidirectional':
        await this.bidirectionalSync(task, sourceFiles, destFiles, syncContext);
        break;
      case 'source-to-destination':
        await this.oneWaySync(task, sourceFiles, destFileMap, syncContext, 'source-to-dest');
        break;
      case 'destination-to-source':
        await this.oneWaySync(task, destFiles, new Map(sourceFiles.map(f => [f.relativePath, f])), syncContext, 'dest-to-source');
        break;
      case 'mirror':
        await this.mirrorSync(task, sourceFiles, destFileMap, syncContext);
        break;
      case 'incremental':
        await this.incrementalSync(task, sourceFiles, destFileMap, syncContext);
        break;
      case 'today-only':
        await this.todayOnlySync(task, sourceFiles, destFileMap, syncContext);
        break;
      default:
        await this.oneWaySync(task, sourceFiles, destFileMap, syncContext, 'source-to-dest');
    }
  }

  async oneWaySync(task, sourceFiles, destFileMap, syncContext, direction) {
    for (const sourceFile of sourceFiles) {
      if (syncContext.cancelled) break;
      
      const destFile = destFileMap.get(sourceFile.relativePath);
      const shouldCopy = !destFile || await this.shouldUpdateFile(sourceFile, destFile);
      
      if (shouldCopy) {
        await this.copyFile(task, sourceFile, syncContext, direction);
      }
      
      syncContext.filesProcessed++;

      const progressData = {
        taskId: task.id,
        status: 'syncing',
        filesProcessed: syncContext.filesProcessed,
        filesTotal: syncContext.filesTotal,
        currentFile: sourceFile.relativePath,
        progress: Math.round((syncContext.filesProcessed / syncContext.filesTotal) * 100),
        bytesTransferred: syncContext.bytesTransferred
      };

      this.emit('syncProgress', progressData);

      // Emit real-time progress update
      if (this.io) {
        this.io.emit('sync-progress', {
          ...progressData,
          timestamp: new Date().toISOString()
        });
      }
    }
  }

  async bidirectionalSync(task, sourceFiles, destFiles, syncContext) {
    // For bidirectional, we need to sync both ways
    const sourceFileMap = new Map(sourceFiles.map(f => [f.relativePath, f]));
    const destFileMap = new Map(destFiles.map(f => [f.relativePath, f]));
    
    // First pass: source to destination
    await this.oneWaySync(task, sourceFiles, destFileMap, syncContext, 'source-to-dest');
    
    // Second pass: destination to source (only files not in source)
    const destOnlyFiles = destFiles.filter(f => !sourceFileMap.has(f.relativePath));
    if (destOnlyFiles.length > 0) {
      syncContext.filesTotal += destOnlyFiles.length;
      await this.oneWaySync(task, destOnlyFiles, sourceFileMap, syncContext, 'dest-to-source');
    }
  }

  async mirrorSync(task, sourceFiles, destFileMap, syncContext) {
    // Copy all source files
    await this.oneWaySync(task, sourceFiles, destFileMap, syncContext, 'source-to-dest');
    
    // Delete extra files in destination (if option enabled)
    if (task.options?.deleteExtraFiles) {
      const sourceFileMap = new Map(sourceFiles.map(f => [f.relativePath, f]));
      
      for (const [relativePath, destFile] of destFileMap) {
        if (!sourceFileMap.has(relativePath)) {
          await this.deleteFile(task.destination_path, relativePath);
        }
      }
    }
  }

  async incrementalSync(task, sourceFiles, destFileMap, syncContext) {
    // Only sync files that are newer or don't exist
    const filesToSync = [];

    for (const sourceFile of sourceFiles) {
      const destFile = destFileMap.get(sourceFile.relativePath);

      if (!destFile || sourceFile.mtime > destFile.mtime) {
        filesToSync.push(sourceFile);
      }
    }

    syncContext.filesTotal = filesToSync.length;
    await this.oneWaySync(task, filesToSync, destFileMap, syncContext, 'source-to-dest');
  }

  async todayOnlySync(task, sourceFiles, destFileMap, syncContext) {
    // Only sync files that were created today
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000); // Next day

    console.log(`📅 Today-only sync: filtering files created between ${todayStart.toISOString()} and ${todayEnd.toISOString()}`);

    const todayFiles = [];

    for (const sourceFile of sourceFiles) {
      // Check if file was created today
      // Use birthtime (creation time) if available, otherwise use mtime (modification time)
      const fileCreationTime = new Date(sourceFile.birthtime || sourceFile.mtime);
      const fileMtime = new Date(sourceFile.mtime);

      // For today-only sync, we check both creation time and modification time
      // If either indicates the file was touched today, include it
      const createdToday = fileCreationTime >= todayStart && fileCreationTime < todayEnd;
      const modifiedToday = fileMtime >= todayStart && fileMtime < todayEnd;

      if (createdToday || modifiedToday) {
        todayFiles.push(sourceFile);
        console.log(`📅 Including today's file: ${sourceFile.relativePath}`);
        console.log(`   📊 Created: ${fileCreationTime.toISOString()} (today: ${createdToday})`);
        console.log(`   📊 Modified: ${fileMtime.toISOString()} (today: ${modifiedToday})`);
      } else {
        console.log(`⏭️ Skipping file: ${sourceFile.relativePath}`);
        console.log(`   📊 Created: ${fileCreationTime.toISOString()}`);
        console.log(`   📊 Modified: ${fileMtime.toISOString()}`);
      }
    }

    console.log(`📊 Today-only sync: ${todayFiles.length} out of ${sourceFiles.length} files qualify`);

    syncContext.filesTotal = todayFiles.length;

    if (todayFiles.length > 0) {
      await this.oneWaySync(task, todayFiles, destFileMap, syncContext, 'source-to-dest');
    } else {
      console.log(`📅 No files created today found in source directory`);
    }
  }

  async shouldUpdateFile(sourceFile, destFile) {
    if (!destFile) return true;
    
    // Compare modification times
    if (sourceFile.mtime > destFile.mtime) return true;
    
    // Compare file sizes
    if (sourceFile.size !== destFile.size) return true;
    
    return false;
  }

  async copyFile(task, file, syncContext, direction) {
    const sourcePath = direction === 'source-to-dest' ? file.path : 
                      path.join(task.source_path, file.relativePath);
    const destPath = direction === 'source-to-dest' ? 
                    path.join(task.destination_path, file.relativePath) :
                    path.join(task.destination_path, file.relativePath);
    
    try {
      // Ensure destination directory exists
      await fs.mkdir(path.dirname(destPath), { recursive: true });
      
      // Copy file
      await fs.copyFile(sourcePath, destPath);
      
      // Preserve timestamps if option enabled
      if (task.options?.preserveTimestamps) {
        const stat = await fs.stat(sourcePath);
        await fs.utimes(destPath, stat.atime, stat.mtime);
      }
      
      syncContext.bytesTransferred += file.size;
      
      console.log(`📁 Copied: ${file.relativePath}`);
      
    } catch (error) {
      const errorMsg = `Failed to copy ${file.relativePath}: ${error.message}`;
      syncContext.errors.push(errorMsg);
      console.error(`❌ ${errorMsg}`);
    }
  }

  async deleteFile(basePath, relativePath) {
    const fullPath = path.join(basePath, relativePath);
    
    try {
      await fs.unlink(fullPath);
      console.log(`🗑️ Deleted: ${relativePath}`);
    } catch (error) {
      console.error(`❌ Failed to delete ${relativePath}: ${error.message}`);
    }
  }

  getSyncStats(syncContext) {
    return {
      filesProcessed: syncContext.filesProcessed,
      filesTotal: syncContext.filesTotal,
      bytesTransferred: syncContext.bytesTransferred,
      duration: syncContext.duration,
      errors: syncContext.errors,
      status: syncContext.status
    };
  }

  getActiveSyncs() {
    const result = {};
    for (const [taskId, context] of this.activeSyncs) {
      result[taskId] = this.getSyncStats(context);
    }
    return result;
  }
}

module.exports = FileSyncEngine;
