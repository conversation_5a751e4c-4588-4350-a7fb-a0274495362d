const EventEmitter = require('events');

class BandwidthThrottler extends EventEmitter {
  constructor(options = {}) {
    super();
    
    // Configuration
    this.maxBytesPerSecond = options.maxBytesPerSecond || 1024 * 1024; // 1MB/s default
    this.burstSize = options.burstSize || this.maxBytesPerSecond * 2; // 2 seconds burst
    this.windowSize = options.windowSize || 1000; // 1 second window
    
    // State tracking
    this.bytesTransferred = 0;
    this.windowStart = Date.now();
    this.tokenBucket = this.burstSize; // Start with full bucket
    this.lastRefill = Date.now();
    
    // Active transfers
    this.activeTransfers = new Map();
    this.transferQueue = [];
    
    // Statistics
    this.stats = {
      totalBytesTransferred: 0,
      totalTransfers: 0,
      averageSpeed: 0,
      currentSpeed: 0,
      throttledTime: 0
    };
    
    // Start monitoring
    this.startMonitoring();
  }

  // Start bandwidth monitoring
  startMonitoring() {
    this.monitorInterval = setInterval(() => {
      this.updateStats();
      this.refillTokenBucket();
      this.processQueue();
    }, 100); // Check every 100ms
  }

  // Stop monitoring
  stop() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
  }

  // Request bandwidth for a transfer
  async requestBandwidth(transferId, bytes, priority = 'normal') {
    return new Promise((resolve) => {
      const request = {
        transferId,
        bytes,
        priority,
        timestamp: Date.now(),
        resolve
      };

      // Check if we can serve immediately
      if (this.canServeImmediately(bytes)) {
        this.consumeTokens(bytes);
        this.recordTransfer(transferId, bytes);
        resolve(true);
      } else {
        // Add to queue
        this.addToQueue(request);
      }
    });
  }

  // Check if request can be served immediately
  canServeImmediately(bytes) {
    this.refillTokenBucket();
    return this.tokenBucket >= bytes;
  }

  // Add request to queue with priority
  addToQueue(request) {
    // Insert based on priority
    const priorityOrder = { 'high': 0, 'normal': 1, 'low': 2 };
    const requestPriority = priorityOrder[request.priority] || 1;
    
    let insertIndex = this.transferQueue.length;
    for (let i = 0; i < this.transferQueue.length; i++) {
      const queuePriority = priorityOrder[this.transferQueue[i].priority] || 1;
      if (requestPriority < queuePriority) {
        insertIndex = i;
        break;
      }
    }
    
    this.transferQueue.splice(insertIndex, 0, request);
    
    this.emit('queued', {
      transferId: request.transferId,
      queuePosition: insertIndex,
      queueLength: this.transferQueue.length
    });
  }

  // Process queued requests
  processQueue() {
    while (this.transferQueue.length > 0) {
      const request = this.transferQueue[0];
      
      if (this.canServeImmediately(request.bytes)) {
        this.transferQueue.shift();
        this.consumeTokens(request.bytes);
        this.recordTransfer(request.transferId, request.bytes);
        request.resolve(true);
        
        this.emit('dequeued', {
          transferId: request.transferId,
          queueLength: this.transferQueue.length
        });
      } else {
        break; // Can't serve this request yet
      }
    }
  }

  // Refill token bucket
  refillTokenBucket() {
    const now = Date.now();
    const timePassed = now - this.lastRefill;
    
    if (timePassed > 0) {
      const tokensToAdd = (timePassed / 1000) * this.maxBytesPerSecond;
      this.tokenBucket = Math.min(this.burstSize, this.tokenBucket + tokensToAdd);
      this.lastRefill = now;
    }
  }

  // Consume tokens from bucket
  consumeTokens(bytes) {
    this.tokenBucket = Math.max(0, this.tokenBucket - bytes);
  }

  // Record transfer for statistics
  recordTransfer(transferId, bytes) {
    const now = Date.now();
    
    this.activeTransfers.set(transferId, {
      bytes,
      timestamp: now
    });

    this.stats.totalBytesTransferred += bytes;
    this.stats.totalTransfers++;
    
    // Update current window
    this.bytesTransferred += bytes;
  }

  // Complete a transfer
  completeTransfer(transferId) {
    this.activeTransfers.delete(transferId);
    
    this.emit('transferComplete', {
      transferId,
      activeTransfers: this.activeTransfers.size
    });
  }

  // Update statistics
  updateStats() {
    const now = Date.now();
    const windowElapsed = now - this.windowStart;
    
    if (windowElapsed >= this.windowSize) {
      // Calculate current speed
      this.stats.currentSpeed = (this.bytesTransferred / windowElapsed) * 1000; // bytes per second
      
      // Calculate average speed
      if (this.stats.totalTransfers > 0) {
        this.stats.averageSpeed = this.stats.totalBytesTransferred / 
          ((now - this.startTime) / 1000);
      }
      
      // Reset window
      this.bytesTransferred = 0;
      this.windowStart = now;
      
      this.emit('statsUpdate', this.getStats());
    }
  }

  // Get current statistics
  getStats() {
    return {
      ...this.stats,
      queueLength: this.transferQueue.length,
      activeTransfers: this.activeTransfers.size,
      tokenBucket: this.tokenBucket,
      maxBytesPerSecond: this.maxBytesPerSecond,
      currentSpeedMbps: (this.stats.currentSpeed / (1024 * 1024)).toFixed(2),
      averageSpeedMbps: (this.stats.averageSpeed / (1024 * 1024)).toFixed(2)
    };
  }

  // Update bandwidth limit
  setBandwidthLimit(bytesPerSecond) {
    this.maxBytesPerSecond = bytesPerSecond;
    this.burstSize = bytesPerSecond * 2;
    
    this.emit('limitChanged', {
      maxBytesPerSecond: this.maxBytesPerSecond,
      burstSize: this.burstSize
    });
  }

  // Pause all transfers
  pause() {
    this.paused = true;
    this.emit('paused');
  }

  // Resume transfers
  resume() {
    this.paused = false;
    this.emit('resumed');
    this.processQueue();
  }

  // Clear queue
  clearQueue() {
    const clearedCount = this.transferQueue.length;
    
    // Reject all queued requests
    this.transferQueue.forEach(request => {
      request.resolve(false);
    });
    
    this.transferQueue = [];
    
    this.emit('queueCleared', { clearedCount });
  }

  // Get queue status
  getQueueStatus() {
    return {
      length: this.transferQueue.length,
      items: this.transferQueue.map((request, index) => ({
        transferId: request.transferId,
        bytes: request.bytes,
        priority: request.priority,
        position: index,
        waitTime: Date.now() - request.timestamp
      }))
    };
  }

  // Estimate wait time for a request
  estimateWaitTime(bytes) {
    let totalBytesAhead = 0;
    
    for (const request of this.transferQueue) {
      totalBytesAhead += request.bytes;
    }
    
    totalBytesAhead += bytes;
    
    // Estimate based on current speed or max speed
    const effectiveSpeed = Math.max(this.stats.currentSpeed, this.maxBytesPerSecond * 0.5);
    
    return Math.ceil(totalBytesAhead / effectiveSpeed * 1000); // milliseconds
  }

  // Create a throttled stream wrapper
  createThrottledStream(transferId, priority = 'normal') {
    const { Transform } = require('stream');
    
    return new Transform({
      transform: async (chunk, encoding, callback) => {
        try {
          await this.requestBandwidth(transferId, chunk.length, priority);
          callback(null, chunk);
        } catch (error) {
          callback(error);
        }
      },
      
      flush: (callback) => {
        this.completeTransfer(transferId);
        callback();
      }
    });
  }
}

module.exports = BandwidthThrottler;
