// Test database writing for real-time sync
// This will help identify if the issue is with database insertion

const axios = require('axios');

const baseURL = 'http://localhost:5002/api';

class DatabaseWritingTester {
  constructor() {
    this.token = null;
  }

  async runTest() {
    console.log('🗄️ Database Writing Test for Real-time Sync\n');
    
    try {
      await this.step1_Login();
      await this.step2_TestRegularSync();
      await this.step3_TestRealtimeSync();
      await this.step4_CompareResults();
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
    }
  }

  async step1_Login() {
    console.log('1. 🔐 Logging in...');
    
    const response = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    this.token = response.data.token;
    console.log('✅ Login successful\n');
  }

  async step2_TestRegularSync() {
    console.log('2. 🔄 Testing Regular Sync Database Writing...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };
    
    // Get tasks
    const tasksResponse = await axios.get(`${baseURL}/sync/tasks`, { headers });
    const tasks = tasksResponse.data.tasks || [];
    
    if (tasks.length === 0) {
      console.log('❌ No tasks found for testing');
      return;
    }
    
    const task = tasks[0];
    console.log(`🎯 Testing with task: ${task.name} (ID: ${task.id})`);
    
    // Check history before
    const historyBefore = await this.getHistory(task.id);
    console.log(`📊 History before regular sync: ${historyBefore.length} entries`);
    
    try {
      // Start regular sync
      console.log('🚀 Starting regular sync...');
      const startResponse = await axios.post(
        `${baseURL}/sync/tasks/${task.id}/start`, 
        {}, 
        { headers }
      );
      
      console.log('✅ Regular sync started:', startResponse.data);
      
      // Wait for completion
      await this.delay(5000);
      
      // Check history after
      const historyAfter = await this.getHistory(task.id);
      console.log(`📊 History after regular sync: ${historyAfter.length} entries`);
      
      const newEntries = historyAfter.length - historyBefore.length;
      console.log(`📈 New history entries: ${newEntries}`);
      
      if (newEntries > 0) {
        console.log('✅ Regular sync DOES create history entries');
        const latestEntry = historyAfter[0];
        console.log(`📄 Latest entry: ${latestEntry.status} - ${latestEntry.files_processed} files`);
      } else {
        console.log('❌ Regular sync did NOT create history entries');
      }
      
    } catch (error) {
      console.log('❌ Regular sync failed:', error.message);
    }
    
    console.log('');
  }

  async step3_TestRealtimeSync() {
    console.log('3. ⚡ Testing Real-time Sync Database Writing...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };
    
    // Get real-time enabled tasks
    const tasksResponse = await axios.get(`${baseURL}/sync/tasks`, { headers });
    const tasks = tasksResponse.data.tasks || [];
    
    const realtimeTasks = tasks.filter(task => {
      const options = typeof task.options === 'string' ? JSON.parse(task.options) : task.options;
      return options?.enableRealtime;
    });
    
    if (realtimeTasks.length === 0) {
      console.log('❌ No real-time tasks found');
      console.log('💡 Create a task with real-time enabled first');
      return;
    }
    
    const task = realtimeTasks[0];
    console.log(`🎯 Testing with real-time task: ${task.name} (ID: ${task.id})`);
    
    // Check history before
    const historyBefore = await this.getHistory(task.id);
    console.log(`📊 History before real-time sync: ${historyBefore.length} entries`);
    
    try {
      // Start real-time sync
      console.log('🚀 Starting real-time sync...');
      const startResponse = await axios.post(
        `${baseURL}/sync/tasks/${task.id}/realtime/start`, 
        {}, 
        { headers }
      );
      
      console.log('✅ Real-time sync started:', startResponse.data);
      
      // Wait for file processing (simulate file changes)
      console.log('⏳ Waiting for real-time processing...');
      await this.delay(10000);
      
      // Check history after
      const historyAfter = await this.getHistory(task.id);
      console.log(`📊 History after real-time sync: ${historyAfter.length} entries`);
      
      const newEntries = historyAfter.length - historyBefore.length;
      console.log(`📈 New history entries: ${newEntries}`);
      
      if (newEntries > 0) {
        console.log('✅ Real-time sync DOES create history entries');
        const latestEntry = historyAfter[0];
        console.log(`📄 Latest entry: ${latestEntry.status} - ${latestEntry.files_processed} files`);
        
        // Check if it's marked as real-time
        if (latestEntry.details) {
          const details = JSON.parse(latestEntry.details);
          console.log(`🏷️ Entry type: ${details.type || 'unknown'}`);
        }
      } else {
        console.log('❌ Real-time sync did NOT create history entries');
        console.log('🔍 This confirms the database writing issue!');
      }
      
      // Stop real-time sync
      await axios.post(
        `${baseURL}/sync/tasks/${task.id}/realtime/stop`, 
        {}, 
        { headers }
      );
      
    } catch (error) {
      console.log('❌ Real-time sync failed:', error.message);
      if (error.response) {
        console.log('Response:', error.response.data);
      }
    }
    
    console.log('');
  }

  async step4_CompareResults() {
    console.log('4. 📊 Comparing Results...');
    
    // Get all recent history
    const allHistory = await this.getHistory();
    console.log(`📋 Total recent history entries: ${allHistory.length}`);
    
    if (allHistory.length > 0) {
      console.log('\n📄 Recent entries:');
      allHistory.slice(0, 5).forEach((entry, index) => {
        const details = entry.details ? JSON.parse(entry.details) : {};
        const type = details.type || 'regular';
        console.log(`   ${index + 1}. Task ${entry.task_id}: ${entry.status} - ${entry.files_processed} files - Type: ${type}`);
      });
      
      // Count by type
      const regularEntries = allHistory.filter(entry => {
        const details = entry.details ? JSON.parse(entry.details) : {};
        return !details.type || details.type === 'regular';
      });
      
      const realtimeEntries = allHistory.filter(entry => {
        const details = entry.details ? JSON.parse(entry.details) : {};
        return details.type === 'realtime' || details.type === 'realtime-batch';
      });
      
      console.log(`\n📊 Entry breakdown:`);
      console.log(`   Regular sync entries: ${regularEntries.length}`);
      console.log(`   Real-time sync entries: ${realtimeEntries.length}`);
      
      if (regularEntries.length > 0 && realtimeEntries.length === 0) {
        console.log('\n🎯 DIAGNOSIS: Real-time sync is NOT writing to database');
        console.log('💡 Possible causes:');
        console.log('   1. Event listener not triggering');
        console.log('   2. Database query failing silently');
        console.log('   3. Transaction rollback');
        console.log('   4. Permission issues');
        console.log('   5. Event data missing required fields');
      } else if (realtimeEntries.length > 0) {
        console.log('\n✅ SUCCESS: Real-time sync IS writing to database');
      }
    }
  }

  async getHistory(taskId = null) {
    const headers = { 'Authorization': `Bearer ${this.token}` };
    const url = taskId 
      ? `${baseURL}/sync/history?taskId=${taskId}&limit=20`
      : `${baseURL}/sync/history?limit=20`;
    
    const response = await axios.get(url, { headers });
    return response.data.history || [];
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Manual testing functions
module.exports = {
  runTest: async () => {
    const tester = new DatabaseWritingTester();
    await tester.runTest();
  },
  
  checkDatabaseConnection: async () => {
    console.log('🔍 Checking Database Connection...\n');
    
    try {
      const loginResponse = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      const token = loginResponse.data.token;
      const headers = { 'Authorization': `Bearer ${token}` };
      
      // Test basic queries
      const endpoints = [
        { url: `${baseURL}/sync/tasks`, name: 'Tasks' },
        { url: `${baseURL}/sync/history`, name: 'History' },
        { url: `${baseURL}/sync/history?limit=1`, name: 'Limited History' }
      ];
      
      for (const endpoint of endpoints) {
        try {
          const response = await axios.get(endpoint.url, { headers });
          console.log(`✅ ${endpoint.name}: ${response.status} - ${JSON.stringify(response.data).length} bytes`);
        } catch (error) {
          console.log(`❌ ${endpoint.name}: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.error('❌ Database connection test failed:', error.message);
    }
  },
  
  testManualHistoryInsert: async () => {
    console.log('🧪 Testing Manual History Insert...\n');
    
    // This would test if we can manually insert a history entry
    // to isolate if the issue is with the SQL query itself
    
    console.log('💡 This test would require direct database access');
    console.log('   Check server logs for SQL errors during real-time sync');
    console.log('   Look for "Real-time sync history created" messages');
  }
};

// Auto-run if called directly
if (require.main === module) {
  const tester = new DatabaseWritingTester();
  tester.runTest();
}

console.log('🎯 Database Writing Tester loaded!');
console.log('📝 Run: node scripts/test-database-writing.js');
console.log('📝 Or: require("./scripts/test-database-writing").checkDatabaseConnection()');
