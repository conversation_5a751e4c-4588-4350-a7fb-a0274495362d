const axios = require('axios');

async function checkClientsViaAPI() {
  console.log('🔍 Checking Clients via API\n');

  try {
    // Test both servers
    const servers = [
      { name: 'Desktop Server', url: 'http://localhost:5002' },
      { name: 'Web Server', url: 'http://localhost:5001' }
    ];

    for (const server of servers) {
      console.log(`\n🔍 Testing ${server.name} (${server.url}):`);
      console.log('=' .repeat(60));

      try {
        // Check if server is running
        const healthResponse = await axios.get(`${server.url}/api/health`, { timeout: 5000 });
        console.log(`   ✅ Server Status: ${healthResponse.data.status}`);

        // Try to login
        const loginResponse = await axios.post(`${server.url}/api/auth/login`, {
          email: '<EMAIL>',
          password: 'password123'
        }, { timeout: 10000 });

        const token = loginResponse.data.token;
        console.log(`   ✅ Login successful`);

        // Get clients
        const clientsResponse = await axios.get(`${server.url}/api/clients`, {
          headers: { Authorization: `Bearer ${token}` },
          timeout: 10000
        });

        const clients = clientsResponse.data.clients || [];
        console.log(`   📋 Total clients: ${clients.length}`);

        if (clients.length === 0) {
          console.log(`   📭 No clients found on ${server.name}`);
        } else {
          console.log(`   📊 Client Details:`);
          clients.forEach((client, index) => {
            console.log(`      ${index + 1}. ${client.client_id}`);
            console.log(`         🏠 Hostname: ${client.hostname}`);
            console.log(`         📊 Status: ${client.status}`);
            console.log(`         👤 User ID: ${client.user_id}`);
            console.log(`         👁️ Last Seen: ${client.last_seen}`);
            console.log(`         📅 Created: ${client.created_at}`);
            console.log('');
          });

          // Summary by status
          const statusCounts = {};
          clients.forEach(client => {
            statusCounts[client.status] = (statusCounts[client.status] || 0) + 1;
          });

          console.log(`   📊 Status Summary:`);
          Object.entries(statusCounts).forEach(([status, count]) => {
            const emoji = status === 'online' ? '🟢' : status === 'offline' ? '🔴' : '⚪';
            console.log(`      ${emoji} ${status}: ${count} clients`);
          });
        }

      } catch (error) {
        if (error.code === 'ECONNREFUSED') {
          console.log(`   ❌ ${server.name} not running`);
        } else if (error.response) {
          console.log(`   ❌ API Error: ${error.response.status} - ${error.response.data?.message || error.response.statusText}`);
        } else {
          console.log(`   ❌ Error: ${error.message}`);
        }
      }
    }

    // Summary
    console.log('\n📋 SUMMARY:');
    console.log('=' .repeat(60));
    console.log('💡 To see client data:');
    console.log('   1. Start desktop server: npm run start-desktop');
    console.log('   2. Start web server: npm run start-web-management');
    console.log('   3. Login to desktop app to register client');
    console.log('   4. Check web-ui Client Management page');

    console.log('\n🔧 Troubleshooting:');
    console.log('   - If no servers running → Start servers first');
    console.log('   - If login fails → Check user credentials');
    console.log('   - If no clients → Login to desktop app first');
    console.log('   - If clients show wrong status → Check logout fix');

  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

// Run check
checkClientsViaAPI();
