const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { getDatabase } = require('../database/init');

class SyncEngine {
  constructor(io) {
    this.io = io;
    this.activeSyncs = new Map();
  }

  // Calculate file checksum
  async calculateChecksum(filePath) {
    try {
      const data = await fs.readFile(filePath);
      return crypto.createHash('md5').update(data).digest('hex');
    } catch (error) {
      return null;
    }
  }

  // Get file stats
  async getFileStats(filePath) {
    try {
      const stats = await fs.stat(filePath);
      return {
        size: stats.size,
        mtime: stats.mtime,
        isDirectory: stats.isDirectory(),
        isFile: stats.isFile()
      };
    } catch (error) {
      return null;
    }
  }

  // Scan directory recursively
  async scanDirectory(dirPath, filters = []) {
    const files = [];
    
    try {
      const items = await fs.readdir(dirPath);
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stats = await this.getFileStats(fullPath);
        
        if (!stats) continue;

        // Check filters
        if (this.shouldSkipFile(item, filters)) continue;

        if (stats.isDirectory) {
          // Recursively scan subdirectories
          const subFiles = await this.scanDirectory(fullPath, filters);
          files.push(...subFiles);
        } else if (stats.isFile) {
          const checksum = await this.calculateChecksum(fullPath);
          files.push({
            path: fullPath,
            relativePath: path.relative(dirPath, fullPath).replace(/\\/g, '/'), // Normalize path separators
            size: stats.size,
            mtime: stats.mtime,
            checksum
          });
        }
      }
    } catch (error) {
      console.error('Error scanning directory:', error);
    }

    return files;
  }

  // Check if file should be skipped based on filters
  shouldSkipFile(fileName, filters) {
    for (const filter of filters) {
      if (filter.startsWith('*.')) {
        const ext = filter.substring(1);
        if (fileName.endsWith(ext)) return true;
      } else if (filter.includes('*')) {
        const regex = new RegExp(filter.replace(/\*/g, '.*'));
        if (regex.test(fileName)) return true;
      } else if (fileName === filter) {
        return true;
      }
    }
    return false;
  }

  // Copy file with progress
  async copyFile(sourcePath, destPath, onProgress) {
    try {
      // Ensure destination directory exists
      await fs.mkdir(path.dirname(destPath), { recursive: true });
      
      // Copy file
      await fs.copyFile(sourcePath, destPath);
      
      // Preserve timestamps
      const stats = await fs.stat(sourcePath);
      await fs.utimes(destPath, stats.atime, stats.mtime);
      
      if (onProgress) onProgress();
      
      return true;
    } catch (error) {
      console.error('Error copying file:', error);
      return false;
    }
  }

  // Delete file
  async deleteFile(filePath) {
    try {
      await fs.unlink(filePath);
      return true;
    } catch (error) {
      console.error('Error deleting file:', error);
      return false;
    }
  }

  // Compare file lists and determine changes
  compareFiles(sourceFiles, destFiles) {
    const sourceMap = new Map(sourceFiles.map(f => [f.relativePath, f]));
    const destMap = new Map(destFiles.map(f => [f.relativePath, f]));
    
    const changes = {
      toAdd: [],
      toUpdate: [],
      toDelete: []
    };

    // Find files to add or update
    for (const [relativePath, sourceFile] of sourceMap) {
      const destFile = destMap.get(relativePath);
      
      if (!destFile) {
        changes.toAdd.push(sourceFile);
      } else if (sourceFile.checksum !== destFile.checksum) {
        changes.toUpdate.push(sourceFile);
      }
    }

    // Find files to delete (only if deleteExtraFiles is enabled)
    for (const [relativePath, destFile] of destMap) {
      if (!sourceMap.has(relativePath)) {
        changes.toDelete.push(destFile);
      }
    }

    return changes;
  }

  // Start sync task
  async startSync(taskId, userId) {
    if (this.activeSyncs.has(taskId)) {
      throw new Error('Sync task is already running');
    }

    const db = getDatabase();
    
    try {
      // Get task details
      const taskResult = await db.query(
        'SELECT * FROM sync_tasks WHERE id = ? AND user_id = ?',
        [taskId, userId]
      );

      if (taskResult.rows.length === 0) {
        throw new Error('Sync task not found');
      }

      const task = taskResult.rows[0];
      const options = typeof task.options === 'string' ? JSON.parse(task.options) : task.options;
      const filters = typeof task.filters === 'string' ? JSON.parse(task.filters) : task.filters;

      // Mark as active
      this.activeSyncs.set(taskId, { status: 'running', startTime: Date.now() });

      // Update task status
      await db.query(
        'UPDATE sync_tasks SET status = ?, last_sync = CURRENT_TIMESTAMP WHERE id = ?',
        ['running', taskId]
      );

      // Create history entry
      const historyResult = await db.query(
        'INSERT INTO sync_history (task_id, user_id, status, started_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP)',
        [taskId, userId, 'running']
      );
      const historyId = historyResult.insertId;

      // Emit start event
      this.io.to(`user-${userId}`).emit('sync-started', {
        taskId,
        taskName: task.name,
        status: 'running'
      });

      // Perform sync
      const result = await this.performSync(task, options, filters, userId, historyId);

      // Update completion
      await db.query(
        'UPDATE sync_history SET status = ?, completed_at = CURRENT_TIMESTAMP, files_processed = ?, files_added = ?, files_updated = ?, files_deleted = ?, total_size = ? WHERE id = ?',
        [result.status, result.filesProcessed, result.filesAdded, result.filesUpdated, result.filesDeleted, result.totalSize, historyId]
      );

      await db.query(
        'UPDATE sync_tasks SET status = ?, files_count = ?, total_size = ? WHERE id = ?',
        ['idle', result.filesProcessed, result.totalSize, taskId]
      );

      // Emit completion event
      this.io.to(`user-${userId}`).emit('sync-completed', {
        taskId,
        taskName: task.name,
        status: result.status,
        result
      });

      return result;

    } catch (error) {
      console.error('Sync error:', error);
      
      // Update error status
      await db.query(
        'UPDATE sync_tasks SET status = ? WHERE id = ?',
        ['error', taskId]
      );

      // Emit error event
      this.io.to(`user-${userId}`).emit('sync-error', {
        taskId,
        error: error.message
      });

      throw error;
    } finally {
      this.activeSyncs.delete(taskId);
    }
  }

  // Perform actual sync
  async performSync(task, options, filters, userId, historyId) {
    const sourcePath = task.source_path;
    const destPath = task.destination_path;
    const syncType = task.sync_type;

    let filesProcessed = 0;
    let filesAdded = 0;
    let filesUpdated = 0;
    let filesDeleted = 0;
    let totalSize = 0;

    // Scan source directory
    console.log('Scanning source directory:', sourcePath);
    const sourceFiles = await this.scanDirectory(sourcePath, filters);
    
    // Scan destination directory
    console.log('Scanning destination directory:', destPath);
    const destFiles = await this.scanDirectory(destPath, filters);

    // Compare and determine changes
    const changes = this.compareFiles(sourceFiles, destFiles);

    console.log('Changes detected:', {
      toAdd: changes.toAdd.length,
      toUpdate: changes.toUpdate.length,
      toDelete: changes.toDelete.length
    });

    // Process additions
    for (const file of changes.toAdd) {
      const destFilePath = path.join(destPath, file.relativePath);
      const success = await this.copyFile(file.path, destFilePath, () => {
        filesProcessed++;
        totalSize += file.size;
        
        // Emit progress
        this.io.to(`user-${userId}`).emit('sync-progress', {
          taskId: task.id,
          filesProcessed,
          totalFiles: changes.toAdd.length + changes.toUpdate.length,
          currentFile: file.relativePath
        });
      });

      if (success) {
        filesAdded++;
        
        // Log file change
        await this.logFileChange(task.id, file.relativePath, 'added', file.size, file.checksum);
      }
    }

    // Process updates
    for (const file of changes.toUpdate) {
      const destFilePath = path.join(destPath, file.relativePath);
      const success = await this.copyFile(file.path, destFilePath, () => {
        filesProcessed++;
        totalSize += file.size;
        
        // Emit progress
        this.io.to(`user-${userId}`).emit('sync-progress', {
          taskId: task.id,
          filesProcessed,
          totalFiles: changes.toAdd.length + changes.toUpdate.length,
          currentFile: file.relativePath
        });
      });

      if (success) {
        filesUpdated++;
        
        // Log file change
        await this.logFileChange(task.id, file.relativePath, 'updated', file.size, file.checksum);
      }
    }

    // Process deletions (if enabled)
    if (options.deleteExtraFiles) {
      for (const file of changes.toDelete) {
        const destFilePath = path.join(destPath, file.relativePath);
        const success = await this.deleteFile(destFilePath);

        if (success) {
          filesDeleted++;
          
          // Log file change
          await this.logFileChange(task.id, file.relativePath, 'deleted', file.size, file.checksum);
        }
      }
    }

    return {
      status: 'completed',
      filesProcessed,
      filesAdded,
      filesUpdated,
      filesDeleted,
      totalSize
    };
  }

  // Log file change
  async logFileChange(taskId, filePath, changeType, fileSize, checksum) {
    try {
      const db = getDatabase();
      await db.query(
        'INSERT INTO file_changes (task_id, file_path, change_type, file_size, checksum) VALUES (?, ?, ?, ?, ?)',
        [taskId, filePath, changeType, fileSize, checksum]
      );
    } catch (error) {
      console.error('Error logging file change:', error);
    }
  }

  // Stop sync task
  async stopSync(taskId) {
    if (this.activeSyncs.has(taskId)) {
      this.activeSyncs.delete(taskId);
      
      const db = getDatabase();
      await db.query(
        'UPDATE sync_tasks SET status = ? WHERE id = ?',
        ['idle', taskId]
      );
      
      return true;
    }
    return false;
  }

  // Get active syncs
  getActiveSyncs() {
    return Array.from(this.activeSyncs.keys());
  }
}

module.exports = SyncEngine;
