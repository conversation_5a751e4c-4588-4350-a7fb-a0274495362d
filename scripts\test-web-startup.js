// Test script for web server startup
const { Pool } = require('pg');

async function testWebStartup() {
  console.log('🔄 Testing Web Server Startup\n');
  
  try {
    // 1. Test PostgreSQL connection
    console.log('1. 🐘 Testing PostgreSQL connection...');
    
    const pgPool = new Pool({
      user: 'pi',
      host: '*************',
      database: 'syncmasterpro',
      password: 'ubuntu',
      port: 5432,
    });
    
    const result = await pgPool.query('SELECT NOW() as current_time, version() as pg_version');
    console.log('✅ PostgreSQL connected successfully');
    console.log(`📊 Server time: ${result.rows[0].current_time}`);
    console.log(`📊 PostgreSQL version: ${result.rows[0].pg_version.split(' ')[0]}`);
    
    // 2. Test database tables
    console.log('\n2. 📋 Checking database tables...');
    
    const tables = ['users', 'sync_tasks', 'sync_history'];
    
    for (const table of tables) {
      try {
        const tableResult = await pgPool.query(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`✅ Table ${table}: ${tableResult.rows[0].count} records`);
      } catch (error) {
        console.log(`❌ Table ${table}: ${error.message}`);
      }
    }
    
    // 3. Test web server ports
    console.log('\n3. 🔌 Checking port availability...');
    
    const net = require('net');
    
    const checkPort = (port) => {
      return new Promise((resolve) => {
        const server = net.createServer();
        server.listen(port, () => {
          server.close(() => {
            resolve(true); // Port is available
          });
        });
        server.on('error', () => {
          resolve(false); // Port is in use
        });
      });
    };
    
    const webServerPort = await checkPort(5001);
    const webClientPort = await checkPort(5000);
    
    console.log(`📊 Web server port 5001: ${webServerPort ? '✅ Available' : '⚠️ In use'}`);
    console.log(`📊 Web client port 5000: ${webClientPort ? '✅ Available' : '⚠️ In use'}`);
    
    // 4. Environment check
    console.log('\n4. 🌍 Environment configuration...');
    
    const expectedEnv = {
      'DB_TYPE': 'postgresql',
      'DB_HOST': '*************',
      'DB_USER': 'pi',
      'DB_NAME': 'syncmasterpro',
      'PORT': '5001'
    };
    
    console.log('📋 Expected environment variables for web server:');
    Object.entries(expectedEnv).forEach(([key, value]) => {
      console.log(`   ${key}=${value}`);
    });
    
    // 5. Summary
    console.log('\n📊 WEB STARTUP TEST SUMMARY:');
    console.log(`   PostgreSQL connection: ✅ SUCCESS`);
    console.log(`   Database tables: ✅ ACCESSIBLE`);
    console.log(`   Port 5001 (server): ${webServerPort ? '✅ AVAILABLE' : '⚠️ IN USE'}`);
    console.log(`   Port 5000 (client): ${webClientPort ? '✅ AVAILABLE' : '⚠️ IN USE'}`);
    
    const allGood = webServerPort && webClientPort;
    
    console.log(`\n🎯 WEB STARTUP STATUS: ${allGood ? '✅ READY' : '⚠️ NEEDS ATTENTION'}`);
    
    if (allGood) {
      console.log('🎉 Web server is ready to start!');
      console.log('💡 Run: npm run start-web');
    } else {
      console.log('🔧 Some issues need to be resolved before starting web server');
      if (!webServerPort) {
        console.log('   - Port 5001 is in use (stop other servers)');
      }
      if (!webClientPort) {
        console.log('   - Port 5000 is in use (stop other React apps)');
      }
    }
    
    await pgPool.end();
    
  } catch (error) {
    console.error('❌ Web startup test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 PostgreSQL Connection Failed:');
      console.log('   - Check if PostgreSQL server is running on *************:5432');
      console.log('   - Verify credentials: user=pi, password=ubuntu');
      console.log('   - Ensure database "syncmasterpro" exists');
      console.log('   - Check network connectivity to *************');
    }
  }
}

// Export for manual use
module.exports = { testWebStartup };

// Auto-run if called directly
if (require.main === module) {
  testWebStartup();
}

console.log('🔄 Web Startup Tester loaded!');
console.log('📝 Run: node scripts/test-web-startup.js');
