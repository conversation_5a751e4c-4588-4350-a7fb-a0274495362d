// Extended Sync Engine with Client Manager Integration
const SyncEngine = require('./syncEngine');
const FileSyncEngine = require('./FileSyncEngine');
const ClientManager = require('./ClientManager');

class ExtendedSyncEngine extends FileSyncEngine {
  constructor(io) {
    super(io);
    this.clientManager = null;
    this.isClientMode = false;
  }

  // Initialize client manager for web server communication
  async initializeClientManager(user, token) {
    try {
      console.log('🔧 Initializing Client Manager...');
      
      this.clientManager = new ClientManager({
        webServerUrl: process.env.WEB_SERVER_URL || 'http://localhost:5001',
        onStatusUpdate: (status) => this.handleStatusUpdate(status),
        onCommandReceived: (command) => this.handleRemoteCommand(command),
        onConnectionChange: (connected) => this.handleConnectionChange(connected)
      });

      const success = await this.clientManager.initialize(user, token);
      
      if (success) {
        this.isClientMode = true;
        console.log('✅ Client Manager initialized successfully');
      } else {
        console.log('⚠️ Client Manager initialization failed, continuing in standalone mode');
      }

      return success;
      
    } catch (error) {
      console.error('❌ Client Manager initialization error:', error);
      return false;
    }
  }

  // Handle status updates
  handleStatusUpdate(status) {
    console.log('📊 Status updated:', status.timestamp);
    
    // Emit to local UI if needed
    if (this.io) {
      this.io.emit('client-status-update', {
        connected: this.clientManager?.isConnected || false,
        lastUpdate: status.timestamp
      });
    }
  }

  // Handle remote commands from web server
  async handleRemoteCommand(command) {
    try {
      console.log(`🎛️ Executing remote command: ${command.type}`);
      
      switch (command.type) {
        case 'start-sync':
          return await this.handleRemoteStartSync(command);
          
        case 'stop-sync':
          return await this.handleRemoteStopSync(command);
          
        case 'get-status':
          return await this.handleRemoteGetStatus(command);
          
        case 'get-tasks':
          return await this.handleRemoteGetTasks(command);
          
        case 'create-task':
          return await this.handleRemoteCreateTask(command);
          
        case 'update-task':
          return await this.handleRemoteUpdateTask(command);
          
        case 'delete-task':
          return await this.handleRemoteDeleteTask(command);
          
        default:
          throw new Error(`Unknown command type: ${command.type}`);
      }
      
    } catch (error) {
      console.error('❌ Remote command execution failed:', error);
      throw error;
    }
  }

  // Handle connection changes
  handleConnectionChange(connected) {
    console.log(`🔌 Connection status changed: ${connected ? 'Connected' : 'Disconnected'}`);
    
    // Emit to local UI
    if (this.io) {
      this.io.emit('web-server-connection', {
        connected,
        timestamp: new Date()
      });
    }
  }

  // Remote command handlers
  async handleRemoteStartSync(command) {
    const { taskId, userId } = command.data;
    
    console.log(`🚀 Remote start sync: Task ${taskId} for User ${userId}`);
    
    const result = await this.startSync(taskId, userId);
    
    // Report sync completion to web server
    if (this.clientManager) {
      await this.clientManager.sendEvent('sync-completed', {
        taskId,
        result,
        timestamp: new Date()
      });
    }
    
    return result;
  }

  async handleRemoteStopSync(command) {
    const { taskId } = command.data;
    
    console.log(`🛑 Remote stop sync: Task ${taskId}`);
    
    const result = await this.stopSync(taskId);
    
    // Report sync stop to web server
    if (this.clientManager) {
      await this.clientManager.sendEvent('sync-stopped', {
        taskId,
        result,
        timestamp: new Date()
      });
    }
    
    return result;
  }

  async handleRemoteGetStatus(command) {
    const activeSyncs = this.getActiveSyncs();
    
    const status = {
      activeSyncs,
      totalActiveSyncs: activeSyncs.length,
      systemInfo: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage()
      },
      timestamp: new Date()
    };
    
    console.log('📊 Remote status request completed');
    return status;
  }

  async handleRemoteGetTasks(command) {
    const { userId } = command.data;
    const { getDatabase } = require('../database/init');
    const db = getDatabase();
    
    const result = await db.query(
      'SELECT * FROM sync_tasks WHERE user_id = ? ORDER BY created_at DESC',
      [userId]
    );
    
    console.log(`📋 Remote get tasks: ${result.rows.length} tasks for User ${userId}`);
    return result.rows;
  }

  async handleRemoteCreateTask(command) {
    const { taskData, userId } = command.data;
    const { getDatabase } = require('../database/init');
    const db = getDatabase();
    
    const result = await db.query(
      `INSERT INTO sync_tasks (user_id, name, source_path, destination_path, sync_type, schedule, filters, options, status, client_id)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'idle', ?)`,
      [
        userId,
        taskData.name,
        taskData.source_path,
        taskData.destination_path,
        taskData.sync_type,
        taskData.schedule,
        JSON.stringify(taskData.filters || []),
        JSON.stringify(taskData.options || {}),
        this.clientManager?.clientId
      ]
    );
    
    console.log(`➕ Remote create task: ${taskData.name} for User ${userId}`);
    return { id: result.insertId, ...taskData };
  }

  async handleRemoteUpdateTask(command) {
    const { taskId, taskData, userId } = command.data;
    const { getDatabase } = require('../database/init');
    const db = getDatabase();
    
    await db.query(
      `UPDATE sync_tasks SET 
       name = ?, source_path = ?, destination_path = ?, sync_type = ?, 
       schedule = ?, filters = ?, options = ?, updated_at = CURRENT_TIMESTAMP
       WHERE id = ? AND user_id = ?`,
      [
        taskData.name,
        taskData.source_path,
        taskData.destination_path,
        taskData.sync_type,
        taskData.schedule,
        JSON.stringify(taskData.filters || []),
        JSON.stringify(taskData.options || {}),
        taskId,
        userId
      ]
    );
    
    console.log(`✏️ Remote update task: ${taskId} for User ${userId}`);
    return { id: taskId, ...taskData };
  }

  async handleRemoteDeleteTask(command) {
    const { taskId, userId } = command.data;
    const { getDatabase } = require('../database/init');
    const db = getDatabase();
    
    await db.query(
      'DELETE FROM sync_tasks WHERE id = ? AND user_id = ?',
      [taskId, userId]
    );
    
    console.log(`🗑️ Remote delete task: ${taskId} for User ${userId}`);
    return { deleted: true, taskId };
  }

  // Override startSync to report to web server
  async startSync(taskId, userId) {
    // Report sync start to web server
    if (this.clientManager) {
      await this.clientManager.sendEvent('sync-started', {
        taskId,
        userId,
        timestamp: new Date()
      });
    }

    try {
      const result = await super.startSync(taskId, userId);
      
      // Report sync completion to web server
      if (this.clientManager) {
        await this.clientManager.sendEvent('sync-completed', {
          taskId,
          userId,
          result,
          timestamp: new Date()
        });
      }
      
      return result;
      
    } catch (error) {
      // Report sync error to web server
      if (this.clientManager) {
        await this.clientManager.sendEvent('sync-error', {
          taskId,
          userId,
          error: error.message,
          timestamp: new Date()
        });
      }
      
      throw error;
    }
  }

  // Override stopSync to report to web server
  async stopSync(taskId) {
    const result = await super.stopSync(taskId);
    
    // Report sync stop to web server
    if (this.clientManager) {
      await this.clientManager.sendEvent('sync-stopped', {
        taskId,
        result,
        timestamp: new Date()
      });
    }
    
    return result;
  }

  // Get client connection status
  getClientStatus() {
    if (!this.clientManager) {
      return { mode: 'standalone', connected: false };
    }
    
    return {
      mode: 'client',
      ...this.clientManager.getConnectionStatus()
    };
  }

  // Disconnect from web server
  async disconnectFromWebServer() {
    if (this.clientManager) {
      await this.clientManager.disconnect();
      this.isClientMode = false;
      console.log('🔌 Disconnected from web server');
    }
  }
}

module.exports = ExtendedSyncEngine;
