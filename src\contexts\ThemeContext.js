import React, { createContext, useContext, useState, useEffect } from 'react';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState('system'); // 'light', 'dark', 'system'
  const [actualTheme, setActualTheme] = useState('light'); // The actual applied theme

  // Check for stored theme preference
  useEffect(() => {
    const getStoredTheme = async () => {
      try {
        let storedTheme = 'system';
        
        if (window.electronAPI) {
          // Desktop: use Electron store
          storedTheme = await window.electronAPI.store.get('theme') || 'system';
        } else {
          // Web: use localStorage
          storedTheme = localStorage.getItem('theme') || 'system';
        }
        
        setTheme(storedTheme);
      } catch (error) {
        console.error('Failed to load theme preference:', error);
        setTheme('system');
      }
    };

    getStoredTheme();
  }, []);

  // Apply theme changes
  useEffect(() => {
    const applyTheme = () => {
      let newActualTheme = theme;
      
      if (theme === 'system') {
        // Detect system preference
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        newActualTheme = systemPrefersDark ? 'dark' : 'light';
      }
      
      setActualTheme(newActualTheme);
      
      // Apply to document
      const root = document.documentElement;
      if (newActualTheme === 'dark') {
        root.classList.add('dark');
      } else {
        root.classList.remove('dark');
      }
      
      // Update meta theme-color for mobile browsers
      const metaThemeColor = document.querySelector('meta[name="theme-color"]');
      if (metaThemeColor) {
        metaThemeColor.setAttribute('content', newActualTheme === 'dark' ? '#1f2937' : '#667eea');
      }
    };

    applyTheme();

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = () => {
      if (theme === 'system') {
        applyTheme();
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);
  }, [theme]);

  const setThemePreference = async (newTheme) => {
    try {
      setTheme(newTheme);
      
      // Store preference
      if (window.electronAPI) {
        await window.electronAPI.store.set('theme', newTheme);
      } else {
        localStorage.setItem('theme', newTheme);
      }
    } catch (error) {
      console.error('Failed to save theme preference:', error);
    }
  };

  const toggleTheme = () => {
    const nextTheme = actualTheme === 'light' ? 'dark' : 'light';
    setThemePreference(nextTheme);
  };

  const value = {
    theme, // User preference: 'light', 'dark', 'system'
    actualTheme, // Actually applied theme: 'light' or 'dark'
    setTheme: setThemePreference,
    toggleTheme,
    isDark: actualTheme === 'dark',
    isLight: actualTheme === 'light',
    isSystem: theme === 'system'
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
