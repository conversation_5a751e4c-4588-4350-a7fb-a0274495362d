// Simple test script for real-time sync
// This will help us identify exactly where the issue is

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const baseURL = 'http://localhost:5002/api';

class SimpleRealtimeTest {
  constructor() {
    this.token = null;
    this.testTaskId = null;
  }

  async runTest() {
    console.log('🧪 Simple Real-time Sync Test\n');
    
    try {
      await this.step1_Login();
      await this.step2_CheckExistingTasks();
      await this.step3_CreateTestDirectories();
      await this.step4_CreateOrUpdateTask();
      await this.step5_StartRealtimeSync();
      await this.step6_CreateTestFile();
      await this.step7_CheckResults();
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
    }
  }

  async step1_Login() {
    console.log('1. 🔐 Logging in...');
    
    const response = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    this.token = response.data.token;
    console.log('✅ Login successful\n');
  }

  async step2_CheckExistingTasks() {
    console.log('2. 📋 Checking existing tasks...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };
    const response = await axios.get(`${baseURL}/sync/tasks`, { headers });
    
    const tasks = response.data.tasks || [];
    console.log(`📊 Found ${tasks.length} existing tasks`);
    
    // Look for real-time enabled tasks
    const realtimeTasks = tasks.filter(task => {
      const options = typeof task.options === 'string' ? JSON.parse(task.options) : task.options;
      return options?.enableRealtime;
    });
    
    console.log(`⚡ Real-time tasks: ${realtimeTasks.length}`);
    
    if (realtimeTasks.length > 0) {
      this.testTaskId = realtimeTasks[0].id;
      console.log(`🎯 Using existing real-time task: ${realtimeTasks[0].name} (ID: ${this.testTaskId})`);
    }
    
    console.log('');
  }

  async step3_CreateTestDirectories() {
    console.log('3. 📁 Creating test directories...');
    
    const testDir = path.join(process.cwd(), 'test-realtime');
    const sourceDir = path.join(testDir, 'source');
    const destDir = path.join(testDir, 'dest');
    
    // Create directories
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
    if (!fs.existsSync(sourceDir)) {
      fs.mkdirSync(sourceDir, { recursive: true });
    }
    if (!fs.existsSync(destDir)) {
      fs.mkdirSync(destDir, { recursive: true });
    }
    
    this.sourceDir = sourceDir;
    this.destDir = destDir;
    
    console.log(`📂 Source: ${sourceDir}`);
    console.log(`📂 Destination: ${destDir}`);
    console.log('');
  }

  async step4_CreateOrUpdateTask() {
    console.log('4. ➕ Creating/updating test task...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };
    
    if (!this.testTaskId) {
      // Create new task
      const taskData = {
        name: 'Real-time Test Task',
        sourcePath: this.sourceDir,
        destinationPath: this.destDir,
        syncType: 'bidirectional',
        options: {
          enableRealtime: true,
          deleteExtraFiles: false,
          preserveTimestamps: true
        }
      };

      const response = await axios.post(`${baseURL}/sync/tasks`, taskData, { headers });
      this.testTaskId = response.data.task.id;
      
      console.log(`✅ Created new task with ID: ${this.testTaskId}`);
    } else {
      console.log(`✅ Using existing task ID: ${this.testTaskId}`);
    }
    
    console.log('');
  }

  async step5_StartRealtimeSync() {
    console.log('5. ⚡ Starting real-time sync...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };
    
    try {
      const response = await axios.post(
        `${baseURL}/sync/tasks/${this.testTaskId}/realtime/start`, 
        {}, 
        { headers }
      );
      
      console.log('✅ Real-time sync started:', response.data);
      console.log(`📊 Task status: ${response.data.status}`);
      
      // Wait a bit for initialization
      await this.delay(2000);
      
    } catch (error) {
      console.log('❌ Failed to start real-time sync:', error.response?.data || error.message);
      throw error;
    }
    
    console.log('');
  }

  async step6_CreateTestFile() {
    console.log('6. 📄 Creating test file...');
    
    const testFileName = `test-${Date.now()}.txt`;
    const testFilePath = path.join(this.sourceDir, testFileName);
    const testContent = `Test file created at ${new Date().toISOString()}\nThis is a real-time sync test.`;
    
    console.log(`📝 Creating file: ${testFileName}`);
    fs.writeFileSync(testFilePath, testContent);
    
    console.log('✅ Test file created');
    console.log('⏳ Waiting 5 seconds for real-time sync...');
    
    await this.delay(5000);
    
    // Check if file was synced
    const destFilePath = path.join(this.destDir, testFileName);
    const fileSynced = fs.existsSync(destFilePath);
    
    console.log(`📊 File synced to destination: ${fileSynced ? '✅ YES' : '❌ NO'}`);
    
    if (fileSynced) {
      const destContent = fs.readFileSync(destFilePath, 'utf8');
      const contentMatches = destContent === testContent;
      console.log(`📊 Content matches: ${contentMatches ? '✅ YES' : '❌ NO'}`);
    }
    
    console.log('');
  }

  async step7_CheckResults() {
    console.log('7. 📊 Checking results...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };
    
    // Check history
    const historyResponse = await axios.get(
      `${baseURL}/sync/history?taskId=${this.testTaskId}&limit=10`, 
      { headers }
    );
    
    const history = historyResponse.data.history || [];
    console.log(`📋 History entries for task: ${history.length}`);
    
    if (history.length > 0) {
      console.log('✅ History entries found:');
      history.slice(0, 3).forEach((entry, index) => {
        console.log(`   ${index + 1}. ${entry.status} - ${entry.files_processed} files - ${entry.started_at}`);
      });
    } else {
      console.log('❌ No history entries found');
    }
    
    // Check all history
    const allHistoryResponse = await axios.get(`${baseURL}/sync/history?limit=10`, { headers });
    const allHistory = allHistoryResponse.data.history || [];
    console.log(`📋 Total recent history entries: ${allHistory.length}`);
    
    // Check task status
    const tasksResponse = await axios.get(`${baseURL}/sync/tasks`, { headers });
    const tasks = tasksResponse.data.tasks || [];
    const ourTask = tasks.find(t => t.id === this.testTaskId);
    
    if (ourTask) {
      console.log(`📊 Task status: ${ourTask.status}`);
      console.log(`📊 Last sync: ${ourTask.last_sync || 'Never'}`);
      console.log(`📊 Files count: ${ourTask.files_count || 0}`);
    }
    
    console.log('\n🎯 Test Summary:');
    console.log(`   Task ID: ${this.testTaskId}`);
    console.log(`   Source: ${this.sourceDir}`);
    console.log(`   Destination: ${this.destDir}`);
    console.log(`   History entries: ${history.length}`);
    console.log(`   Total history: ${allHistory.length}`);
    
    if (history.length > 0) {
      console.log('✅ SUCCESS: Real-time sync is creating history entries!');
    } else {
      console.log('❌ ISSUE: Real-time sync is not creating history entries');
      console.log('\n💡 Possible causes:');
      console.log('   1. Real-time sync engine not emitting completion events');
      console.log('   2. Event listeners not working properly');
      console.log('   3. Database insertion failing');
      console.log('   4. File changes not being detected');
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export for manual use
module.exports = {
  runTest: async () => {
    const tester = new SimpleRealtimeTest();
    await tester.runTest();
  },
  
  quickTest: async () => {
    console.log('🔍 Quick Real-time Test\n');
    
    try {
      const loginResponse = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      const token = loginResponse.data.token;
      const headers = { 'Authorization': `Bearer ${token}` };
      
      // Check history
      const historyResponse = await axios.get(`${baseURL}/sync/history?limit=5`, { headers });
      const history = historyResponse.data.history || [];
      
      console.log(`📊 Recent history entries: ${history.length}`);
      
      if (history.length > 0) {
        history.forEach((entry, index) => {
          console.log(`   ${index + 1}. Task ${entry.task_id}: ${entry.status} - ${entry.files_processed} files`);
        });
      }
      
      // Check tasks
      const tasksResponse = await axios.get(`${baseURL}/sync/tasks`, { headers });
      const tasks = tasksResponse.data.tasks || [];
      
      const realtimeTasks = tasks.filter(task => {
        const options = typeof task.options === 'string' ? JSON.parse(task.options) : task.options;
        return options?.enableRealtime;
      });
      
      console.log(`⚡ Real-time tasks: ${realtimeTasks.length}`);
      
    } catch (error) {
      console.error('❌ Quick test failed:', error.message);
    }
  }
};

// Auto-run if called directly
if (require.main === module) {
  const tester = new SimpleRealtimeTest();
  tester.runTest();
}

console.log('🎯 Simple Real-time Tester loaded!');
console.log('📝 Run: node scripts/test-realtime-simple.js');
console.log('📝 Or: require("./scripts/test-realtime-simple").quickTest()');
