const chokidar = require('chokidar');
const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');
const EventEmitter = require('events');

class SyncEngine extends EventEmitter {
  constructor(options = {}) {
    super();
    this.userId = options.userId;
    this.token = options.token;
    this.onSyncStart = options.onSyncStart || (() => {});
    this.onSyncProgress = options.onSyncProgress || (() => {});
    this.onSyncComplete = options.onSyncComplete || (() => {});
    this.onSyncError = options.onSyncError || (() => {});
    this.onFileChange = options.onFileChange || (() => {});

    this.activeSyncs = new Map();
    this.watchers = new Map();
    this.syncQueues = new Map();
  }

  async startSync(task) {
    try {
      if (this.activeSyncs.has(task.id)) {
        throw new Error('Sync already running for this task');
      }

      console.log(`Starting sync for task: ${task.name}`);
      this.onSyncStart(task.id);

      const syncContext = {
        task,
        startTime: Date.now(),
        filesProcessed: 0,
        filesAdded: 0,
        filesUpdated: 0,
        filesDeleted: 0,
        totalSize: 0,
        errors: []
      };

      this.activeSyncs.set(task.id, syncContext);

      // Validate paths
      await this.validatePaths(task);

      // Start file watcher if not already watching
      if (!this.watchers.has(task.id)) {
        this.startFileWatcher(task);
      }

      // Perform initial sync
      await this.performSync(syncContext);

      // Complete sync
      const duration = Date.now() - syncContext.startTime;
      const result = {
        filesCount: syncContext.filesProcessed,
        filesAdded: syncContext.filesAdded,
        filesUpdated: syncContext.filesUpdated,
        filesDeleted: syncContext.filesDeleted,
        totalSize: syncContext.totalSize,
        duration,
        errors: syncContext.errors
      };

      this.activeSyncs.delete(task.id);
      this.onSyncComplete(task.id, result);

      return result;

    } catch (error) {
      console.error(`Sync failed for task ${task.id}:`, error);
      this.activeSyncs.delete(task.id);
      this.onSyncError(task.id, error);
      throw error;
    }
  }

  async stopSync(taskId) {
    try {
      // Stop file watcher
      if (this.watchers.has(taskId)) {
        await this.watchers.get(taskId).close();
        this.watchers.delete(taskId);
      }

      // Clear sync queue
      if (this.syncQueues.has(taskId)) {
        this.syncQueues.delete(taskId);
      }

      // Remove from active syncs
      this.activeSyncs.delete(taskId);

      console.log(`Sync stopped for task: ${taskId}`);
    } catch (error) {
      console.error(`Failed to stop sync for task ${taskId}:`, error);
      throw error;
    }
  }

  async startRealtimeSync(task) {
    console.log(`🚀 Starting real-time sync for task: ${task.name}`);

    // Create sync context with task reference
    const syncContext = {
      task,
      taskId: task.id,
      startTime: Date.now(),
      filesProcessed: 0,
      filesAdded: 0,
      filesUpdated: 0,
      filesDeleted: 0,
      totalSize: 0,
      errors: []
    };

    this.activeSyncs.set(task.id, syncContext);
    this.startFileWatcher(task);

    this.onSyncStart(task.id, { message: 'Real-time sync started' });

    console.log(`✅ Real-time sync context created for task ${task.id}`);
  }

  async stopRealtimeSync(taskId) {
    console.log(`🛑 Stopping real-time sync for task: ${taskId}`);
    await this.stopSync(taskId);
  }

  async validatePaths(task) {
    // Check if source path exists
    if (!(await fs.pathExists(task.sourcePath))) {
      throw new Error(`Source path does not exist: ${task.sourcePath}`);
    }

    // Create destination path if it doesn't exist
    if (!(await fs.pathExists(task.destinationPath))) {
      await fs.ensureDir(task.destinationPath);
    }

    // Check permissions
    try {
      await fs.access(task.sourcePath, fs.constants.R_OK);
      await fs.access(task.destinationPath, fs.constants.W_OK);
    } catch (error) {
      throw new Error(`Permission denied: ${error.message}`);
    }
  }

  startFileWatcher(task) {
    const watcher = chokidar.watch(task.sourcePath, {
      ignored: this.getIgnorePatterns(task),
      persistent: true,
      ignoreInitial: false,
      followSymlinks: false,
      depth: task.options?.maxDepth || undefined
    });

    watcher
      .on('add', (filePath) => this.handleFileChange(task.id, filePath, 'add'))
      .on('change', (filePath) => this.handleFileChange(task.id, filePath, 'change'))
      .on('unlink', (filePath) => this.handleFileChange(task.id, filePath, 'unlink'))
      .on('addDir', (dirPath) => this.handleFileChange(task.id, dirPath, 'addDir'))
      .on('unlinkDir', (dirPath) => this.handleFileChange(task.id, dirPath, 'unlinkDir'))
      .on('error', (error) => {
        console.error(`Watcher error for task ${task.id}:`, error);
        this.onSyncError(task.id, error);
      });

    this.watchers.set(task.id, watcher);
    console.log(`File watcher started for task: ${task.name}`);
  }

  handleFileChange(taskId, filePath, changeType) {
    console.log(`🔍 File change detected for task ${taskId}: ${changeType} ${filePath}`);

    this.onFileChange(taskId, filePath, changeType);

    // Check if we have an active sync context
    if (!this.activeSyncs.has(taskId)) {
      console.log(`⚠️ No active sync context for task ${taskId}, creating one...`);

      // Create a minimal sync context for real-time operations
      // Note: This is a fallback, normally context should be created in startRealtimeSync
      const syncContext = {
        task: { id: taskId }, // Minimal task reference
        taskId,
        startTime: Date.now(),
        filesProcessed: 0,
        filesAdded: 0,
        filesUpdated: 0,
        filesDeleted: 0,
        totalSize: 0,
        errors: []
      };

      this.activeSyncs.set(taskId, syncContext);
    }

    // Add to sync queue
    if (!this.syncQueues.has(taskId)) {
      this.syncQueues.set(taskId, new Set());
    }

    this.syncQueues.get(taskId).add({
      filePath,
      changeType,
      timestamp: Date.now()
    });

    console.log(`📁 File change queued: ${changeType} ${filePath}`);

    // Debounce sync operations
    this.debouncedSync(taskId);
  }

  debouncedSync = this.debounce((taskId) => {
    this.processSyncQueue(taskId);
  }, 1000);

  async processSyncQueue(taskId) {
    const queue = this.syncQueues.get(taskId);
    if (!queue || queue.size === 0) return;

    const syncContext = this.activeSyncs.get(taskId);
    if (!syncContext) return;

    try {
      for (const change of queue) {
        await this.processFileChange(syncContext, change);
      }

      queue.clear();

      // Emit completion event for real-time sync batch
      const stats = {
        filesProcessed: syncContext.filesProcessed,
        filesAdded: syncContext.filesAdded,
        filesUpdated: syncContext.filesUpdated,
        filesDeleted: syncContext.filesDeleted,
        totalSize: syncContext.totalSize,
        bytesTransferred: syncContext.totalSize,
        duration: Date.now() - syncContext.startTime,
        type: 'realtime-batch'
      };

      // Call callback for backward compatibility
      this.onSyncCompleted(taskId, stats);

      // Emit event for new event-based system
      console.log(`📡 Emitting 'syncCompleted' event for task ${taskId}...`);
      console.log(`📊 Event data:`, { taskId, stats });
      console.log(`📊 Listener count:`, this.listenerCount('syncCompleted'));

      this.emit('syncCompleted', { taskId, stats });

      console.log(`✅ Real-time sync batch completed and event emitted for task ${taskId}`);
      console.log(`📊 Stats summary: ${stats.filesProcessed} files, ${stats.bytesTransferred} bytes`);

    } catch (error) {
      console.error(`Failed to process sync queue for task ${taskId}:`, error);
      this.onSyncError(taskId, error);
    }
  }

  async processFileChange(syncContext, change) {
    const { task } = syncContext;
    const { filePath, changeType } = change;

    try {
      // For minimal task reference, we need to get task info from somewhere
      if (!task.sourcePath || !task.destinationPath) {
        console.log(`⚠️ Task ${task.id} missing path info, skipping file processing`);
        return;
      }

      const relativePath = path.relative(task.sourcePath, filePath);

      switch (changeType) {
        case 'add':
        case 'change':
          // Copy file to destination
          const destPath = path.join(task.destinationPath, relativePath);
          await this.copyFile(filePath, destPath);

          if (changeType === 'add') {
            syncContext.filesAdded++;
          } else {
            syncContext.filesUpdated++;
          }

          try {
            const stats = await fs.stat(filePath);
            syncContext.totalSize += stats.size;
          } catch (statError) {
            console.warn(`Could not get file stats for ${filePath}:`, statError.message);
          }
          break;

        case 'unlink':
          // Delete file from destination if deleteExtraFiles is enabled
          if (task.options?.deleteExtraFiles) {
            const destPath = path.join(task.destinationPath, relativePath);
            try {
              await fs.remove(destPath);
              syncContext.filesDeleted++;
            } catch (error) {
              // File might not exist in destination, ignore
              console.log(`File ${destPath} already removed or doesn't exist`);
            }
          }
          break;

        case 'addDir':
          // Create directory in destination
          const destDirPath = path.join(task.destinationPath, relativePath);
          await fs.ensureDir(destDirPath);
          break;

        case 'unlinkDir':
          // Remove directory from destination if deleteExtraFiles is enabled
          if (task.options?.deleteExtraFiles) {
            const destDirPath = path.join(task.destinationPath, relativePath);
            try {
              await fs.remove(destDirPath);
            } catch (error) {
              // Directory might not exist in destination, ignore
              console.log(`Directory ${destDirPath} already removed or doesn't exist`);
            }
          }
          break;
      }

      syncContext.filesProcessed++;

      console.log(`📁 Real-time sync processed: ${changeType} ${relativePath}`);

    } catch (error) {
      console.error(`❌ Failed to process file change ${filePath}:`, error);
      syncContext.errors.push(`${changeType} ${filePath}: ${error.message}`);
    }
  }

  async performSync(syncContext) {
    const { task } = syncContext;
    
    try {
      // Get file lists from both directories
      const sourceFiles = await this.getFileList(task.sourcePath);
      const destFiles = await this.getFileList(task.destinationPath);

      // Compare and sync files
      for (const sourceFile of sourceFiles) {
        const relativePath = path.relative(task.sourcePath, sourceFile.path);
        const destPath = path.join(task.destinationPath, relativePath);
        
        const destFile = destFiles.find(f => f.path === destPath);
        
        if (!destFile) {
          // File doesn't exist in destination, copy it
          await this.copyFile(sourceFile.path, destPath);
          syncContext.filesAdded++;
        } else if (sourceFile.mtime > destFile.mtime || sourceFile.checksum !== destFile.checksum) {
          // File is newer or different, update it
          await this.copyFile(sourceFile.path, destPath);
          syncContext.filesUpdated++;
        }
        
        syncContext.filesProcessed++;
        syncContext.totalSize += sourceFile.size;
        
        // Report progress
        this.onSyncProgress(task.id, {
          processed: syncContext.filesProcessed,
          total: sourceFiles.length,
          currentFile: relativePath
        });
      }

      // Handle bidirectional sync
      if (task.syncType === 'bidirectional') {
        await this.performBidirectionalSync(syncContext, sourceFiles, destFiles);
      }

      // Handle file deletions
      if (task.options?.deleteExtraFiles) {
        await this.handleDeletions(syncContext, sourceFiles, destFiles);
      }

    } catch (error) {
      syncContext.errors.push(error.message);
      throw error;
    }
  }

  async performBidirectionalSync(syncContext, sourceFiles, destFiles) {
    const { task } = syncContext;
    
    for (const destFile of destFiles) {
      const relativePath = path.relative(task.destinationPath, destFile.path);
      const sourcePath = path.join(task.sourcePath, relativePath);
      
      const sourceFile = sourceFiles.find(f => f.path === sourcePath);
      
      if (!sourceFile) {
        // File only exists in destination, copy to source
        await this.copyFile(destFile.path, sourcePath);
        syncContext.filesAdded++;
      } else if (destFile.mtime > sourceFile.mtime && destFile.checksum !== sourceFile.checksum) {
        // Destination file is newer, copy to source
        await this.copyFile(destFile.path, sourcePath);
        syncContext.filesUpdated++;
      }
    }
  }

  async handleDeletions(syncContext, sourceFiles, destFiles) {
    const { task } = syncContext;
    
    for (const destFile of destFiles) {
      const relativePath = path.relative(task.destinationPath, destFile.path);
      const sourcePath = path.join(task.sourcePath, relativePath);
      
      const sourceExists = sourceFiles.some(f => f.path === sourcePath);
      
      if (!sourceExists) {
        await fs.remove(destFile.path);
        syncContext.filesDeleted++;
      }
    }
  }

  async getFileList(dirPath) {
    const files = [];
    
    async function traverse(currentPath) {
      const items = await fs.readdir(currentPath);
      
      for (const item of items) {
        const itemPath = path.join(currentPath, item);
        const stats = await fs.stat(itemPath);
        
        if (stats.isFile()) {
          const checksum = await this.calculateChecksum(itemPath);
          files.push({
            path: itemPath,
            size: stats.size,
            mtime: stats.mtime,
            checksum
          });
        } else if (stats.isDirectory()) {
          await traverse(itemPath);
        }
      }
    }
    
    await traverse.call(this, dirPath);
    return files;
  }

  async copyFile(sourcePath, destPath) {
    await fs.ensureDir(path.dirname(destPath));
    await fs.copy(sourcePath, destPath, { 
      overwrite: true,
      preserveTimestamps: true 
    });
  }

  async calculateChecksum(filePath) {
    const hash = crypto.createHash('md5');
    const stream = fs.createReadStream(filePath);
    
    return new Promise((resolve, reject) => {
      stream.on('data', data => hash.update(data));
      stream.on('end', () => resolve(hash.digest('hex')));
      stream.on('error', reject);
    });
  }

  getIgnorePatterns(task) {
    const defaultPatterns = [
      '**/.git/**',
      '**/.svn/**',
      '**/.hg/**',
      '**/node_modules/**',
      '**/.DS_Store',
      '**/Thumbs.db',
      '**/*.tmp',
      '**/*.temp'
    ];
    
    const customPatterns = task.filters || [];
    return [...defaultPatterns, ...customPatterns];
  }

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
}

module.exports = SyncEngine;
