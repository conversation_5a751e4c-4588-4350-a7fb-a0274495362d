const bcrypt = require('bcryptjs');
const { getDatabase } = require('../database/init');

class UserManagementService {
  constructor() {
    this.db = getDatabase();
  }

  // Get all users with pagination and filters
  async getUsers(filters = {}) {
    try {
      const {
        search,
        role,
        status,
        limit = 50,
        offset = 0,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = filters;

      let whereClause = 'WHERE 1=1';
      const params = [];

      if (search) {
        whereClause += ' AND (email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)';
        const searchPattern = `%${search}%`;
        params.push(searchPattern, searchPattern, searchPattern);
      }

      if (role) {
        whereClause += ' AND role = ?';
        params.push(role);
      }

      if (status) {
        whereClause += ' AND status = ?';
        params.push(status);
      }

      const validSortColumns = ['email', 'name', 'role', 'status', 'created_at', 'last_login'];
      const validSortOrders = ['ASC', 'DESC'];
      
      const safeSortBy = validSortColumns.includes(sortBy) ? sortBy : 'created_at';
      const safeSortOrder = validSortOrders.includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

      const result = await this.db.query(`
        SELECT
          id, email, name, role, status,
          created_at, updated_at, last_login, login_count,
          (SELECT COUNT(*) FROM sync_tasks WHERE user_id = users.id) as sync_tasks_count,
          (SELECT COUNT(*) FROM sessions WHERE user_id = users.id AND expires_at > datetime('now')) as active_sessions
        FROM users
        ${whereClause}
        ORDER BY ${safeSortBy} ${safeSortOrder}
        LIMIT ? OFFSET ?
      `, [...params, limit, offset]);

      const countResult = await this.db.query(`
        SELECT COUNT(*) as total FROM users ${whereClause}
      `, params);

      return {
        users: result.rows.map(user => ({
          id: user.id,
          email: user.email,
          name: user.name,
          fullName: user.name || '',
          role: user.role,
          status: user.status,
          createdAt: user.created_at,
          updatedAt: user.updated_at,
          lastLogin: user.last_login,
          loginCount: user.login_count || 0,
          syncTasksCount: user.sync_tasks_count || 0,
          activeSessions: user.active_sessions || 0
        })),
        total: countResult.rows[0].total,
        limit,
        offset
      };
    } catch (error) {
      console.error('Get users error:', error);
      throw error;
    }
  }

  // Get user by ID with detailed information
  async getUserById(userId) {
    try {
      const userResult = await this.db.query(`
        SELECT
          id, email, name, role, status,
          created_at, updated_at, last_login, login_count,
          preferences, settings
        FROM users WHERE id = ?
      `, [userId]);

      if (userResult.rows.length === 0) {
        throw new Error('User not found');
      }

      const user = userResult.rows[0];

      // Get user statistics
      const statsResult = await this.db.query(`
        SELECT 
          (SELECT COUNT(*) FROM sync_tasks WHERE user_id = ?) as sync_tasks,
          (SELECT COUNT(*) FROM sync_history WHERE user_id = ?) as sync_history,
          (SELECT COUNT(*) FROM file_conflicts fc JOIN sync_tasks st ON fc.task_id = st.id WHERE st.user_id = ?) as conflicts,
          (SELECT COUNT(*) FROM sync_profiles WHERE user_id = ?) as profiles,
          (SELECT COUNT(*) FROM sessions WHERE user_id = ? AND expires_at > datetime('now')) as active_sessions
      `, [userId, userId, userId, userId, userId]);

      const stats = statsResult.rows[0];

      // Get recent activity
      const activityResult = await this.db.query(`
        SELECT action, resource, timestamp, details
        FROM audit_trail
        WHERE user_id = ?
        ORDER BY timestamp DESC
        LIMIT 10
      `, [userId]);

      return {
        id: user.id,
        email: user.email,
        name: user.name,
        fullName: user.name || '',
        role: user.role,
        status: user.status,
        createdAt: user.created_at,
        updatedAt: user.updated_at,
        lastLogin: user.last_login,
        loginCount: user.login_count || 0,
        preferences: user.preferences ? JSON.parse(user.preferences) : {},
        settings: user.settings ? JSON.parse(user.settings) : {},
        statistics: {
          syncTasks: parseInt(stats.sync_tasks) || 0,
          syncHistory: parseInt(stats.sync_history) || 0,
          conflicts: parseInt(stats.conflicts) || 0,
          profiles: parseInt(stats.profiles) || 0,
          activeSessions: parseInt(stats.active_sessions) || 0
        },
        recentActivity: activityResult.rows.map(activity => ({
          action: activity.action,
          resource: activity.resource,
          timestamp: activity.timestamp,
          details: activity.details ? JSON.parse(activity.details) : {}
        }))
      };
    } catch (error) {
      console.error('Get user by ID error:', error);
      throw error;
    }
  }

  // Create new user
  async createUser(userData) {
    try {
      const {
        email,
        password,
        name,
        role = 'user',
        status = 'active'
      } = userData;

      // Validate required fields
      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      // Check if user already exists
      const existingUser = await this.db.query(
        'SELECT id FROM users WHERE email = ?',
        [email]
      );

      if (existingUser.rows.length > 0) {
        throw new Error('User with this email already exists');
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create user
      const result = await this.db.query(`
        INSERT INTO users (
          email, password, name, role, status, created_at
        ) VALUES (?, ?, ?, ?, ?, datetime('now'))
      `, [email, hashedPassword, name, role, status]);

      const userId = result.insertId || result.rows[0]?.id;

      // Create default sync profile for user
      await this.createDefaultProfile(userId);

      return {
        id: userId,
        email,
        name,
        role,
        status,
        message: 'User created successfully'
      };
    } catch (error) {
      console.error('Create user error:', error);
      throw error;
    }
  }

  // Update user
  async updateUser(userId, updateData) {
    try {
      const {
        email,
        name,
        role,
        status,
        preferences,
        settings
      } = updateData;

      // Check if user exists
      const existingUser = await this.db.query(
        'SELECT id, email FROM users WHERE id = ?',
        [userId]
      );

      if (existingUser.rows.length === 0) {
        throw new Error('User not found');
      }

      // Check email uniqueness if email is being updated
      if (email && email !== existingUser.rows[0].email) {
        const emailCheck = await this.db.query(
          'SELECT id FROM users WHERE email = ? AND id != ?',
          [email, userId]
        );

        if (emailCheck.rows.length > 0) {
          throw new Error('Email already in use by another user');
        }
      }

      // Build update query
      const updateFields = [];
      const params = [];

      if (email) {
        updateFields.push('email = ?');
        params.push(email);
      }

      if (name !== undefined) {
        updateFields.push('name = ?');
        params.push(name);
      }

      if (role) {
        updateFields.push('role = ?');
        params.push(role);
      }

      if (status) {
        updateFields.push('status = ?');
        params.push(status);
      }

      if (preferences) {
        updateFields.push('preferences = ?');
        params.push(JSON.stringify(preferences));
      }

      if (settings) {
        updateFields.push('settings = ?');
        params.push(JSON.stringify(settings));
      }

      if (updateFields.length === 0) {
        throw new Error('No fields to update');
      }

      updateFields.push('updated_at = datetime(\'now\')');
      params.push(userId);

      await this.db.query(`
        UPDATE users SET ${updateFields.join(', ')} WHERE id = ?
      `, params);

      return { success: true, message: 'User updated successfully' };
    } catch (error) {
      console.error('Update user error:', error);
      throw error;
    }
  }

  // Delete user (soft delete)
  async deleteUser(userId) {
    try {
      // Check if user exists
      const existingUser = await this.db.query(
        'SELECT id, status FROM users WHERE id = ?',
        [userId]
      );

      if (existingUser.rows.length === 0) {
        throw new Error('User not found');
      }

      // Soft delete by setting status to 'deleted'
      await this.db.query(`
        UPDATE users 
        SET status = 'deleted', updated_at = datetime('now')
        WHERE id = ?
      `, [userId]);

      // Invalidate all user sessions
      await this.db.query(
        'DELETE FROM sessions WHERE user_id = ?',
        [userId]
      );

      return { success: true, message: 'User deleted successfully' };
    } catch (error) {
      console.error('Delete user error:', error);
      throw error;
    }
  }

  // Change user password
  async changePassword(userId, currentPassword, newPassword) {
    try {
      // Get current password hash
      const userResult = await this.db.query(
        'SELECT password FROM users WHERE id = ? AND status = "active"',
        [userId]
      );

      if (userResult.rows.length === 0) {
        throw new Error('User not found or inactive');
      }

      // Verify current password
      const isValidPassword = await bcrypt.compare(currentPassword, userResult.rows[0].password);
      if (!isValidPassword) {
        throw new Error('Current password is incorrect');
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(newPassword, 10);

      // Update password
      await this.db.query(`
        UPDATE users 
        SET password = ?, updated_at = datetime('now')
        WHERE id = ?
      `, [hashedNewPassword, userId]);

      // Invalidate all other sessions
      await this.db.query(
        'DELETE FROM sessions WHERE user_id = ?',
        [userId]
      );

      return { success: true, message: 'Password changed successfully' };
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  }

  // Reset user password (admin function)
  async resetPassword(userId, newPassword) {
    try {
      // Check if user exists
      const existingUser = await this.db.query(
        'SELECT id FROM users WHERE id = ?',
        [userId]
      );

      if (existingUser.rows.length === 0) {
        throw new Error('User not found');
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Update password
      await this.db.query(`
        UPDATE users 
        SET password = ?, updated_at = datetime('now')
        WHERE id = ?
      `, [hashedPassword, userId]);

      // Invalidate all user sessions
      await this.db.query(
        'DELETE FROM sessions WHERE user_id = ?',
        [userId]
      );

      return { success: true, message: 'Password reset successfully' };
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  }

  // Get user sessions
  async getUserSessions(userId) {
    try {
      const result = await this.db.query(`
        SELECT 
          token, created_at, expires_at, last_used,
          ip_address, user_agent
        FROM sessions 
        WHERE user_id = ? AND expires_at > datetime('now')
        ORDER BY last_used DESC
      `, [userId]);

      return result.rows.map(session => ({
        token: session.token.substring(0, 10) + '...', // Masked token
        createdAt: session.created_at,
        expiresAt: session.expires_at,
        lastUsed: session.last_used,
        ipAddress: session.ip_address,
        userAgent: session.user_agent,
        isActive: new Date(session.expires_at) > new Date()
      }));
    } catch (error) {
      console.error('Get user sessions error:', error);
      throw error;
    }
  }

  // Revoke user session
  async revokeSession(userId, sessionToken) {
    try {
      await this.db.query(
        'DELETE FROM sessions WHERE user_id = ? AND token = ?',
        [userId, sessionToken]
      );

      return { success: true, message: 'Session revoked successfully' };
    } catch (error) {
      console.error('Revoke session error:', error);
      throw error;
    }
  }

  // Get user statistics
  async getUserStatistics() {
    try {
      const result = await this.db.query(`
        SELECT 
          COUNT(*) as total_users,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
          COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_users,
          COUNT(CASE WHEN status = 'deleted' THEN 1 END) as deleted_users,
          COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_users,
          COUNT(CASE WHEN role = 'user' THEN 1 END) as regular_users,
          COUNT(CASE WHEN last_login >= datetime('now', '-7 days') THEN 1 END) as active_last_week,
          COUNT(CASE WHEN created_at >= datetime('now', '-30 days') THEN 1 END) as new_last_month
        FROM users
      `);

      const stats = result.rows[0];

      return {
        totalUsers: parseInt(stats.total_users) || 0,
        activeUsers: parseInt(stats.active_users) || 0,
        inactiveUsers: parseInt(stats.inactive_users) || 0,
        deletedUsers: parseInt(stats.deleted_users) || 0,
        adminUsers: parseInt(stats.admin_users) || 0,
        regularUsers: parseInt(stats.regular_users) || 0,
        activeLastWeek: parseInt(stats.active_last_week) || 0,
        newLastMonth: parseInt(stats.new_last_month) || 0
      };
    } catch (error) {
      console.error('Get user statistics error:', error);
      throw error;
    }
  }

  // Create default profile for new user
  async createDefaultProfile(userId) {
    try {
      const defaultConfig = {
        syncType: 'bidirectional',
        conflictStrategy: 'ask_user',
        bandwidthLimit: 0,
        deleteExtraFiles: false,
        preserveTimestamps: true,
        followSymlinks: false,
        excludePatterns: ['*.tmp', '*.log', '.DS_Store'],
        maxFileSize: 0,
        versioning: {
          enabled: true,
          maxVersions: 10
        }
      };

      await this.db.query(`
        INSERT INTO sync_profiles (user_id, name, description, config, is_default)
        VALUES (?, ?, ?, ?, ?)
      `, [
        userId,
        'Default Profile',
        'Default sync configuration',
        JSON.stringify(defaultConfig),
        1
      ]);
    } catch (error) {
      console.error('Create default profile error:', error);
      // Don't throw error as this is not critical
    }
  }
}

module.exports = UserManagementService;
