#!/usr/bin/env node

/**
 * Test web UI login and dashboard data
 */

const axios = require('axios');

console.log('🌐 Testing Web UI Login and Dashboard');
console.log('====================================');

async function testWebUILogin() {
  console.log('\n1. 🔐 Testing web UI login...');
  
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Login Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Web UI login successful!');
      console.log('👤 User:', response.data.user.name);
      console.log('🔑 Token length:', response.data.token.length);
      return {
        token: response.data.token,
        user: response.data.user
      };
    } else {
      console.log('❌ Web UI login failed:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Web UI login error:', error.message);
    return null;
  }
}

async function testDashboardAPI(token) {
  console.log('\n2. 📊 Testing dashboard API...');
  
  try {
    const response = await axios.get('http://localhost:5001/api/clients', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Clients API Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Dashboard API working!');
      console.log('📊 Response:', response.data);
      
      const clients = response.data.clients || [];
      console.log(`📋 Found ${clients.length} clients:`);
      
      clients.forEach((client, index) => {
        console.log(`   ${index + 1}. ${client.hostname} (${client.client_id})`);
        console.log(`      Status: ${client.status}`);
        console.log(`      Platform: ${client.platform}`);
        console.log(`      Tasks: ${client.total_tasks || 0} total, ${client.active_tasks || 0} active`);
        console.log(`      Last seen: ${client.last_seen}`);
      });
      
      return clients;
    } else {
      console.log('❌ Dashboard API failed:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Dashboard API error:', error.message);
    return null;
  }
}

async function testWebUIAccess() {
  console.log('\n3. 🌐 Testing web UI access...');
  
  try {
    const response = await axios.get('http://localhost:3001', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📝 Web UI Status:', response.status);
    
    if (response.status === 200) {
      console.log('✅ Web UI accessible!');
      console.log('📄 Content type:', response.headers['content-type']);
      
      // Check if it's HTML content
      if (response.headers['content-type']?.includes('text/html')) {
        console.log('✅ HTML content served correctly');
        return true;
      } else {
        console.log('⚠️ Unexpected content type');
        return false;
      }
    } else {
      console.log('❌ Web UI not accessible');
      return false;
    }
  } catch (error) {
    console.log('❌ Web UI access error:', error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting web UI test...\n');
    
    // Test web UI access
    const webUIWorking = await testWebUIAccess();
    
    // Test login
    const auth = await testWebUILogin();
    
    // Test dashboard API
    let clients = null;
    if (auth) {
      clients = await testDashboardAPI(auth.token);
    }
    
    console.log('\n📊 Test Summary:');
    console.log('================');
    console.log('- Web UI Access:', webUIWorking ? '✅ Working' : '❌ Failed');
    console.log('- Web UI Login:', auth ? '✅ Success' : '❌ Failed');
    console.log('- Dashboard API:', clients ? '✅ Success' : '❌ Failed');
    console.log('- Connected Clients:', clients ? clients.length : 0);
    
    if (webUIWorking && auth && clients) {
      console.log('\n🎉 SUCCESS! Web management interface is working!');
      console.log('🔍 You can now:');
      console.log('   1. Open http://localhost:3001 in browser');
      console.log('   2. <NAME_EMAIL> / admin');
      console.log('   3. View real desktop client data on dashboard');
      console.log('   4. Go to Client Management to control clients');
      
      if (clients.length > 0) {
        console.log('\n📋 Real client data available:');
        clients.forEach(client => {
          console.log(`   - ${client.hostname}: ${client.status} (${client.total_tasks || 0} tasks)`);
        });
      }
    } else {
      console.log('\n❌ Some components not working properly');
      
      if (!webUIWorking) {
        console.log('💡 Check if web UI server is running: npm run web-ui');
      }
      if (!auth) {
        console.log('💡 Check if web server is running: npm run server-web');
      }
      if (!clients) {
        console.log('💡 Check authentication and API endpoints');
      }
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
