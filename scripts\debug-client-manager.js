#!/usr/bin/env node

/**
 * Debug script to check ClientManager initialization
 */

const axios = require('axios');

console.log('🔍 Debugging ClientManager Initialization');
console.log('=========================================');

async function testDesktopLogin() {
  console.log('\n1. Testing desktop login API...');
  
  try {
    const response = await axios.post('http://localhost:5002/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Login Response Status:', response.status);
    console.log('📝 Login Response Headers:', response.headers);
    
    if (response.status === 200) {
      console.log('✅ Login successful!');
      console.log('👤 User:', response.data.user.name);
      console.log('🔑 Token received:', response.data.token ? 'Yes' : 'No');
      
      // Check if ClientManager logs appear after this
      console.log('\n💡 Check desktop server logs for ClientManager initialization...');
      console.log('💡 Look for: "🖥️ Initializing Client Manager for desktop user:"');
      
      return {
        success: true,
        token: response.data.token,
        user: response.data.user
      };
    } else {
      console.log('❌ Login failed');
      console.log('📝 Response:', response.data);
      return { success: false };
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return { success: false };
  }
}

async function checkExtendedSyncEngine() {
  console.log('\n2. Checking ExtendedSyncEngine status...');
  
  try {
    // Try to access any endpoint that would use extendedSyncEngine
    const response = await axios.get('http://localhost:5002/api/sync/tasks', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📊 Sync Tasks API Status:', response.status);
    
    if (response.status === 401) {
      console.log('✅ ExtendedSyncEngine is responding (requires auth)');
      return true;
    } else if (response.status === 200) {
      console.log('✅ ExtendedSyncEngine is responding');
      return true;
    } else {
      console.log('⚠️ ExtendedSyncEngine response:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ ExtendedSyncEngine check error:', error.message);
    return false;
  }
}

async function checkEnvironmentVariables() {
  console.log('\n3. Checking environment variables...');
  
  try {
    // Create a simple endpoint test to check env vars
    const response = await axios.get('http://localhost:5002/api/sync/tasks', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    // The fact that we get a response means the server is running
    console.log('✅ Desktop server is running');
    console.log('💡 ELECTRON_ENV should be "true" (check server startup logs)');
    console.log('💡 DB_TYPE should be "sqlite" (check server startup logs)');
    
    return true;
  } catch (error) {
    console.log('❌ Desktop server not responding:', error.message);
    return false;
  }
}

async function simulateClientManagerCheck() {
  console.log('\n4. Simulating ClientManager check...');
  
  // Wait a moment for any async initialization
  console.log('⏳ Waiting 3 seconds for any async processes...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  try {
    // Check if web server receives any client registrations
    const response = await axios.get('http://localhost:5001/api/clients', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📊 Web Server Clients API Status:', response.status);
    
    if (response.status === 401) {
      console.log('⚠️ Web server requires authentication');
      console.log('💡 This is expected - web server needs auth');
    } else if (response.status === 200) {
      console.log('📊 Registered Clients:', response.data);
      console.log('📊 Total Clients:', response.data.length);
      
      if (response.data.length > 0) {
        console.log('✅ ClientManager is working! Found registered clients.');
        return true;
      } else {
        console.log('⚠️ No clients registered yet.');
        return false;
      }
    } else {
      console.log('📊 Web Server Response:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Web server check error:', error.message);
    return false;
  }
  
  return false;
}

async function main() {
  try {
    console.log('🚀 Starting ClientManager debug session...\n');
    
    // Step 1: Check environment
    const envOk = await checkEnvironmentVariables();
    if (!envOk) {
      console.log('\n❌ Environment check failed. Desktop server not responding.');
      return;
    }
    
    // Step 2: Check ExtendedSyncEngine
    const engineOk = await checkExtendedSyncEngine();
    
    // Step 3: Test login (this should trigger ClientManager)
    const loginResult = await testDesktopLogin();
    
    // Step 4: Check if ClientManager registered with web server
    const clientManagerOk = await simulateClientManagerCheck();
    
    console.log('\n📊 Debug Summary:');
    console.log('=================');
    console.log('- Desktop Server:', envOk ? '✅ Running' : '❌ Not responding');
    console.log('- ExtendedSyncEngine:', engineOk ? '✅ Active' : '❌ Not responding');
    console.log('- Login API:', loginResult.success ? '✅ Working' : '❌ Failed');
    console.log('- ClientManager:', clientManagerOk ? '✅ Active' : '⚠️ Not detected');
    
    if (loginResult.success && !clientManagerOk) {
      console.log('\n🔍 Diagnosis:');
      console.log('- Login is successful ✅');
      console.log('- But ClientManager is not registering with web server ❌');
      console.log('\n💡 Possible causes:');
      console.log('1. ExtendedSyncEngine not properly set in app context');
      console.log('2. ClientManager initialization throwing silent error');
      console.log('3. Web server connection failing (check network)');
      console.log('4. Authentication token not being passed correctly');
      
      console.log('\n🔧 Next steps:');
      console.log('1. Check desktop server logs for ClientManager initialization');
      console.log('2. Look for any error messages during login');
      console.log('3. Verify web server is accessible from desktop');
    } else if (clientManagerOk) {
      console.log('\n🎉 SUCCESS! ClientManager is working correctly!');
      console.log('🌐 Web management should show real data now.');
    }
    
  } catch (error) {
    console.error('\n❌ Debug session failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { testDesktopLogin, checkExtendedSyncEngine };
