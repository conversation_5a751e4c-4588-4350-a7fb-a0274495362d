const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

async function clearSQLiteSessions() {
  console.log('🗑️ Clearing All Sessions from SQLite Database\n');

  try {
    // Find SQLite database file
    const possiblePaths = [
      path.join(__dirname, '..', 'data', 'syncmasterpro.db'),
      path.join(__dirname, '..', 'database', 'syncmaster.db'),
      path.join(__dirname, '..', 'server', 'database', 'syncmaster.db'),
      path.join(__dirname, '..', 'syncmaster.db')
    ];

    let dbPath = null;
    for (const testPath of possiblePaths) {
      if (fs.existsSync(testPath)) {
        dbPath = testPath;
        break;
      }
    }

    if (!dbPath) {
      console.log('❌ SQLite database file not found in any of these locations:');
      possiblePaths.forEach(p => console.log(`   - ${p}`));
      return;
    }

    console.log('📁 Database path:', dbPath);

    // Connect to SQLite database
    const db = new Database(dbPath);
    console.log('✅ Connected to SQLite database');

    // Check if sessions table exists
    const tables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='sessions'
    `).all();

    if (tables.length === 0) {
      console.log('❌ Sessions table not found');
      console.log('💡 Database may not be initialized yet');
      db.close();
      return;
    }

    // Check current sessions before deletion
    console.log('\n📊 BEFORE DELETION:');
    console.log('=' .repeat(50));
    
    const beforeStats = db.prepare(`
      SELECT 
        COUNT(*) as total_sessions,
        COUNT(CASE WHEN expires_at > datetime('now') THEN 1 END) as active_sessions,
        COUNT(CASE WHEN expires_at <= datetime('now') THEN 1 END) as expired_sessions
      FROM sessions
    `).get();

    console.log(`📋 Total sessions: ${beforeStats.total_sessions}`);
    console.log(`🟢 Active sessions: ${beforeStats.active_sessions}`);
    console.log(`🔴 Expired sessions: ${beforeStats.expired_sessions}`);

    if (beforeStats.total_sessions === 0) {
      console.log('✅ No sessions to delete');
      db.close();
      return;
    }

    // Show sessions by user
    const userSessions = db.prepare(`
      SELECT 
        s.user_id,
        u.email,
        COUNT(*) as session_count,
        COUNT(CASE WHEN s.expires_at > datetime('now') THEN 1 END) as active_count
      FROM sessions s
      LEFT JOIN users u ON s.user_id = u.id
      GROUP BY s.user_id, u.email
      ORDER BY session_count DESC
    `).all();

    if (userSessions.length > 0) {
      console.log('\n👥 Sessions by User:');
      userSessions.forEach(row => {
        console.log(`   👤 ${row.email || 'Unknown'} (ID: ${row.user_id}): ${row.session_count} total, ${row.active_count} active`);
      });
    }

    // Show recent sessions
    const recentSessions = db.prepare(`
      SELECT 
        s.id,
        s.user_id,
        u.email,
        s.created_at,
        s.expires_at,
        CASE WHEN s.expires_at > datetime('now') THEN 'Active' ELSE 'Expired' END as status
      FROM sessions s
      LEFT JOIN users u ON s.user_id = u.id
      ORDER BY s.created_at DESC
      LIMIT 10
    `).all();

    if (recentSessions.length > 0) {
      console.log('\n📋 Recent Sessions (Last 10):');
      recentSessions.forEach((row, index) => {
        console.log(`   ${index + 1}. ID: ${row.id} | User: ${row.email || 'Unknown'} | Status: ${row.status}`);
        console.log(`      Created: ${row.created_at} | Expires: ${row.expires_at}`);
      });
    }

    // Confirm deletion
    console.log('\n⚠️ WARNING: This will delete ALL sessions!');
    console.log('   - All users will be logged out');
    console.log('   - Desktop clients will need to login again');
    console.log('   - Web sessions will be invalidated');

    // Delete all sessions
    console.log('\n🗑️ DELETING ALL SESSIONS...');
    
    const deleteResult = db.prepare('DELETE FROM sessions').run();
    const deletedCount = deleteResult.changes;
    
    console.log(`✅ Deleted ${deletedCount} sessions`);

    // Verify deletion
    console.log('\n📊 AFTER DELETION:');
    console.log('=' .repeat(50));
    
    const afterStats = db.prepare('SELECT COUNT(*) as remaining_sessions FROM sessions').get();
    console.log(`📋 Remaining sessions: ${afterStats.remaining_sessions}`);

    if (afterStats.remaining_sessions === 0) {
      console.log('✅ All sessions successfully deleted');
    } else {
      console.log(`⚠️ ${afterStats.remaining_sessions} sessions still remain`);
    }

    // Check desktop_clients table
    const clientTables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='desktop_clients'
    `).all();

    if (clientTables.length > 0) {
      console.log('\n🖥️ CHECKING DESKTOP CLIENTS...');
      
      const clientStats = db.prepare(`
        SELECT COUNT(*) as total_clients,
               COUNT(CASE WHEN status = 'online' THEN 1 END) as online_clients,
               COUNT(CASE WHEN status = 'offline' THEN 1 END) as offline_clients
        FROM desktop_clients
      `).get();

      console.log(`📋 Total clients: ${clientStats.total_clients}`);
      console.log(`🟢 Online clients: ${clientStats.online_clients}`);
      console.log(`🔴 Offline clients: ${clientStats.offline_clients}`);

      // Mark all clients as offline since sessions are cleared
      if (clientStats.online_clients > 0) {
        console.log('\n📴 Marking all clients as offline (since sessions are cleared)...');
        
        const updateResult = db.prepare(`
          UPDATE desktop_clients 
          SET status = 'offline', last_seen = datetime('now')
          WHERE status = 'online'
        `).run();
        
        console.log(`✅ Marked ${updateResult.changes} clients as offline`);
      }
    }

    // Close database
    db.close();
    console.log('\n🎉 SESSION CLEANUP COMPLETED!');
    
    console.log('\n💡 NEXT STEPS:');
    console.log('   1. All users need to login again');
    console.log('   2. Desktop clients will reconnect automatically');
    console.log('   3. Web sessions are now clean');
    console.log('   4. Check Client Management to verify status');
    console.log('   5. Use Adminer to verify database changes');

  } catch (error) {
    console.error('❌ Failed to clear sessions:', error);
  }
}

// Run cleanup
clearSQLiteSessions();
