const express = require('express');
const router = express.Router();
const AdvancedFilters = require('../services/AdvancedFilters');
const authenticateToken = require('../middleware/auth');
const { getDatabase } = require('../database/init');

const advancedFilters = new AdvancedFilters();

// Get filter presets
router.get('/presets', authenticateToken, async (req, res) => {
  try {
    const presets = advancedFilters.getPresets();
    
    res.json({
      success: true,
      presets: Object.keys(presets).map(key => ({
        id: key,
        ...presets[key]
      }))
    });
  } catch (error) {
    console.error('Failed to get filter presets:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get filter presets'
    });
  }
});

// Get specific preset
router.get('/presets/:presetId', authenticateToken, async (req, res) => {
  try {
    const { presetId } = req.params;
    const presets = advancedFilters.getPresets();
    
    if (!presets[presetId]) {
      return res.status(404).json({
        success: false,
        error: 'Preset not found'
      });
    }
    
    res.json({
      success: true,
      preset: {
        id: presetId,
        ...presets[presetId]
      }
    });
  } catch (error) {
    console.error('Failed to get filter preset:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get filter preset'
    });
  }
});

// Analyze directory with filters
router.post('/analyze', authenticateToken, async (req, res) => {
  try {
    const { directoryPath, filters = {} } = req.body;
    
    if (!directoryPath) {
      return res.status(400).json({
        success: false,
        error: 'Directory path is required'
      });
    }
    
    const analysis = await advancedFilters.analyzeDirectory(directoryPath, filters);
    
    res.json({
      success: true,
      analysis: {
        ...analysis,
        totalSizeMB: (analysis.totalSize / (1024 * 1024)).toFixed(2),
        includedSizeMB: (analysis.includedSize / (1024 * 1024)).toFixed(2),
        excludedSizeMB: (analysis.excludedSize / (1024 * 1024)).toFixed(2),
        inclusionRate: analysis.totalFiles > 0 
          ? ((analysis.includedFiles / analysis.totalFiles) * 100).toFixed(1)
          : '0.0'
      }
    });
  } catch (error) {
    console.error('Failed to analyze directory:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to analyze directory'
    });
  }
});

// Test filters on a single file
router.post('/test-file', authenticateToken, async (req, res) => {
  try {
    const { filePath, filters = {} } = req.body;
    
    if (!filePath) {
      return res.status(400).json({
        success: false,
        error: 'File path is required'
      });
    }
    
    const fs = require('fs').promises;
    const stats = await fs.stat(filePath);
    const result = await advancedFilters.shouldIncludeFile(filePath, stats, filters);
    
    res.json({
      success: true,
      result: {
        ...result,
        fileInfo: {
          path: filePath,
          size: stats.size,
          sizeMB: (stats.size / (1024 * 1024)).toFixed(2),
          modified: stats.mtime,
          created: stats.birthtime,
          isDirectory: stats.isDirectory()
        }
      }
    });
  } catch (error) {
    console.error('Failed to test file filter:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test file filter'
    });
  }
});

// Save custom filter configuration
router.post('/custom', authenticateToken, async (req, res) => {
  try {
    const { name, description, filters } = req.body;
    
    if (!name || !filters) {
      return res.status(400).json({
        success: false,
        error: 'Name and filters are required'
      });
    }
    
    const db = getDatabase();
    
    // Save to database (we'll need to add a filters table)
    const result = await db.query(
      `INSERT INTO filter_configurations (user_id, name, description, filters, created_at)
       VALUES (?, ?, ?, ?, datetime('now'))`,
      [req.userId, name, description || '', JSON.stringify(filters)]
    );
    
    const filterId = result.insertId || result.rows[0]?.id;
    
    res.status(201).json({
      success: true,
      message: 'Custom filter saved successfully',
      filterId,
      filter: {
        id: filterId,
        name,
        description: description || '',
        filters
      }
    });
  } catch (error) {
    console.error('Failed to save custom filter:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to save custom filter'
    });
  }
});

// Get user's custom filters
router.get('/custom', authenticateToken, async (req, res) => {
  try {
    const db = getDatabase();
    
    const result = await db.query(
      'SELECT * FROM filter_configurations WHERE user_id = ? ORDER BY created_at DESC',
      [req.userId]
    );
    
    const filters = result.rows.map(filter => ({
      id: filter.id,
      name: filter.name,
      description: filter.description,
      filters: JSON.parse(filter.filters),
      createdAt: filter.created_at,
      updatedAt: filter.updated_at
    }));
    
    res.json({
      success: true,
      filters,
      count: filters.length
    });
  } catch (error) {
    console.error('Failed to get custom filters:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get custom filters'
    });
  }
});

// Update custom filter
router.put('/custom/:filterId', authenticateToken, async (req, res) => {
  try {
    const { filterId } = req.params;
    const { name, description, filters } = req.body;
    const db = getDatabase();
    
    // Check if filter exists and belongs to user
    const existingResult = await db.query(
      'SELECT * FROM filter_configurations WHERE id = ? AND user_id = ?',
      [filterId, req.userId]
    );
    
    if (existingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Filter not found'
      });
    }
    
    const existing = existingResult.rows[0];
    
    await db.query(
      `UPDATE filter_configurations 
       SET name = ?, description = ?, filters = ?, updated_at = datetime('now')
       WHERE id = ? AND user_id = ?`,
      [
        name || existing.name,
        description !== undefined ? description : existing.description,
        filters ? JSON.stringify(filters) : existing.filters,
        filterId,
        req.userId
      ]
    );
    
    res.json({
      success: true,
      message: 'Filter updated successfully'
    });
  } catch (error) {
    console.error('Failed to update custom filter:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update custom filter'
    });
  }
});

// Delete custom filter
router.delete('/custom/:filterId', authenticateToken, async (req, res) => {
  try {
    const { filterId } = req.params;
    const db = getDatabase();
    
    const result = await db.query(
      'DELETE FROM filter_configurations WHERE id = ? AND user_id = ?',
      [filterId, req.userId]
    );
    
    if (result.changes === 0) {
      return res.status(404).json({
        success: false,
        error: 'Filter not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Filter deleted successfully'
    });
  } catch (error) {
    console.error('Failed to delete custom filter:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete custom filter'
    });
  }
});

// Validate filter configuration
router.post('/validate', authenticateToken, async (req, res) => {
  try {
    const { filters } = req.body;
    
    if (!filters) {
      return res.status(400).json({
        success: false,
        error: 'Filters configuration is required'
      });
    }
    
    const validation = {
      valid: true,
      warnings: [],
      errors: []
    };
    
    // Validate filter structure
    if (filters.maxFileSize && filters.minFileSize && filters.maxFileSize < filters.minFileSize) {
      validation.errors.push('Maximum file size cannot be smaller than minimum file size');
      validation.valid = false;
    }
    
    if (filters.modifiedAfter && filters.modifiedBefore) {
      const after = new Date(filters.modifiedAfter);
      const before = new Date(filters.modifiedBefore);
      if (after > before) {
        validation.errors.push('Modified after date cannot be later than modified before date');
        validation.valid = false;
      }
    }
    
    if (filters.excludePatterns && filters.includePatterns) {
      const hasOverlap = filters.excludePatterns.some(exclude => 
        filters.includePatterns.some(include => include === exclude)
      );
      if (hasOverlap) {
        validation.warnings.push('Some patterns appear in both include and exclude lists');
      }
    }
    
    if (filters.maxFileSize && filters.maxFileSize > 1024 * 1024 * 1024) { // 1GB
      validation.warnings.push('Large file size limit may impact performance');
    }
    
    res.json({
      success: true,
      validation
    });
  } catch (error) {
    console.error('Failed to validate filters:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate filters'
    });
  }
});

module.exports = router;
