const express = require('express');
const { getDatabase } = require('../database/init');

const router = express.Router();

// Get user profile
router.get('/profile', async (req, res) => {
  try {
    const db = getDatabase();
    const result = await db.query(
      'SELECT id, email, name, avatar, settings, created_at FROM users WHERE id = ?',
      [req.userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'User not found'
      });
    }

    const user = result.rows[0];
    const userData = {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      settings: typeof user.settings === 'string' ? JSON.parse(user.settings) : user.settings,
      createdAt: user.created_at
    };

    res.json({ user: userData });
  } catch (error) {
    console.error('Failed to get user profile:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve user profile'
    });
  }
});

// Get user statistics
router.get('/stats', async (req, res) => {
  try {
    const db = getDatabase();

    // Get sync tasks count
    const tasksResult = await db.query(
      'SELECT COUNT(*) as count FROM sync_tasks WHERE user_id = ?',
      [req.userId]
    );

    // Get sync history count
    const historyResult = await db.query(
      'SELECT COUNT(*) as count FROM sync_history WHERE user_id = ?',
      [req.userId]
    );

    // Get successful syncs count
    const successfulResult = await db.query(
      'SELECT COUNT(*) as count FROM sync_history WHERE user_id = ? AND status = ?',
      [req.userId, 'completed']
    );

    // Get failed syncs count
    const failedResult = await db.query(
      'SELECT COUNT(*) as count FROM sync_history WHERE user_id = ? AND status = ?',
      [req.userId, 'error']
    );

    // Get total files processed
    const filesResult = await db.query(
      'SELECT SUM(files_processed) as total FROM sync_history WHERE user_id = ? AND status = ?',
      [req.userId, 'completed']
    );

    // Get total data transferred
    const dataResult = await db.query(
      'SELECT SUM(total_size) as total FROM sync_history WHERE user_id = ? AND status = ?',
      [req.userId, 'completed']
    );

    const stats = {
      totalTasks: tasksResult.rows[0].count || 0,
      totalSyncs: historyResult.rows[0].count || 0,
      successfulSyncs: successfulResult.rows[0].count || 0,
      failedSyncs: failedResult.rows[0].count || 0,
      totalFilesProcessed: filesResult.rows[0].total || 0,
      totalDataTransferred: dataResult.rows[0].total || 0
    };

    res.json({ stats });
  } catch (error) {
    console.error('Failed to get user statistics:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve user statistics'
    });
  }
});

// Get user activity
router.get('/activity', async (req, res) => {
  try {
    const { limit = 20 } = req.query;
    const db = getDatabase();

    const result = await db.query(
      `SELECT h.*, t.name as task_name 
       FROM sync_history h 
       LEFT JOIN sync_tasks t ON h.task_id = t.id 
       WHERE h.user_id = ? 
       ORDER BY h.started_at DESC 
       LIMIT ?`,
      [req.userId, parseInt(limit)]
    );

    const activity = result.rows.map(entry => ({
      ...entry,
      details: typeof entry.details === 'string' ? JSON.parse(entry.details) : entry.details
    }));

    res.json({ activity });
  } catch (error) {
    console.error('Failed to get user activity:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve user activity'
    });
  }
});

// Get user settings
router.get('/settings', async (req, res) => {
  try {
    const db = getDatabase();
    const result = await db.query(
      'SELECT settings FROM users WHERE id = ?',
      [req.userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'User not found'
      });
    }

    const user = result.rows[0];
    const settings = typeof user.settings === 'string' ? JSON.parse(user.settings) : user.settings || {};

    res.json({ settings });
  } catch (error) {
    console.error('Failed to get user settings:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve user settings'
    });
  }
});

// Update user settings
router.put('/settings', async (req, res) => {
  try {
    const { settings } = req.body;

    if (!settings || typeof settings !== 'object') {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Settings object is required'
      });
    }

    const db = getDatabase();

    // Get current settings
    const currentResult = await db.query(
      'SELECT settings FROM users WHERE id = ?',
      [req.userId]
    );

    if (currentResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'User not found'
      });
    }

    const currentSettings = typeof currentResult.rows[0].settings === 'string'
      ? JSON.parse(currentResult.rows[0].settings)
      : currentResult.rows[0].settings || {};

    // Merge with new settings
    const updatedSettings = { ...currentSettings, ...settings };

    // Update in database
    await db.query(
      'UPDATE users SET settings = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [JSON.stringify(updatedSettings), req.userId]
    );

    res.json({
      message: 'Settings updated successfully',
      settings: updatedSettings
    });
  } catch (error) {
    console.error('Failed to update user settings:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update user settings'
    });
  }
});

module.exports = router;
