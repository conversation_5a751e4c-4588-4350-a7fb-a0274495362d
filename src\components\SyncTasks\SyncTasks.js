import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSync } from '../../contexts/SyncContext';
import { useSocket } from '../../contexts/SocketContext';
import { useLanguage } from '../../contexts/LanguageContext';
import TaskCard from './TaskCard';
import CreateTaskModal from './CreateTaskModal';

const SyncTasks = () => {
  const { syncTasks, createSyncTask, updateSyncTask, deleteSyncTask, startSync, stopSync, activeSyncs } = useSync();
  const { isConnected } = useSocket();
  const { t } = useLanguage();
  const location = useLocation();
  const navigate = useNavigate();

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingTask, setEditingTask] = useState(null);
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Handle URL parameters
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const action = params.get('action');
    const editId = params.get('edit');
    const filter = params.get('filter');

    if (action === 'new') {
      setShowCreateModal(true);
    }

    if (editId) {
      const taskToEdit = syncTasks.find(task => task.id === editId);
      if (taskToEdit) {
        setEditingTask(taskToEdit);
        setShowEditModal(true);
      }
    }

    if (filter) {
      setFilterStatus(filter);
    }
  }, [location.search, syncTasks]);

  const handleCreateTask = async (taskData) => {
    console.log('🔍 SyncTasks: handleCreateTask called with:', taskData);
    try {
      const result = await createSyncTask(taskData);
      console.log('📊 SyncTasks: createSyncTask result:', result);

      // Clear URL parameters after successful creation
      if (result.success) {
        navigate('/sync-tasks', { replace: true });
      }

      return result;
    } catch (error) {
      console.error('❌ SyncTasks: Error in handleCreateTask:', error);
      return { success: false, error: error.message };
    }
  };

  const handleEditTask = async (taskData) => {
    try {
      const result = await updateSyncTask(editingTask.id, taskData);

      if (result.success) {
        setShowEditModal(false);
        setEditingTask(null);
        navigate('/sync-tasks', { replace: true });
      }

      return result;
    } catch (error) {
      console.error('❌ SyncTasks: Error in handleEditTask:', error);
      return { success: false, error: error.message };
    }
  };

  // Filter and search tasks
  const filteredTasks = syncTasks.filter(task => {
    const matchesFilter = filterStatus === 'all' ||
      (filterStatus === 'active' && activeSyncs.has(task.id)) ||
      (filterStatus === 'idle' && !activeSyncs.has(task.id) && task.status !== 'error') ||
      (filterStatus === 'error' && task.status === 'error') ||
      (filterStatus === 'completed' && task.status === 'completed');

    const matchesSearch = searchTerm === '' ||
      task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.sourcePath.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.destinationPath.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-3">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('syncTasksTitle')}</h1>
            {/* Socket.IO Connection Status */}
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`}></div>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {isConnected ? t('realTimeConnected') : t('offlineMode')}
              </span>
            </div>
          </div>
          <p className="text-gray-600 dark:text-gray-300 mt-1">
            {t('syncTasksDescription')}
          </p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
        >
          <PlusIcon className="w-4 h-4 mr-2" />
          {t('createNewTask')}
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-400">
              <FolderIcon className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-300">{t('totalSyncTasks')}</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{syncTasks.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-green-50 dark:bg-green-900 text-green-600 dark:text-green-400">
              <PlayIcon className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-300">{t('activeSyncs')}</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{activeSyncs.size}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-yellow-50 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-400">
              <ClockIcon className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Scheduled</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {syncTasks.filter(task => task.schedule).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-red-50 dark:bg-red-900 text-red-600 dark:text-red-400">
              <ExclamationTriangleIcon className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Errors</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {syncTasks.filter(task => task.status === 'error').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      {syncTasks.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-4">
              <div>
                <label htmlFor="filter" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('filter')} by {t('status')}
                </label>
                <select
                  id="filter"
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                >
                  <option value="all">All Tasks ({syncTasks.length})</option>
                  <option value="active">{t('activeSyncs')} ({activeSyncs.size})</option>
                  <option value="idle">Idle ({syncTasks.filter(t => !activeSyncs.has(t.id) && t.status !== 'error').length})</option>
                  <option value="completed">{t('completed')} ({syncTasks.filter(t => t.status === 'completed').length})</option>
                  <option value="error">Error ({syncTasks.filter(t => t.status === 'error').length})</option>
                </select>
              </div>
            </div>

            <div className="flex-1 max-w-md">
              <label htmlFor="search" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('search')} Tasks
              </label>
              <div className="relative">
                <input
                  type="text"
                  id="search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search by name, source, or destination..."
                  className="block w-full px-3 py-2 pl-10 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                />
                <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-4 w-4 text-gray-400 dark:text-gray-500" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tasks List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              {filterStatus === 'all' ? t('allSyncTasks') :
               filterStatus === 'active' ? t('activeTasks') :
               filterStatus === 'completed' ? t('completedTasks') :
               filterStatus === 'error' ? t('errorTasks') :
               `${filterStatus.charAt(0).toUpperCase() + filterStatus.slice(1)} Tasks`}
            </h2>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {filteredTasks.length} of {syncTasks.length} tasks
            </span>
          </div>
        </div>

        {syncTasks.length === 0 ? (
          <div className="text-center py-12">
            <FolderIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{t('noSyncTasks')}</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {t('createFirstTask')}
            </p>
            <button
              onClick={() => setShowCreateModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
            >
              <PlusIcon className="w-4 h-4 mr-2" />
              {t('createTask')}
            </button>
          </div>
        ) : filteredTasks.length === 0 ? (
          <div className="text-center py-12">
            <MagnifyingGlassIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{t('noTasksFound')}</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {t('noTasksFound')}
            </p>
            <button
              onClick={() => {
                setFilterStatus('all');
                setSearchTerm('');
              }}
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
            >
              {t('clearFilters')}
            </button>
          </div>
        ) : (
          <div className="p-6">
            <div className="space-y-4">
              {filteredTasks.map((task) => (
                <TaskCard
                  key={task.id}
                  task={task}
                  isActive={activeSyncs.has(task.id)}
                  onStart={() => startSync(task.id)}
                  onStop={() => stopSync(task.id)}
                  onEdit={(task) => {
                    setEditingTask(task);
                    setShowEditModal(true);
                  }}
                  onDelete={() => deleteSyncTask(task.id)}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Create Task Modal */}
      {showCreateModal && (
        <CreateTaskModal
          onClose={() => {
            setShowCreateModal(false);
            navigate('/sync-tasks', { replace: true });
          }}
          onCreate={handleCreateTask}
        />
      )}

      {/* Edit Task Modal */}
      {showEditModal && editingTask && (
        <CreateTaskModal
          onClose={() => {
            setShowEditModal(false);
            setEditingTask(null);
            navigate('/sync-tasks', { replace: true });
          }}
          onCreate={handleEditTask}
          initialData={editingTask}
          isEditing={true}
        />
      )}
    </div>
  );
};





// Icon components
const PlusIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
  </svg>
);

const FolderIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 9.776c.112-.017.227-.026.344-.026h15.812c.117 0 .232.009.344.026m-16.5 0a2.25 2.25 0 0 0-1.883 2.542l.857 6a2.25 2.25 0 0 0 2.227 1.932H19.05a2.25 2.25 0 0 0 2.227-1.932l.857-6a2.25 2.25 0 0 0-1.883-2.542m-16.5 0V6A2.25 2.25 0 0 1 6 3.75h3.879a1.5 1.5 0 0 1 1.06.44l2.122 2.12a1.5 1.5 0 0 0 1.06.44H18A2.25 2.25 0 0 1 20.25 9v.776" />
  </svg>
);

const PlayIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z" />
  </svg>
);

const ClockIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
);

const ExclamationTriangleIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z" />
  </svg>
);

const PencilIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10" />
  </svg>
);

const MagnifyingGlassIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
  </svg>
);

const TrashIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
  </svg>
);

export default SyncTasks;
