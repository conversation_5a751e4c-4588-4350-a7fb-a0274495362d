#!/usr/bin/env node

/**
 * Fix database constraints and duplicate token issues
 */

const { Pool } = require('pg');

console.log('🔧 Fixing Database Constraints');
console.log('==============================');

async function fixDatabaseConstraints() {
  console.log('\n1. 🐘 Connecting to PostgreSQL...');
  
  const pool = new Pool({
    host: '*************',
    port: 5432,
    database: 'syncmasterpro',
    user: 'pi',
    password: 'ubuntu'
  });

  try {
    const client = await pool.connect();
    console.log('✅ Connected to PostgreSQL');
    
    // Check sessions table structure
    console.log('\n2. 🔍 Checking sessions table...');
    const sessionsInfo = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'sessions' 
      ORDER BY ordinal_position
    `);
    
    console.log('📋 Sessions table structure:');
    sessionsInfo.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    // Check for duplicate token constraint
    console.log('\n3. 🔍 Checking constraints...');
    const constraints = await client.query(`
      SELECT constraint_name, constraint_type 
      FROM information_schema.table_constraints 
      WHERE table_name = 'sessions'
    `);
    
    console.log('📋 Sessions table constraints:');
    constraints.rows.forEach(constraint => {
      console.log(`   - ${constraint.constraint_name}: ${constraint.constraint_type}`);
    });
    
    // Clean up old sessions
    console.log('\n4. 🧹 Cleaning up old sessions...');
    const deleteResult = await client.query('DELETE FROM sessions WHERE created_at < NOW() - INTERVAL \'1 day\'');
    console.log(`✅ Deleted ${deleteResult.rowCount} old sessions`);
    
    // Check for duplicate tokens
    console.log('\n5. 🔍 Checking for duplicate tokens...');
    const duplicates = await client.query(`
      SELECT token, COUNT(*) as count 
      FROM sessions 
      GROUP BY token 
      HAVING COUNT(*) > 1
    `);
    
    if (duplicates.rows.length > 0) {
      console.log(`⚠️ Found ${duplicates.rows.length} duplicate tokens`);
      
      // Remove duplicates, keep the latest one
      for (const dup of duplicates.rows) {
        console.log(`🧹 Cleaning up duplicates for token: ${dup.token.substring(0, 20)}...`);
        await client.query(`
          DELETE FROM sessions 
          WHERE token = $1 AND id NOT IN (
            SELECT id FROM sessions WHERE token = $1 ORDER BY created_at DESC LIMIT 1
          )
        `, [dup.token]);
      }
      console.log('✅ Duplicate tokens cleaned up');
    } else {
      console.log('✅ No duplicate tokens found');
    }
    
    // Check current sessions
    console.log('\n6. 📊 Current sessions summary...');
    const sessionCount = await client.query('SELECT COUNT(*) as count FROM sessions');
    console.log(`📋 Total active sessions: ${sessionCount.rows[0].count}`);
    
    client.release();
    await pool.end();
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
    await pool.end();
    throw error;
  }
}

async function testAuthentication() {
  console.log('\n7. 🔐 Testing authentication flow...');
  
  const axios = require('axios');
  
  try {
    // Test login
    const loginResponse = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Login Status:', loginResponse.status);
    
    if (loginResponse.status === 200) {
      console.log('✅ Login successful!');
      const token = loginResponse.data.token;
      
      // Test API call with token
      const apiResponse = await axios.get('http://localhost:5001/api/clients', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000,
        validateStatus: () => true
      });
      
      console.log('📝 API Status:', apiResponse.status);
      
      if (apiResponse.status === 200) {
        console.log('✅ API call successful!');
        console.log(`📊 Found ${apiResponse.data.clients?.length || 0} clients`);
        return true;
      } else {
        console.log('❌ API call failed:', apiResponse.data);
        return false;
      }
    } else {
      console.log('❌ Login failed:', loginResponse.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Authentication test error:', error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting database constraint fix...\n');
    
    // Fix database constraints
    await fixDatabaseConstraints();
    
    // Test authentication
    const authWorking = await testAuthentication();
    
    console.log('\n📊 Summary:');
    console.log('===========');
    console.log('- Database Constraints:', '✅ Fixed');
    console.log('- Session Cleanup:', '✅ Complete');
    console.log('- Authentication Test:', authWorking ? '✅ Success' : '❌ Failed');
    
    if (authWorking) {
      console.log('\n🎉 SUCCESS! Database issues fixed and authentication working!');
      console.log('🔍 Web UI should now work without database constraint errors');
    } else {
      console.log('\n❌ Authentication still has issues');
      console.log('💡 Check web server logs for more details');
    }
    
  } catch (error) {
    console.error('\n❌ Fix failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
