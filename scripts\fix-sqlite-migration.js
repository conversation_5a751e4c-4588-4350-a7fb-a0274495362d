const Database = require('better-sqlite3');
const path = require('path');

async function fixSQLiteMigration() {
  console.log('🔧 Fixing SQLite Migration Errors\n');

  try {
    // Connect directly to SQLite database
    const dbPath = path.join(__dirname, '..', 'data', 'syncmasterpro.db');
    console.log(`📁 Database path: ${dbPath}`);
    
    const db = new Database(dbPath);
    
    console.log('1. 📋 Checking existing table schemas...');
    
    // Check existing columns in each table
    const tables = ['users', 'sync_tasks', 'sync_history', 'sessions'];
    
    for (const table of tables) {
      try {
        const columns = db.prepare(`PRAGMA table_info(${table})`).all();
        const columnNames = columns.map(col => col.name);
        
        console.log(`   📊 Table ${table}:`);
        console.log(`      Columns: ${columnNames.join(', ')}`);
        
      } catch (error) {
        console.log(`   ❌ Error checking ${table}: ${error.message}`);
      }
    }

    console.log('\n2. 🔧 Creating migration tracking table...');
    
    // Create migration tracking table to prevent duplicate migrations
    try {
      db.exec(`
        CREATE TABLE IF NOT EXISTS schema_migrations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          migration_name VARCHAR(255) UNIQUE NOT NULL,
          applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('   ✅ Migration tracking table created');
    } catch (error) {
      console.log(`   ℹ️ Migration table already exists: ${error.message}`);
    }

    console.log('\n3. 📝 Recording existing migrations...');
    
    // Record existing migrations to prevent re-running
    const migrations = [
      'add_bytes_transferred_to_sync_history',
      'add_user_fields',
      'add_client_id_to_sync_tasks', 
      'add_session_fields'
    ];
    
    const insertMigration = db.prepare(`
      INSERT OR IGNORE INTO schema_migrations (migration_name) 
      VALUES (?)
    `);
    
    for (const migration of migrations) {
      try {
        insertMigration.run(migration);
        console.log(`   ✅ Recorded migration: ${migration}`);
      } catch (error) {
        console.log(`   ⚠️ Failed to record ${migration}: ${error.message}`);
      }
    }

    console.log('\n4. 🧹 Cleaning up duplicate session tokens...');
    
    // Clean up duplicate session tokens
    try {
      const duplicates = db.prepare(`
        SELECT token, COUNT(*) as count 
        FROM sessions 
        GROUP BY token 
        HAVING COUNT(*) > 1
      `).all();
      
      if (duplicates.length > 0) {
        console.log(`   🔍 Found ${duplicates.length} duplicate tokens`);
        
        const deleteDuplicates = db.prepare(`
          DELETE FROM sessions 
          WHERE token = ? AND id NOT IN (
            SELECT MAX(id) FROM sessions WHERE token = ?
          )
        `);
        
        for (const dup of duplicates) {
          deleteDuplicates.run(dup.token, dup.token);
          console.log(`   🗑️ Cleaned up duplicates for token: ${dup.token.substring(0, 20)}...`);
        }
      } else {
        console.log('   ✅ No duplicate tokens found');
      }
    } catch (error) {
      console.log(`   ⚠️ Error cleaning duplicates: ${error.message}`);
    }

    console.log('\n5. 🔍 Verifying database integrity...');
    
    // Run integrity check
    try {
      const integrity = db.prepare('PRAGMA integrity_check').get();
      if (integrity.integrity_check === 'ok') {
        console.log('   ✅ Database integrity: OK');
      } else {
        console.log('   ⚠️ Database integrity issues found');
        console.log('   📋 Issues:', integrity);
      }
    } catch (error) {
      console.log(`   ⚠️ Integrity check failed: ${error.message}`);
    }

    console.log('\n6. 📊 Database statistics...');
    
    // Show table counts
    for (const table of tables) {
      try {
        const count = db.prepare(`SELECT COUNT(*) as count FROM ${table}`).get();
        console.log(`   📋 ${table}: ${count.count} records`);
      } catch (error) {
        console.log(`   ❌ Error counting ${table}: ${error.message}`);
      }
    }

    // Close database connection
    db.close();

    console.log('\n🎉 SQLite Migration Error Fix Completed!');
    
    console.log('\n📋 SUMMARY:');
    console.log('   ✅ Checked existing table schemas');
    console.log('   ✅ Created migration tracking table');
    console.log('   ✅ Recorded existing migrations');
    console.log('   ✅ Cleaned up duplicate session tokens');
    console.log('   ✅ Verified database integrity');
    console.log('   ✅ Showed database statistics');
    
    console.log('\n💡 NEXT STEPS:');
    console.log('   1. Restart desktop server to see if migration errors are gone');
    console.log('   2. Check web server is running on port 5001');
    console.log('   3. Test client-server connection');

  } catch (error) {
    console.error('❌ Fix failed:', error);
    console.error('Stack:', error.stack);
  }
}

// Run fix
fixSQLiteMigration();
