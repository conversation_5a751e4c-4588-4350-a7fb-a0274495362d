import React from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useSettings } from '../../../contexts/SettingsContext';
import Toggle from '../../UI/Toggle';

const SyncSettingsPanel = () => {
  const { t } = useLanguage();
  const { settings, updateSetting } = useSettings();

  const handleSyncIntervalChange = (interval) => {
    updateSetting('syncInterval', interval);
  };

  const handleMaxRetriesChange = (retries) => {
    updateSetting('maxRetries', retries);
  };

  const syncIntervalOptions = [
    { value: 60, label: t('oneMinute') },
    { value: 300, label: t('fiveMinutes') },
    { value: 600, label: t('tenMinutes') },
    { value: 1800, label: t('thirtyMinutes') },
    { value: 3600, label: t('oneHour') }
  ];

  const maxRetriesOptions = [
    { value: 1, label: '1' },
    { value: 3, label: '3' },
    { value: 5, label: '5' },
    { value: 10, label: '10' }
  ];

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t('syncSettings')}</h2>
        <p className="text-gray-600 dark:text-gray-300 mt-1">{t('syncSettingsDescription')}</p>
      </div>

      {/* Auto Sync Settings */}
      <div className="space-y-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white">{t('automaticSync')}</h3>
        
        <div className="space-y-4">
          <Toggle
            label={t('enableAutoSync')}
            description={t('enableAutoSyncDescription')}
            checked={settings.autoSync}
            onChange={(checked) => updateSetting('autoSync', checked)}
          />

          {settings.autoSync && (
            <div className="pl-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('syncInterval')}
                </label>
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">{t('syncIntervalDescription')}</p>
                
                <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                  {syncIntervalOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => handleSyncIntervalChange(option.value)}
                      className={`px-3 py-2 text-sm font-medium rounded-md border transition-colors ${
                        settings.syncInterval === option.value
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                          : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                      }`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Error Handling Settings */}
      <div className="space-y-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white">{t('errorHandling')}</h3>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('maxRetries')}
          </label>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">{t('maxRetriesDescription')}</p>
          
          <div className="grid grid-cols-4 gap-2">
            {maxRetriesOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => handleMaxRetriesChange(option.value)}
                className={`px-3 py-2 text-sm font-medium rounded-md border transition-colors ${
                  settings.maxRetries === option.value
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                    : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Performance Settings */}
      <div className="space-y-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white">{t('performanceSettings')}</h3>
        
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-blue-500 text-xl">⚡</span>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                {t('performanceOptimization')}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {t('performanceOptimizationDescription')}
              </p>
              
              <div className="mt-3 space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">{t('currentSyncInterval')}</span>
                  <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                    {syncIntervalOptions.find(opt => opt.value === settings.syncInterval)?.label || t('custom')}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">{t('maxRetryAttempts')}</span>
                  <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                    {settings.maxRetries}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">{t('autoSyncStatus')}</span>
                  <span className={`text-sm font-medium ${
                    settings.autoSync 
                      ? 'text-green-600 dark:text-green-400' 
                      : 'text-red-600 dark:text-red-400'
                  }`}>
                    {settings.autoSync ? t('enabled') : t('disabled')}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Settings */}
      <div className="space-y-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white">{t('advancedSettings')}</h3>
        
        <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-amber-400 text-xl">⚠️</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-amber-800 dark:text-amber-200">
                {t('advancedSettingsWarning')}
              </h3>
              <p className="mt-1 text-sm text-amber-700 dark:text-amber-300">
                {t('advancedSettingsWarningDescription')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SyncSettingsPanel;
