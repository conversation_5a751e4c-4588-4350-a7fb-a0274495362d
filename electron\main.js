const { app, BrowserWindow, Menu, ipc<PERSON>ain, dialog, shell, Tray, nativeImage } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';
const { autoUpdater } = require('electron-updater');
const Store = require('electron-store');
const logger = require('./logger');

// Initialize electron store
const store = new Store();

// Make store available globally for ClientManager
global.electronStore = store;

let mainWindow;
let splashWindow;
let tray = null;
let isQuitting = false;

// Startup settings
function setStartupSettings(enable) {
  if (isDev) {
    logger.logElectron('startup-settings', 'Skipping startup settings in development mode');
    return;
  }

  try {
    if (enable) {
      app.setLoginItemSettings({
        openAtLogin: true,
        openAsHidden: true,
        name: 'SyncMasterPro',
        path: process.execPath
      });
      logger.logElectron('startup-enable', 'Startup with Windows enabled');
    } else {
      app.setLoginItemSettings({
        openAtLogin: false
      });
      logger.logElectron('startup-disable', 'Startup with Windows disabled');
    }

    // Save setting to store
    store.set('startupWithWindows', enable);
  } catch (error) {
    logger.logElectron('startup-error', 'Error setting startup settings', { error: error.message });
  }
}

// Create system tray
function createTray() {
  try {
    // Create tray icon
    const iconPath = path.join(__dirname, 'assets', 'tray-icon.png');
    let trayIcon;

    try {
      trayIcon = nativeImage.createFromPath(iconPath);
    } catch (error) {
      // Fallback to a simple icon if file doesn't exist
      trayIcon = nativeImage.createEmpty();
      logger.logElectron('tray-icon-fallback', 'Using fallback tray icon');
    }

    tray = new Tray(trayIcon);

    // Create context menu
    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'SyncMasterPro',
        enabled: false
      },
      { type: 'separator' },
      {
        label: 'Show Window',
        click: () => {
          showMainWindow();
        }
      },
      {
        label: 'Start All Syncs',
        click: () => {
          if (mainWindow) {
            mainWindow.webContents.send('tray-start-all-sync');
          }
        }
      },
      {
        label: 'Stop All Syncs',
        click: () => {
          if (mainWindow) {
            mainWindow.webContents.send('tray-stop-all-sync');
          }
        }
      },
      { type: 'separator' },
      {
        label: 'Settings',
        click: () => {
          showMainWindow();
          if (mainWindow) {
            mainWindow.webContents.send('tray-open-settings');
          }
        }
      },
      { type: 'separator' },
      {
        label: 'Quit SyncMasterPro',
        click: () => {
          isQuitting = true;
          app.quit();
        }
      }
    ]);

    tray.setContextMenu(contextMenu);
    tray.setToolTip('SyncMasterPro - File Synchronization');

    // Handle tray click
    tray.on('click', () => {
      showMainWindow();
    });

    logger.logElectron('tray-created', 'System tray created successfully');
  } catch (error) {
    logger.logElectron('tray-error', 'Error creating system tray', { error: error.message });
  }
}

// Show main window
function showMainWindow() {
  if (mainWindow) {
    if (mainWindow.isMinimized()) {
      mainWindow.restore();
    }
    mainWindow.show();
    mainWindow.focus();
    logger.logElectron('window-show', 'Main window shown from tray');
  } else {
    createMainWindow();
  }
}

function createSplashWindow() {
  logger.logElectron('splash-create', 'Creating splash window');
  splashWindow = new BrowserWindow({
    width: 400,
    height: 300,
    frame: false,
    alwaysOnTop: true,
    transparent: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true
    }
  });

  splashWindow.loadFile(path.join(__dirname, 'splash.html'));
  logger.logElectron('splash-load', 'Splash window loaded');

  splashWindow.on('closed', () => {
    logger.logElectron('splash-closed', 'Splash window closed');
    splashWindow = null;
  });
}

function createMainWindow() {
  logger.logElectron('main-create', 'Creating main window');
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    show: false,
    icon: path.join(__dirname, 'assets', 'icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // Load the app
  const startUrl = isDev
    ? 'http://localhost:3000'
    : `file://${path.join(__dirname, '../build/index.html')}`;

  logger.logElectron('main-load', `Loading URL: ${startUrl}`);
  mainWindow.loadURL(startUrl);

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    logger.logElectron('main-ready', 'Main window ready to show');
    if (splashWindow) {
      logger.logElectron('splash-close', 'Closing splash window');
      splashWindow.close();
    }
    mainWindow.show();
    logger.logElectron('main-show', 'Main window shown');

    // Focus on window
    if (isDev) {
      logger.logElectron('dev-tools', 'Opening DevTools in development mode');
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    logger.logElectron('main-closed', 'Main window closed');
    mainWindow = null;
  });

  // Handle minimize to tray
  mainWindow.on('minimize', (event) => {
    const minimizeToTray = store.get('minimizeToTray', true);
    if (minimizeToTray && tray) {
      event.preventDefault();
      mainWindow.hide();
      logger.logElectron('window-minimize', 'Window minimized to tray');
    }
  });

  // Handle close to tray
  mainWindow.on('close', (event) => {
    const closeToTray = store.get('closeToTray', true);
    if (!isQuitting && closeToTray && tray) {
      event.preventDefault();
      mainWindow.hide();
      logger.logElectron('window-close', 'Window closed to tray');
    }
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Sync Task',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-new-sync');
          }
        },
        {
          label: 'Open Settings',
          accelerator: 'CmdOrCtrl+,',
          click: () => {
            mainWindow.webContents.send('menu-settings');
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Sync',
      submenu: [
        {
          label: 'Start All Sync Tasks',
          click: () => {
            mainWindow.webContents.send('menu-start-all-sync');
          }
        },
        {
          label: 'Stop All Sync Tasks',
          click: () => {
            mainWindow.webContents.send('menu-stop-all-sync');
          }
        },
        { type: 'separator' },
        {
          label: 'View Sync History',
          click: () => {
            mainWindow.webContents.send('menu-sync-history');
          }
        }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About SyncMasterPro',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About SyncMasterPro',
              message: 'SyncMasterPro v1.0.0',
              detail: 'Professional File & Folder Synchronization Software'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// IPC Handlers
ipcMain.handle('select-folder', async () => {
  logger.logElectron('ipc-select-folder', 'Folder selection dialog requested');
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory']
  });
  logger.logElectron('ipc-select-folder', 'Folder selection completed', {
    canceled: result.canceled,
    pathCount: result.filePaths?.length || 0
  });
  return result;
});

ipcMain.handle('get-app-version', () => {
  const version = app.getVersion();
  logger.logElectron('ipc-get-version', `App version requested: ${version}`);
  return version;
});

ipcMain.handle('store-get', (event, key) => {
  const value = store.get(key);
  logger.debug(`[ELECTRON] Store get: ${key}`, { hasValue: value !== undefined });
  return value;
});

ipcMain.handle('store-set', (event, key, value) => {
  store.set(key, value);
  logger.debug(`[ELECTRON] Store set: ${key}`, { valueType: typeof value });
});

ipcMain.handle('store-delete', (event, key) => {
  store.delete(key);
  logger.debug(`[ELECTRON] Store delete: ${key}`);
});

// Function to register startup IPC handlers
function registerStartupHandlers() {
  // Startup settings handlers
  ipcMain.handle('get-startup-settings', () => {
    const settings = {
      startupWithWindows: store.get('startupWithWindows', false),
      minimizeToTray: store.get('minimizeToTray', true),
      closeToTray: store.get('closeToTray', true),
      startMinimized: store.get('startMinimized', false)
    };
    logger.logElectron('get-startup-settings', 'Startup settings requested', settings);
    return settings;
  });

  ipcMain.handle('set-startup-with-windows', (event, enable) => {
    setStartupSettings(enable);
    logger.logElectron('set-startup-with-windows', `Startup with Windows ${enable ? 'enabled' : 'disabled'}`);
    return true;
  });

  ipcMain.handle('set-minimize-to-tray', (event, enable) => {
    store.set('minimizeToTray', enable);
    logger.logElectron('set-minimize-to-tray', `Minimize to tray ${enable ? 'enabled' : 'disabled'}`);
    return true;
  });

  ipcMain.handle('set-close-to-tray', (event, enable) => {
    store.set('closeToTray', enable);
    logger.logElectron('set-close-to-tray', `Close to tray ${enable ? 'enabled' : 'disabled'}`);
    return true;
  });

  ipcMain.handle('set-start-minimized', (event, enable) => {
    store.set('startMinimized', enable);
    logger.logElectron('set-start-minimized', `Start minimized ${enable ? 'enabled' : 'disabled'}`);
    return true;
  });

  // Tray control handlers
  ipcMain.handle('show-main-window', () => {
    showMainWindow();
    return true;
  });

  ipcMain.handle('hide-to-tray', () => {
    if (mainWindow && tray) {
      mainWindow.hide();
      logger.logElectron('hide-to-tray', 'Window hidden to tray via IPC');
      return true;
    }
    return false;
  });

  logger.logElectron('ipc-handlers', 'Startup IPC handlers registered');
}

// Register IPC handlers immediately when module loads
registerStartupHandlers();

// App event handlers
app.whenReady().then(() => {
  logger.logElectron('app-ready', 'Application ready');

  // Create system tray first
  createTray();

  // Check if app was started hidden (from startup)
  const startMinimized = store.get('startMinimized', false);
  const wasOpenedAsHidden = app.getLoginItemSettings().wasOpenedAsHidden;

  if (startMinimized || wasOpenedAsHidden) {
    logger.logElectron('app-start-hidden', 'App started hidden, skipping splash and main window');
    // Don't show splash or main window, just run in background
    createMainWindow(); // Create but don't show
    createMenu();
  } else {
    logger.logElectron('app-start-normal', 'App started normally, showing splash');
    createSplashWindow();

    setTimeout(() => {
      logger.logElectron('app-init', 'Creating main window and menu');
      createMainWindow();
      createMenu();
    }, 2000);
  }

  app.on('activate', () => {
    logger.logElectron('app-activate', 'Application activated');
    if (BrowserWindow.getAllWindows().length === 0) {
      logger.logElectron('app-activate', 'No windows found, creating main window');
      createMainWindow();
    } else if (mainWindow) {
      showMainWindow();
    }
  });
});

app.on('window-all-closed', () => {
  // Don't quit when all windows are closed if we have a tray icon
  // This allows the app to continue running in the background
  if (process.platform !== 'darwin' && !tray) {
    app.quit();
  }
});

app.on('before-quit', () => {
  isQuitting = true;
});

// Auto updater events
autoUpdater.checkForUpdatesAndNotify();

autoUpdater.on('update-available', () => {
  dialog.showMessageBox(mainWindow, {
    type: 'info',
    title: 'Update available',
    message: 'A new version is available. It will be downloaded in the background.',
    buttons: ['OK']
  });
});

autoUpdater.on('update-downloaded', () => {
  dialog.showMessageBox(mainWindow, {
    type: 'info',
    title: 'Update ready',
    message: 'Update downloaded. The application will restart to apply the update.',
    buttons: ['Restart', 'Later']
  }).then((result) => {
    if (result.response === 0) {
      autoUpdater.quitAndInstall();
    }
  });
});
