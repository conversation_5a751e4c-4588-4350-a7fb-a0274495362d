// Client Manager for Desktop-to-Web Server Communication
const os = require('os');
const { io } = require('socket.io-client');
const axios = require('axios');

class ClientManager {
  constructor(options = {}) {
    this.clientId = null; // Will be set in initialize()
    this.webServerUrl = options.webServerUrl || process.env.WEB_SERVER_URL || 'http://localhost:5001';
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 5000; // 5 seconds
    this.currentUser = null;
    this.userToken = null;
    this.statusReportInterval = null;
    
    // Callbacks for handling events
    this.onStatusUpdate = options.onStatusUpdate || (() => {});
    this.onCommandReceived = options.onCommandReceived || (() => {});
    this.onConnectionChange = options.onConnectionChange || (() => {});
    
    // Client ID will be set during initialize()
  }

  async getOrCreateClientId() {
    try {
      // Try to get existing client ID from storage
      let storedClientId = null;

      if (process.env.ELECTRON_ENV === 'true' && global.electronStore) {
        // Electron environment - use electron store
        storedClientId = global.electronStore.get('clientId');
      } else {
        // Node.js environment - use file system or database
        const fs = require('fs');
        const path = require('path');
        const clientIdFile = path.join(__dirname, '..', '..', 'data', 'client-id.txt');

        try {
          if (fs.existsSync(clientIdFile)) {
            storedClientId = fs.readFileSync(clientIdFile, 'utf8').trim();
          }
        } catch (error) {
          console.log('📝 Could not read client ID file, will generate new one');
        }
      }

      // Validate stored client ID
      if (storedClientId && this.isValidClientId(storedClientId)) {
        console.log(`🔄 Reusing existing client ID: ${storedClientId}`);
        return storedClientId;
      }

      // Generate new client ID
      const newClientId = this.generateClientId();
      console.log(`🆕 Generated new client ID: ${newClientId}`);

      // Store the new client ID
      await this.storeClientId(newClientId);

      return newClientId;

    } catch (error) {
      console.error('❌ Error getting/creating client ID:', error);
      // Fallback to generating new ID without storing
      return this.generateClientId();
    }
  }

  generateClientId() {
    const hostname = os.hostname();
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${hostname}-${timestamp}-${random}`;
  }

  isValidClientId(clientId) {
    // Basic validation: should contain hostname and be reasonable length
    return clientId &&
           typeof clientId === 'string' &&
           clientId.length > 10 &&
           clientId.length < 100 &&
           clientId.includes(os.hostname());
  }

  async storeClientId(clientId) {
    try {
      if (process.env.ELECTRON_ENV === 'true' && global.electronStore) {
        // Electron environment
        global.electronStore.set('clientId', clientId);
        console.log('💾 Client ID stored in Electron store');
      } else {
        // Node.js environment - store in file
        const fs = require('fs');
        const path = require('path');
        const dataDir = path.join(__dirname, '..', '..', 'data');
        const clientIdFile = path.join(dataDir, 'client-id.txt');

        // Ensure data directory exists
        if (!fs.existsSync(dataDir)) {
          fs.mkdirSync(dataDir, { recursive: true });
        }

        fs.writeFileSync(clientIdFile, clientId, 'utf8');
        console.log('💾 Client ID stored in file system');
      }
    } catch (error) {
      console.error('❌ Failed to store client ID:', error);
    }
  }

  async initialize(user, token) {
    try {
      this.currentUser = user;
      this.userToken = token;

      console.log(`🔐 Initializing client for user: ${user.email}`);

      // Get or create persistent client ID
      this.clientId = await this.getOrCreateClientId();
      console.log(`🖥️ ClientManager initialized with ID: ${this.clientId}`);

      // Register with web server
      await this.registerWithWebServer();
      
      // Connect to web server via Socket.IO
      await this.connectToWebServer();
      
      // Start status reporting
      this.startStatusReporting();
      
      console.log('✅ ClientManager initialized successfully');
      return true;
      
    } catch (error) {
      console.error('❌ ClientManager initialization failed:', error.message);
      return false;
    }
  }

  async registerWithWebServer() {
    try {
      console.log('📡 Registering with web server...');
      
      const registrationData = {
        clientId: this.clientId,
        hostname: os.hostname(),
        platform: os.platform(),
        arch: os.arch(),
        version: process.env.npm_package_version || '1.0.0',
        userId: this.currentUser.id,
        nodeVersion: process.version,
        totalMemory: os.totalmem(),
        cpuCount: os.cpus().length
      };

      const response = await axios.post(`${this.webServerUrl}/api/clients/register`, registrationData, {
        headers: {
          'Authorization': `Bearer ${this.userToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      if (response.status === 200) {
        console.log('✅ Successfully registered with web server');
        return response.data;
      } else {
        throw new Error(`Registration failed with status: ${response.status}`);
      }

    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log('⚠️ Web server not available, continuing in standalone mode');
        return null;
      }
      throw error;
    }
  }

  async connectToWebServer() {
    try {
      console.log('🔌 Connecting to web server via Socket.IO...');
      
      this.socket = io(this.webServerUrl, {
        auth: {
          token: this.userToken,
          clientId: this.clientId,
          userId: this.currentUser.id
        },
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectInterval
      });

      this.setupSocketHandlers();
      
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        this.socket.on('connect', () => {
          clearTimeout(timeout);
          this.isConnected = true;
          this.reconnectAttempts = 0;
          console.log('✅ Connected to web server');
          this.onConnectionChange(true);
          resolve();
        });

        this.socket.on('connect_error', (error) => {
          clearTimeout(timeout);
          console.log('⚠️ Failed to connect to web server, continuing in standalone mode');
          this.onConnectionChange(false);
          resolve(); // Don't reject, allow standalone mode
        });
      });

    } catch (error) {
      console.log('⚠️ Socket connection failed, continuing in standalone mode');
      this.onConnectionChange(false);
    }
  }

  setupSocketHandlers() {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      this.isConnected = true;
      this.reconnectAttempts = 0;
      console.log('🔌 Socket connected to web server');
      this.onConnectionChange(true);
      
      // Join client room
      this.socket.emit('client-register', {
        clientId: this.clientId,
        userId: this.currentUser.id
      });
    });

    this.socket.on('disconnect', () => {
      this.isConnected = false;
      console.log('🔌 Socket disconnected from web server');
      this.onConnectionChange(false);
    });

    // Remote command handling
    this.socket.on('remote-command', (command) => {
      console.log('📨 Received remote command:', command.type);
      this.handleRemoteCommand(command);
    });

    // Status request
    this.socket.on('status-request', () => {
      console.log('📊 Status requested by web server');
      this.reportStatus();
    });

    // Acknowledgments
    this.socket.on('command-ack', (ack) => {
      console.log('✅ Command acknowledged:', ack);
    });
  }

  async handleRemoteCommand(command) {
    try {
      console.log(`🎛️ Executing remote command: ${command.type}`);
      
      const result = await this.onCommandReceived(command);
      
      // Send result back to web server
      if (this.socket && this.isConnected) {
        this.socket.emit('command-result', {
          commandId: command.id,
          clientId: this.clientId,
          success: true,
          result: result,
          timestamp: new Date()
        });
      }
      
    } catch (error) {
      console.error('❌ Remote command execution failed:', error);
      
      // Send error back to web server
      if (this.socket && this.isConnected) {
        this.socket.emit('command-result', {
          commandId: command.id,
          clientId: this.clientId,
          success: false,
          error: error.message,
          timestamp: new Date()
        });
      }
    }
  }

  reportStatus(additionalData = {}) {
    if (!this.socket || !this.isConnected) return;

    const status = {
      clientId: this.clientId,
      userId: this.currentUser.id,
      status: 'online',
      timestamp: new Date(),
      systemInfo: {
        hostname: os.hostname(),
        platform: os.platform(),
        arch: os.arch(),
        uptime: os.uptime(),
        loadAverage: os.loadavg(),
        freeMemory: os.freemem(),
        totalMemory: os.totalmem(),
        cpuCount: os.cpus().length
      },
      ...additionalData
    };

    this.socket.emit('client-status', status);
    this.onStatusUpdate(status);
  }

  startStatusReporting() {
    // Report status every 30 seconds
    this.statusReportInterval = setInterval(() => {
      this.reportStatus();
    }, 30000);
    
    // Initial status report
    setTimeout(() => {
      this.reportStatus();
    }, 1000);
  }

  stopStatusReporting() {
    if (this.statusReportInterval) {
      clearInterval(this.statusReportInterval);
      this.statusReportInterval = null;
    }
  }

  async sendEvent(eventType, data) {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit('client-event', {
      clientId: this.clientId,
      userId: this.currentUser.id,
      eventType,
      data,
      timestamp: new Date()
    });
  }

  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      clientId: this.clientId,
      webServerUrl: this.webServerUrl,
      reconnectAttempts: this.reconnectAttempts
    };
  }

  async disconnect() {
    console.log('🔌 Disconnecting from web server...');

    this.stopStatusReporting();

    if (this.socket && this.currentUser) {
      console.log(`📡 Emitting client-disconnect for client: ${this.clientId}`);
      this.socket.emit('client-disconnect', {
        clientId: this.clientId,
        userId: this.currentUser.id
      });

      // Wait a moment for the event to be processed
      await new Promise(resolve => setTimeout(resolve, 500));

      this.socket.disconnect();
      this.socket = null;
      console.log('✅ Socket disconnected and client-disconnect event sent');
    }

    this.isConnected = false;
    this.onConnectionChange(false);
    console.log('✅ Disconnected from web server');
  }

  async logout() {
    console.log('🚪 Logging out client...');
    console.log(`🔍 Client ID: ${this.clientId}`);
    console.log(`🔍 User: ${this.currentUser?.email}`);

    // Disconnect from web server (this will emit client-disconnect event)
    await this.disconnect();

    // Note: Client offline status is handled by socket disconnect event
    // No need for separate API call to /api/clients/${clientId}/offline

    // Clear user data but keep client ID for reuse
    this.currentUser = null;
    this.userToken = null;

    console.log('✅ Client logout completed');
  }
}

module.exports = ClientManager;
