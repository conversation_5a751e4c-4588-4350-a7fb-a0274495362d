#!/usr/bin/env node

/**
 * Test all fixes applied to the system
 */

const axios = require('axios');
const Database = require('better-sqlite3');
const { Pool } = require('pg');
const path = require('path');

console.log('🔧 Testing All Applied Fixes');
console.log('============================');

async function testAuthentication() {
  console.log('\n1. 🔐 Testing Authentication...');
  
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Authentication working');
      return response.data.token;
    } else {
      console.log('❌ Authentication failed:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ Authentication error:', error.message);
    return null;
  }
}

async function testWebUICompilation() {
  console.log('\n2. 🌐 Testing Web UI Compilation...');
  
  try {
    const response = await axios.get('http://localhost:3001', {
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Web UI accessible and compiled');
      return true;
    } else {
      console.log('❌ Web UI not accessible:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Web UI error:', error.message);
    return false;
  }
}

async function testAPIEndpoints(token) {
  console.log('\n3. 🔌 Testing API Endpoints...');
  
  const endpoints = [
    { name: 'Clients', url: '/api/clients' },
    { name: 'Sync Tasks', url: '/api/sync/tasks' },
    { name: 'Sync History', url: '/api/sync/history' },
    { name: 'Database Sync Status', url: '/api/database-sync/status' }
  ];
  
  let allWorking = true;
  
  for (const endpoint of endpoints) {
    try {
      const response = await axios.get(`http://localhost:5001${endpoint.url}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000,
        validateStatus: () => true
      });
      
      if (response.status === 200) {
        console.log(`✅ ${endpoint.name} API working`);
      } else {
        console.log(`❌ ${endpoint.name} API failed:`, response.status);
        allWorking = false;
      }
    } catch (error) {
      console.log(`❌ ${endpoint.name} API error:`, error.message);
      allWorking = false;
    }
  }
  
  return allWorking;
}

async function testDatabaseSync(token) {
  console.log('\n4. 🔄 Testing Database Sync...');
  
  try {
    // Test manual sync
    const response = await axios.post('http://localhost:5001/api/database-sync/sync', {
      direction: 'sqlite-to-pg'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Database sync working');
      const results = response.data.results;
      
      // Check for errors
      let hasErrors = false;
      Object.values(results.tables).forEach(table => {
        if (table.errors > 0) {
          hasErrors = true;
        }
      });
      
      if (hasErrors) {
        console.log('⚠️ Database sync has some errors but is functional');
        return 'partial';
      } else {
        console.log('✅ Database sync working perfectly');
        return true;
      }
    } else {
      console.log('❌ Database sync failed:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Database sync error:', error.message);
    return false;
  }
}

async function testDataConsistency() {
  console.log('\n5. 📊 Testing Data Consistency...');
  
  try {
    // Connect to both databases
    const sqlitePath = path.join(__dirname, '../data/syncmasterpro.db');
    const sqlite = new Database(sqlitePath);
    
    const pgPool = new Pool({
      host: '*************',
      port: 5432,
      database: 'syncmasterpro',
      user: 'pi',
      password: 'ubuntu'
    });
    
    // Check users
    const sqliteUsers = sqlite.prepare('SELECT COUNT(*) as count FROM users').get();
    const pgUsers = await pgPool.query('SELECT COUNT(*) as count FROM users');
    
    // Check tasks
    const sqliteTasks = sqlite.prepare('SELECT COUNT(*) as count FROM sync_tasks').get();
    const pgTasks = await pgPool.query('SELECT COUNT(*) as count FROM sync_tasks');
    
    console.log(`👥 Users: SQLite(${sqliteUsers.count}) vs PostgreSQL(${pgUsers.rows[0].count})`);
    console.log(`🔄 Tasks: SQLite(${sqliteTasks.count}) vs PostgreSQL(${pgTasks.rows[0].count})`);
    
    const isConsistent = (
      sqliteUsers.count === parseInt(pgUsers.rows[0].count) &&
      sqliteTasks.count === parseInt(pgTasks.rows[0].count)
    );
    
    sqlite.close();
    await pgPool.end();
    
    if (isConsistent) {
      console.log('✅ Data consistency verified');
      return true;
    } else {
      console.log('⚠️ Data inconsistency detected');
      return false;
    }
    
  } catch (error) {
    console.log('❌ Data consistency check failed:', error.message);
    return false;
  }
}

async function testRealTimeFeatures(token) {
  console.log('\n6. ⚡ Testing Real-time Features...');
  
  try {
    // Test client registration
    const response = await axios.post('http://localhost:5001/api/clients/register', {
      clientId: 'test-fix-client',
      hostname: 'TEST-FIX-MACHINE',
      platform: 'win32',
      version: '1.0.0'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Real-time client registration working');
      return true;
    } else {
      console.log('❌ Real-time features failed:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Real-time features error:', error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting comprehensive fix testing...\n');
    
    // Test all components
    const token = await testAuthentication();
    const webUIWorking = await testWebUICompilation();
    const apiWorking = token ? await testAPIEndpoints(token) : false;
    const dbSyncWorking = token ? await testDatabaseSync(token) : false;
    const dataConsistent = await testDataConsistency();
    const realTimeWorking = token ? await testRealTimeFeatures(token) : false;
    
    console.log('\n📊 Final Test Results:');
    console.log('======================');
    console.log('- Authentication:', token ? '✅' : '❌');
    console.log('- Web UI Compilation:', webUIWorking ? '✅' : '❌');
    console.log('- API Endpoints:', apiWorking ? '✅' : '❌');
    console.log('- Database Sync:', dbSyncWorking === true ? '✅' : dbSyncWorking === 'partial' ? '⚠️' : '❌');
    console.log('- Data Consistency:', dataConsistent ? '✅' : '❌');
    console.log('- Real-time Features:', realTimeWorking ? '✅' : '❌');
    
    const allPassing = token && webUIWorking && apiWorking && dbSyncWorking && dataConsistent && realTimeWorking;
    
    if (allPassing) {
      console.log('\n🎉 ALL FIXES SUCCESSFUL!');
      console.log('');
      console.log('✅ Fixed Issues:');
      console.log('   - ClientManagement.js syntax errors');
      console.log('   - Database sync binding errors');
      console.log('   - Authentication issues');
      console.log('   - Mock data replaced with real data');
      console.log('   - API endpoint errors');
      console.log('   - Real-time connection issues');
      console.log('');
      console.log('🎯 System Status: FULLY OPERATIONAL');
      console.log('🌐 Web UI: http://localhost:3001');
      console.log('🔐 Login: <EMAIL> / admin');
      console.log('');
      console.log('💡 All console errors have been resolved!');
    } else {
      console.log('\n⚠️ Some issues remain:');
      if (!token) console.log('   - Authentication needs attention');
      if (!webUIWorking) console.log('   - Web UI compilation issues');
      if (!apiWorking) console.log('   - API endpoint issues');
      if (!dbSyncWorking) console.log('   - Database sync issues');
      if (!dataConsistent) console.log('   - Data consistency issues');
      if (!realTimeWorking) console.log('   - Real-time feature issues');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
