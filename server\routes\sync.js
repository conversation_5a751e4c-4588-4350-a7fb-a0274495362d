const express = require('express');
const { getDatabase } = require('../database/init');
const FileSyncEngine = require('../services/FileSyncEngine');
const RealtimeSyncEngine = require('../services/RealtimeSyncEngine');
const ExtendedSyncEngine = require('../services/ExtendedSyncEngine');

const router = express.Router();

// Global sync engine instances (will be initialized with Socket.IO)
let syncEngine = null;
let realtimeSyncEngine = null;
let extendedSyncEngine = null;

// Initialize sync engines with Socket.IO
const initSyncEngine = (io) => {
  if (!syncEngine) {
    // Check if this is desktop mode (has ELECTRON_ENV)
    const isDesktop = process.env.ELECTRON_ENV === 'true';

    if (isDesktop) {
      // Desktop mode: Use ExtendedSyncEngine with client capabilities
      extendedSyncEngine = new ExtendedSyncEngine(io);
      syncEngine = extendedSyncEngine; // For backward compatibility
      console.log('🖥️ ExtendedSyncEngine initialized for Desktop mode');
    } else {
      // Web mode: Use regular FileSyncEngine
      syncEngine = new FileSyncEngine(io);
      console.log('🌐 FileSyncEngine initialized for Web mode');
    }

    realtimeSyncEngine = new RealtimeSyncEngine(io);
    setupEventListeners();
    console.log('⚡ RealtimeSyncEngine initialized with Socket.IO');
  }
  return { syncEngine, realtimeSyncEngine, extendedSyncEngine };
};

// Setup event listeners for sync engine
const setupEventListeners = () => {
  if (!syncEngine) return;

  console.log('🔧 Setting up sync engine event listeners...');

  // Setup listeners for regular sync engine
  syncEngine.on('syncCompleted', async ({ taskId, stats }) => {
  try {
    console.log(`🎯 syncCompleted event received for task ${taskId}`);
    const db = getDatabase();

    // Get task to find user_id
    const taskResult = await db.query(
      'SELECT user_id FROM sync_tasks WHERE id = ?',
      [taskId]
    );

    if (taskResult.rows.length === 0) {
      console.error(`Task ${taskId} not found for sync completion`);
      return;
    }

    const userId = taskResult.rows[0].user_id;

    // Update task status
    await db.query(
      'UPDATE sync_tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      ['completed', taskId]
    );

    // Insert sync history
    await db.query(
      `INSERT INTO sync_history
       (task_id, user_id, status, started_at, completed_at, files_processed, bytes_transferred, details)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        taskId,
        userId, // Use the user_id from the task
        'completed',
        new Date(Date.now() - stats.duration).toISOString(),
        new Date().toISOString(),
        stats.filesProcessed,
        stats.bytesTransferred,
        JSON.stringify(stats)
      ]
    );

    console.log(`✅ Sync completed for task ${taskId}: ${stats.filesProcessed} files, ${stats.bytesTransferred} bytes`);
  } catch (error) {
    console.error('Failed to update sync completion:', error);
  }
});

syncEngine.on('syncError', async ({ taskId, error }) => {
  try {
    const db = getDatabase();

    // Get task details to get user_id
    const taskResult = await db.query(
      'SELECT user_id FROM sync_tasks WHERE id = ?',
      [taskId]
    );

    if (taskResult.rows.length === 0) {
      console.error(`Task ${taskId} not found for error handling`);
      return;
    }

    const userId = taskResult.rows[0].user_id;

    // Update task status
    await db.query(
      'UPDATE sync_tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      ['error', taskId]
    );

    // Insert error into sync history
    await db.query(
      `INSERT INTO sync_history
       (task_id, user_id, status, started_at, completed_at, files_processed, bytes_transferred, error_message, details)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        taskId,
        userId,
        'error',
        new Date().toISOString(),
        new Date().toISOString(),
        0,
        0,
        error.toString(),
        JSON.stringify({ error: error.toString(), timestamp: new Date().toISOString() })
      ]
    );

    console.error(`❌ Sync error for task ${taskId}: ${error}`);
  } catch (dbError) {
    console.error('Failed to update sync error:', dbError);
  }
});

syncEngine.on('syncCancelled', async ({ taskId }) => {
  try {
    const db = getDatabase();

    // Update task status
    await db.query(
      'UPDATE sync_tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      ['idle', taskId]
    );

    console.log(`🛑 Sync cancelled for task ${taskId}`);
  } catch (error) {
    console.error('Failed to update sync cancellation:', error);
  }
});

  // Setup listeners for real-time sync engine
  if (realtimeSyncEngine) {
    console.log('🔧 Setting up real-time sync engine event listeners...');

    realtimeSyncEngine.on('syncCompleted', async ({ taskId, stats }) => {
      try {
        console.log(`⚡ Real-time syncCompleted event received for task ${taskId}:`, stats);
        const db = getDatabase();

        // Get task to find user_id
        const taskResult = await db.query(
          'SELECT user_id FROM sync_tasks WHERE id = ?',
          [taskId]
        );

        if (taskResult.rows.length === 0) {
          console.error(`Task ${taskId} not found for real-time sync completion`);
          return;
        }

        const userId = taskResult.rows[0].user_id;

        // Insert sync history for real-time sync
        console.log(`💾 Creating real-time sync history for task ${taskId}...`);

        const historyResult = await db.query(
          `INSERT INTO sync_history
           (task_id, user_id, status, started_at, completed_at, files_processed, files_added, files_updated, files_deleted, total_size, bytes_transferred, details)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            taskId,
            userId,
            'completed',
            new Date(Date.now() - (stats.duration || 0)).toISOString(),
            new Date().toISOString(),
            stats.filesProcessed || 0,
            stats.filesAdded || 0,
            stats.filesUpdated || 0,
            stats.filesDeleted || 0,
            stats.totalSize || stats.bytesTransferred || 0,
            stats.bytesTransferred || stats.totalSize || 0,
            JSON.stringify({
              ...stats,
              type: 'realtime',
              timestamp: new Date().toISOString(),
              source: 'realtime-sync-engine'
            })
          ]
        );

        console.log(`📊 Real-time history insert result:`, historyResult);

        // Update task with latest stats
        await db.query(
          'UPDATE sync_tasks SET files_count = ?, total_size = ?, last_sync = CURRENT_TIMESTAMP WHERE id = ?',
          [stats.filesProcessed || 0, stats.bytesTransferred || stats.totalSize || 0, taskId]
        );

        console.log(`✅ Real-time sync history created for task ${taskId}`);

      } catch (error) {
        console.error(`❌ Failed to create real-time sync history for task ${taskId}:`, error);
        console.error(`❌ Error details:`, error.message);
      }
    });
  }
}; // Close setupEventListeners function

// Middleware to inject syncEngine
router.use((req, res, next) => {
  if (!syncEngine) {
    syncEngine = req.app.get('syncEngine');
  }
  next();
});

// Get all sync tasks for user
router.get('/tasks', async (req, res) => {
  try {
    const db = getDatabase();
    const result = await db.query(
      'SELECT * FROM sync_tasks WHERE user_id = ? ORDER BY created_at DESC',
      [req.userId]
    );

    const tasks = result.rows.map(task => ({
      id: task.id,
      name: task.name,
      sourcePath: task.source_path,
      destinationPath: task.destination_path,
      syncType: task.sync_type,
      schedule: task.schedule,
      filters: typeof task.filters === 'string' ? JSON.parse(task.filters) : task.filters,
      options: typeof task.options === 'string' ? JSON.parse(task.options) : task.options,
      status: task.status,
      lastSync: task.last_sync,
      filesCount: task.files_count,
      totalSize: task.total_size,
      createdAt: task.created_at,
      updatedAt: task.updated_at
    }));

    res.json({ tasks });
  } catch (error) {
    console.error('Failed to get sync tasks:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve sync tasks'
    });
  }
});

// Create new sync task
router.post('/tasks', async (req, res) => {
  try {
    const {
      name,
      sourcePath,
      destinationPath,
      syncType = 'bidirectional',
      schedule,
      filters = [],
      options = {}
    } = req.body;

    // Validation
    if (!name || !sourcePath || !destinationPath) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Name, source path, and destination path are required'
      });
    }

    const db = getDatabase();
    const result = await db.query(
      `INSERT INTO sync_tasks 
       (user_id, name, source_path, destination_path, sync_type, schedule, filters, options) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        req.userId,
        name,
        sourcePath,
        destinationPath,
        syncType,
        schedule,
        JSON.stringify(filters),
        JSON.stringify(options)
      ]
    );

    const taskId = result.insertId || result.rows[0]?.id;

    res.status(201).json({
      message: 'Sync task created successfully',
      task: {
        id: taskId,
        name,
        sourcePath,
        destinationPath,
        syncType,
        schedule,
        filters,
        options,
        status: 'idle',
        lastSync: null,
        filesCount: 0,
        totalSize: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Failed to create sync task:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create sync task'
    });
  }
});

// Update sync task
router.put('/tasks/:id', async (req, res) => {
  try {
    const taskId = req.params.id;
    const updates = req.body;

    console.log('🔄 Update task request:', { taskId, updates });

    // Map frontend field names to database field names
    const fieldMapping = {
      'name': 'name',
      'sourcePath': 'source_path',
      'destinationPath': 'destination_path',
      'syncType': 'sync_type',
      'schedule': 'schedule',
      'filters': 'filters',
      'options': 'options',
      'status': 'status'
    };

    // Build update query dynamically
    const allowedFields = ['name', 'source_path', 'destination_path', 'sync_type', 'schedule', 'filters', 'options', 'status'];
    const updateFields = [];
    const values = [];

    Object.keys(updates).forEach(key => {
      const dbField = fieldMapping[key] || key;

      if (allowedFields.includes(dbField)) {
        updateFields.push(`${dbField} = ?`);
        if (dbField === 'filters' || dbField === 'options') {
          values.push(JSON.stringify(updates[key]));
        } else {
          values.push(updates[key]);
        }
        console.log(`📝 Mapping field: ${key} → ${dbField} = ${updates[key]}`);
      } else {
        console.log(`⚠️ Ignoring unknown field: ${key}`);
      }
    });

    if (updateFields.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'No valid fields to update'
      });
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(taskId, req.userId);

    const db = getDatabase();
    await db.query(
      `UPDATE sync_tasks SET ${updateFields.join(', ')} WHERE id = ? AND user_id = ?`,
      values
    );

    res.json({ message: 'Sync task updated successfully' });
  } catch (error) {
    console.error('Failed to update sync task:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update sync task'
    });
  }
});

// Delete sync task
router.delete('/tasks/:id', async (req, res) => {
  try {
    const taskId = req.params.id;
    const db = getDatabase();

    await db.query(
      'DELETE FROM sync_tasks WHERE id = ? AND user_id = ?',
      [taskId, req.userId]
    );

    res.json({ message: 'Sync task deleted successfully' });
  } catch (error) {
    console.error('Failed to delete sync task:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to delete sync task'
    });
  }
});

// Get sync history
router.get('/history', async (req, res) => {
  try {
    const { limit = 50, offset = 0, taskId, status } = req.query;
    const db = getDatabase();

    let query = 'SELECT * FROM sync_history WHERE user_id = ?';
    const params = [req.userId];

    if (taskId) {
      query += ' AND task_id = ?';
      params.push(taskId);
    }

    if (status) {
      query += ' AND status = ?';
      params.push(status);
    }

    query += ' ORDER BY started_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const result = await db.query(query, params);

    const history = result.rows.map(entry => ({
      id: entry.id,
      taskId: entry.task_id,
      userId: entry.user_id,
      status: entry.status,
      timestamp: entry.started_at,
      startedAt: entry.started_at,
      completedAt: entry.completed_at,
      filesProcessed: entry.files_processed || 0,
      filesAdded: entry.files_added || 0,
      filesUpdated: entry.files_updated || 0,
      filesDeleted: entry.files_deleted || 0,
      totalSize: entry.total_size || 0,
      bytesTransferred: entry.bytes_transferred || 0,
      duration: entry.completed_at && entry.started_at
        ? new Date(entry.completed_at) - new Date(entry.started_at)
        : 0,
      error: entry.error_message,
      details: (() => {
        try {
          return typeof entry.details === 'string' ? JSON.parse(entry.details) : entry.details;
        } catch (error) {
          console.warn(`Failed to parse details for history entry ${entry.id}:`, error);
          return entry.details;
        }
      })()
    }));

    res.json({ history });
  } catch (error) {
    console.error('Failed to get sync history:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve sync history'
    });
  }
});

// Start sync task
router.post('/tasks/:id/start', async (req, res) => {
  try {
    const taskId = parseInt(req.params.id);
    const db = getDatabase();

    // Get task details
    const result = await db.query(
      'SELECT * FROM sync_tasks WHERE id = ? AND user_id = ?',
      [taskId, req.userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Sync task not found'
      });
    }

    const task = result.rows[0];

    // Parse JSON fields
    task.filters = typeof task.filters === 'string' ? JSON.parse(task.filters) : task.filters;
    task.options = typeof task.options === 'string' ? JSON.parse(task.options) : task.options;

    // Map database field names to expected field names for SyncEngine
    task.sourcePath = task.source_path;
    task.destinationPath = task.destination_path;
    task.syncType = task.sync_type;

    // Update task status to running
    await db.query(
      'UPDATE sync_tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      ['running', taskId]
    );

    // Start sync in background
    syncEngine.startSync(task).catch(async (error) => {
      console.error('Background sync error:', error);

      // Update task status to error
      await db.query(
        'UPDATE sync_tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        ['error', taskId]
      );
    });

    res.json({
      message: 'Sync task started successfully',
      taskId
    });
  } catch (error) {
    console.error('Failed to start sync task:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to start sync task'
    });
  }
});

// Stop sync task
router.post('/tasks/:id/stop', async (req, res) => {
  try {
    const taskId = parseInt(req.params.id);
    const db = getDatabase();

    // Stop sync
    await syncEngine.stopSync(taskId);

    // Update task status to idle
    await db.query(
      'UPDATE sync_tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?',
      ['idle', taskId, req.userId]
    );

    res.json({
      message: 'Sync task stopped successfully',
      taskId
    });
  } catch (error) {
    console.error('Failed to stop sync task:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to stop sync task'
    });
  }
});

// Get directory contents (for folder picker)
router.get('/directories', async (req, res) => {
  try {
    const { path: dirPath = '' } = req.query;
    const fs = require('fs').promises;
    const path = require('path');
    const os = require('os');

    let targetPath;

    // Handle different path scenarios
    if (!dirPath) {
      // Default to user's home directory
      targetPath = os.homedir();
    } else if (dirPath === 'drives') {
      // Return available drives on Windows
      if (process.platform === 'win32') {
        const drives = [];
        for (let i = 65; i <= 90; i++) { // A-Z
          const drive = String.fromCharCode(i) + ':\\';
          try {
            await fs.access(drive);
            drives.push({
              name: `${String.fromCharCode(i)}: Drive`,
              path: drive,
              isDirectory: true,
              isDrive: true
            });
          } catch (error) {
            // Drive doesn't exist, skip
          }
        }
        return res.json({
          currentPath: 'Computer',
          directories: drives
        });
      } else {
        targetPath = '/';
      }
    } else {
      targetPath = dirPath;
    }

    const items = await fs.readdir(targetPath);
    const directories = [];

    for (const item of items) {
      try {
        const fullPath = path.join(targetPath, item);
        const stats = await fs.stat(fullPath);

        if (stats.isDirectory()) {
          // Skip system directories that might cause issues
          if (process.platform === 'win32' &&
              ['System Volume Information', '$Recycle.Bin', 'pagefile.sys', 'hiberfil.sys'].includes(item)) {
            continue;
          }

          directories.push({
            name: item,
            path: fullPath,
            isDirectory: true,
            size: stats.size,
            modified: stats.mtime
          });
        }
      } catch (error) {
        // Skip items we can't access (permissions, etc.)
        continue;
      }
    }

    // Sort directories alphabetically
    directories.sort((a, b) => a.name.localeCompare(b.name));

    // Add special entries
    const specialEntries = [];

    // Add "Computer" entry for Windows to show drives
    if (process.platform === 'win32' && targetPath !== 'Computer') {
      specialEntries.push({
        name: '💻 Computer (Show Drives)',
        path: 'drives',
        isDirectory: true,
        isSpecial: true
      });
    }

    // Add common directories
    if (targetPath === os.homedir()) {
      const commonDirs = [
        { name: '📄 Documents', path: path.join(os.homedir(), 'Documents') },
        { name: '🖼️ Pictures', path: path.join(os.homedir(), 'Pictures') },
        { name: '🎵 Music', path: path.join(os.homedir(), 'Music') },
        { name: '🎬 Videos', path: path.join(os.homedir(), 'Videos') },
        { name: '⬇️ Downloads', path: path.join(os.homedir(), 'Downloads') },
        { name: '🖥️ Desktop', path: path.join(os.homedir(), 'Desktop') }
      ];

      for (const dir of commonDirs) {
        try {
          await fs.access(dir.path);
          specialEntries.push({
            ...dir,
            isDirectory: true,
            isSpecial: true
          });
        } catch (error) {
          // Directory doesn't exist, skip
        }
      }
    }

    res.json({
      currentPath: targetPath,
      directories: [...specialEntries, ...directories]
    });
  } catch (error) {
    console.error('Failed to get directories:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve directories'
    });
  }
});

// Get active sync status
router.get('/status', async (req, res) => {
  try {
    if (!syncEngine) {
      return res.json({ activeSyncs: [] });
    }

    const activeSyncs = syncEngine.getActiveSyncs();
    res.json({ activeSyncs });
  } catch (error) {
    console.error('Failed to get sync status:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get sync status'
    });
  }
});

// Create test history data (for development/testing)
router.post('/history/test', async (req, res) => {
  try {
    const db = getDatabase();

    // Get user's tasks
    const tasksResult = await db.query(
      'SELECT id FROM sync_tasks WHERE user_id = ? LIMIT 3',
      [req.userId]
    );

    if (tasksResult.rows.length === 0) {
      return res.status(400).json({
        error: 'No tasks found',
        message: 'Create some sync tasks first'
      });
    }

    const testEntries = [];
    const statuses = ['completed', 'error', 'completed'];

    for (let i = 0; i < tasksResult.rows.length; i++) {
      const task = tasksResult.rows[i];
      const status = statuses[i];
      const now = new Date();
      const startTime = new Date(now.getTime() - (i + 1) * 60000); // Different times
      const endTime = new Date(startTime.getTime() + 30000); // 30 seconds duration

      const entry = {
        task_id: task.id,
        user_id: req.userId,
        status: status,
        started_at: startTime.toISOString(),
        completed_at: endTime.toISOString(),
        files_processed: status === 'completed' ? Math.floor(Math.random() * 100) + 10 : 0,
        files_added: status === 'completed' ? Math.floor(Math.random() * 20) + 1 : 0,
        files_updated: status === 'completed' ? Math.floor(Math.random() * 30) + 5 : 0,
        files_deleted: status === 'completed' ? Math.floor(Math.random() * 5) : 0,
        total_size: status === 'completed' ? Math.floor(Math.random() * 1000000) + 100000 : 0,
        bytes_transferred: status === 'completed' ? Math.floor(Math.random() * 1000000) + 100000 : 0,
        error_message: status === 'error' ? 'Test error: Permission denied' : null,
        details: JSON.stringify({
          testData: true,
          duration: 30000,
          timestamp: now.toISOString()
        })
      };

      await db.query(
        `INSERT INTO sync_history
         (task_id, user_id, status, started_at, completed_at, files_processed, files_added, files_updated, files_deleted, total_size, bytes_transferred, error_message, details)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          entry.task_id, entry.user_id, entry.status, entry.started_at, entry.completed_at,
          entry.files_processed, entry.files_added, entry.files_updated, entry.files_deleted,
          entry.total_size, entry.bytes_transferred, entry.error_message, entry.details
        ]
      );

      testEntries.push(entry);
    }

    res.json({
      message: 'Test history data created successfully',
      entries: testEntries.length
    });
  } catch (error) {
    console.error('Failed to create test history:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create test history data'
    });
  }
});

// Delete single history entry
router.delete('/history/:id', async (req, res) => {
  try {
    const db = getDatabase();
    const historyId = req.params.id;

    // Check if entry exists and belongs to user
    const checkResult = await db.query(
      'SELECT id FROM sync_history WHERE id = ? AND user_id = ?',
      [historyId, req.userId]
    );

    if (checkResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'History entry not found or access denied'
      });
    }

    // Delete the entry
    await db.query(
      'DELETE FROM sync_history WHERE id = ? AND user_id = ?',
      [historyId, req.userId]
    );

    res.json({
      message: 'History entry deleted successfully'
    });
  } catch (error) {
    console.error('Failed to delete history entry:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to delete history entry'
    });
  }
});

// Delete multiple history entries
router.delete('/history', async (req, res) => {
  try {
    const db = getDatabase();
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Please provide an array of history IDs to delete'
      });
    }

    // Validate all IDs belong to the user
    const placeholders = ids.map(() => '?').join(',');
    const checkResult = await db.query(
      `SELECT id FROM sync_history WHERE id IN (${placeholders}) AND user_id = ?`,
      [...ids, req.userId]
    );

    if (checkResult.rows.length !== ids.length) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Some history entries not found or access denied'
      });
    }

    // Delete the entries
    await db.query(
      `DELETE FROM sync_history WHERE id IN (${placeholders}) AND user_id = ?`,
      [...ids, req.userId]
    );

    res.json({
      message: `${ids.length} history entries deleted successfully`,
      deletedCount: ids.length
    });
  } catch (error) {
    console.error('Failed to delete history entries:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to delete history entries'
    });
  }
});

// Clear all history for user
router.delete('/history/clear/all', async (req, res) => {
  try {
    const db = getDatabase();

    // Get count before deletion
    const countResult = await db.query(
      'SELECT COUNT(*) as count FROM sync_history WHERE user_id = ?',
      [req.userId]
    );
    const totalCount = countResult.rows[0].count;

    // Delete all history for the user
    await db.query(
      'DELETE FROM sync_history WHERE user_id = ?',
      [req.userId]
    );

    res.json({
      message: 'All history cleared successfully',
      deletedCount: totalCount
    });
  } catch (error) {
    console.error('Failed to clear all history:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to clear all history'
    });
  }
});

// Start real-time sync monitoring
router.post('/tasks/:id/realtime/start', async (req, res) => {
  try {
    const taskId = parseInt(req.params.id);
    const db = getDatabase();

    if (!realtimeSyncEngine) {
      return res.status(503).json({
        error: 'Service Unavailable',
        message: 'Real-time sync engine not initialized'
      });
    }

    // Get task details
    const result = await db.query(
      'SELECT * FROM sync_tasks WHERE id = ? AND user_id = ?',
      [taskId, req.userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Sync task not found'
      });
    }

    const task = result.rows[0];

    // Parse JSON fields
    task.filters = typeof task.filters === 'string' ? JSON.parse(task.filters) : task.filters;
    task.options = typeof task.options === 'string' ? JSON.parse(task.options) : task.options;

    // Map database field names to expected field names for SyncEngine
    task.sourcePath = task.source_path;
    task.destinationPath = task.destination_path;
    task.syncType = task.sync_type;

    console.log(`🎯 Task paths mapped: source=${task.sourcePath}, dest=${task.destinationPath}`);

    // Update task status to monitoring
    await db.query(
      'UPDATE sync_tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      ['monitoring', taskId]
    );

    // Real-time sync engine now has global event listeners
    // No need for task-specific listeners

    // Start real-time monitoring
    console.log(`🚀 Starting real-time sync engine for task ${taskId}...`);
    await realtimeSyncEngine.startRealtimeSync(task);

    console.log(`✅ Real-time sync setup completed for task ${taskId}`);

    res.json({
      message: 'Real-time sync monitoring started successfully',
      taskId,
      status: 'monitoring'
    });
  } catch (error) {
    console.error('Failed to start real-time sync:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to start real-time sync monitoring'
    });
  }
});

// Stop real-time sync monitoring
router.post('/tasks/:id/realtime/stop', async (req, res) => {
  try {
    const taskId = parseInt(req.params.id);
    const db = getDatabase();

    if (!realtimeSyncEngine) {
      return res.status(503).json({
        error: 'Service Unavailable',
        message: 'Real-time sync engine not initialized'
      });
    }

    // Stop real-time monitoring
    await realtimeSyncEngine.stopRealtimeSync(taskId);

    // Update task status to idle
    await db.query(
      'UPDATE sync_tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?',
      ['idle', taskId, req.userId]
    );

    res.json({
      message: 'Real-time sync monitoring stopped successfully',
      taskId,
      status: 'idle'
    });
  } catch (error) {
    console.error('Failed to stop real-time sync:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to stop real-time sync monitoring'
    });
  }
});

// Get real-time sync status
router.get('/realtime/status', async (req, res) => {
  try {
    if (!realtimeSyncEngine) {
      return res.json({
        status: 'unavailable',
        activeWatchers: {}
      });
    }

    const status = realtimeSyncEngine.getStatus();
    res.json({
      status: 'available',
      activeWatchers: status
    });
  } catch (error) {
    console.error('Failed to get real-time sync status:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get real-time sync status'
    });
  }
});

module.exports = { router, initSyncEngine };
