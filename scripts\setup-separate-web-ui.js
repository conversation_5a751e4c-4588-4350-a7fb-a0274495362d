#!/usr/bin/env node

/**
 * Script to setup separate Web UI for SyncMasterPro
 * This will create a new React app for web management interface
 * while keeping the existing desktop UI intact
 */

const fs = require('fs-extra');
const path = require('path');
const { exec } = require('child_process');

console.log('🚀 Setting up Separate Web UI for SyncMasterPro');
console.log('================================================');

async function createWebUI() {
  const webUIPath = path.join(__dirname, '../web-ui');
  
  try {
    // Step 1: Create web-ui directory structure
    console.log('📁 Creating web-ui directory structure...');
    await fs.ensureDir(webUIPath);
    await fs.ensureDir(path.join(webUIPath, 'src'));
    await fs.ensureDir(path.join(webUIPath, 'src/components'));
    await fs.ensureDir(path.join(webUIPath, 'src/contexts'));
    await fs.ensureDir(path.join(webUIPath, 'src/pages'));
    await fs.ensureDir(path.join(webUIPath, 'src/services'));
    await fs.ensureDir(path.join(webUIPath, 'public'));
    
    // Step 2: Create package.json for web-ui
    console.log('📦 Creating package.json for web-ui...');
    const webPackageJson = {
      "name": "syncmasterpro-web-ui",
      "version": "1.0.0",
      "description": "SyncMasterPro Web Management Interface",
      "private": true,
      "dependencies": {
        "@heroicons/react": "^2.0.18",
        "axios": "^1.6.0",
        "react": "^18.2.0",
        "react-dom": "^18.2.0",
        "react-router-dom": "^6.8.0",
        "socket.io-client": "^4.8.1",
        "tailwindcss": "^3.3.6"
      },
      "scripts": {
        "start": "react-scripts start",
        "build": "react-scripts build",
        "test": "react-scripts test",
        "eject": "react-scripts eject"
      },
      "devDependencies": {
        "react-scripts": "5.0.1"
      },
      "browserslist": {
        "production": [
          ">0.2%",
          "not dead",
          "not op_mini all"
        ],
        "development": [
          "last 1 chrome version",
          "last 1 firefox version",
          "last 1 safari version"
        ]
      }
    };
    
    await fs.writeJSON(path.join(webUIPath, 'package.json'), webPackageJson, { spaces: 2 });
    
    // Step 3: Create basic web UI structure
    console.log('🎨 Creating basic web UI components...');
    
    // Create index.html
    const indexHtml = `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="SyncMasterPro Web Management Interface" />
    <title>SyncMasterPro - Web Management</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>`;
    
    await fs.writeFile(path.join(webUIPath, 'public/index.html'), indexHtml);
    
    // Create basic App.js
    const appJs = `import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Dashboard from './pages/Dashboard';
import ClientManagement from './pages/ClientManagement';
import Analytics from './pages/Analytics';
import Settings from './pages/Settings';
import Login from './pages/Login';
import Layout from './components/Layout/Layout';
import './index.css';

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/" element={<Layout />}>
            <Route index element={<Dashboard />} />
            <Route path="clients" element={<ClientManagement />} />
            <Route path="analytics" element={<Analytics />} />
            <Route path="settings" element={<Settings />} />
          </Route>
        </Routes>
      </div>
    </Router>
  );
}

export default App;`;
    
    await fs.writeFile(path.join(webUIPath, 'src/App.js'), appJs);
    
    // Create index.js
    const indexJs = `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`;
    
    await fs.writeFile(path.join(webUIPath, 'src/index.js'), indexJs);
    
    // Create basic CSS
    const indexCss = `@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}`;
    
    await fs.writeFile(path.join(webUIPath, 'src/index.css'), indexCss);
    
    console.log('✅ Web UI structure created successfully!');
    
    // Step 4: Update root package.json
    console.log('📝 Updating root package.json...');
    const rootPackagePath = path.join(__dirname, '../package.json');
    const rootPackage = await fs.readJSON(rootPackagePath);
    
    // Add new scripts for web UI
    rootPackage.scripts = {
      ...rootPackage.scripts,
      "web-ui": "cd web-ui && npm start",
      "web-ui:build": "cd web-ui && npm run build",
      "web-ui:install": "cd web-ui && npm install",
      "start-web-management": "concurrently \"npm run server-web\" \"npm run web-ui\"",
      "build-web-management": "npm run web-ui:build"
    };
    
    await fs.writeJSON(rootPackagePath, rootPackage, { spaces: 2 });
    
    console.log('✅ Root package.json updated!');
    
    // Step 5: Create installation instructions
    const instructions = `# 🌐 SyncMasterPro Web UI Setup Complete!

## 📋 Next Steps:

### 1. Install Web UI Dependencies
\`\`\`bash
cd web-ui
npm install
\`\`\`

### 2. Start Development
\`\`\`bash
# From root directory
npm run start-web-management
\`\`\`

### 3. Build for Production
\`\`\`bash
npm run build-web-management
\`\`\`

## 🏗️ Architecture:

- **Desktop App**: \`src/\` (unchanged)
- **Web Management**: \`web-ui/src/\` (new)
- **Shared Backend**: \`server/\` (unchanged)

## 🎯 Web UI Features to Implement:

1. **Multi-Client Dashboard**
2. **Remote Client Management**
3. **Cross-Client Analytics**
4. **Centralized Settings**
5. **Real-time Monitoring**

## 🔗 URLs:

- Desktop App: http://localhost:3000 (Electron)
- Web Management: http://localhost:3001 (Browser)
- API Server: http://localhost:5001
`;
    
    await fs.writeFile(path.join(__dirname, '../WEB_UI_SETUP.md'), instructions);
    
    console.log('✅ Setup complete! Check WEB_UI_SETUP.md for next steps.');
    
  } catch (error) {
    console.error('❌ Error setting up web UI:', error);
    throw error;
  }
}

async function main() {
  try {
    await createWebUI();
    console.log('\n🎉 Web UI setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. cd web-ui && npm install');
    console.log('2. npm run start-web-management');
    console.log('3. Open http://localhost:3001 for web management');
    
  } catch (error) {
    console.error('\n❌ Setup failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { createWebUI };
