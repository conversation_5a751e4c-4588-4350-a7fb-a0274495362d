const path = require('path');
const fs = require('fs').promises;

class AdvancedFilters {
  constructor() {
    this.defaultFilters = {
      // File type exclusions
      excludeExtensions: ['.tmp', '.log', '.cache', '.bak', '~', '.swp', '.DS_Store'],
      
      // System files
      excludeSystemFiles: [
        'Thumbs.db', 'desktop.ini', '.DS_Store', '.localized',
        'System Volume Information', '$RECYCLE.BIN', '.Trash'
      ],
      
      // Directory exclusions
      excludeDirectories: [
        'node_modules', '.git', '.svn', '.hg', 'CVS',
        '.vscode', '.idea', '__pycache__', '.pytest_cache',
        'build', 'dist', 'target', 'bin', 'obj'
      ],
      
      // Size limits (in bytes)
      maxFileSize: 100 * 1024 * 1024, // 100MB default
      minFileSize: 0,
      
      // Date filters
      modifiedAfter: null,
      modifiedBefore: null,
      createdAfter: null,
      createdBefore: null,
      
      // Pattern matching
      includePatterns: [], // Glob patterns to include
      excludePatterns: [], // Glob patterns to exclude
      
      // Advanced options
      followSymlinks: false,
      includeHiddenFiles: false,
      caseSensitive: false,
      
      // Content-based filtering
      excludeEmptyFiles: false,
      excludeEmptyDirectories: false,
      
      // Custom rules
      customRules: []
    };
  }

  // Apply all filters to a file
  async shouldIncludeFile(filePath, stats, filters = {}) {
    try {
      const mergedFilters = { ...this.defaultFilters, ...filters };
      const fileName = path.basename(filePath);
      const fileExt = path.extname(filePath).toLowerCase();
      const relativePath = filePath;

      // 1. Check file extension exclusions
      if (mergedFilters.excludeExtensions.includes(fileExt)) {
        return { include: false, reason: `Excluded extension: ${fileExt}` };
      }

      // 2. Check system files
      if (mergedFilters.excludeSystemFiles.includes(fileName)) {
        return { include: false, reason: `System file: ${fileName}` };
      }

      // 3. Check directory exclusions
      const pathParts = relativePath.split(path.sep);
      for (const excludeDir of mergedFilters.excludeDirectories) {
        if (pathParts.includes(excludeDir)) {
          return { include: false, reason: `Excluded directory: ${excludeDir}` };
        }
      }

      // 4. Check hidden files
      if (!mergedFilters.includeHiddenFiles && fileName.startsWith('.')) {
        return { include: false, reason: 'Hidden file excluded' };
      }

      // 5. Check file size limits
      if (stats.isFile()) {
        if (mergedFilters.maxFileSize && stats.size > mergedFilters.maxFileSize) {
          return { 
            include: false, 
            reason: `File too large: ${this.formatFileSize(stats.size)} > ${this.formatFileSize(mergedFilters.maxFileSize)}` 
          };
        }

        if (mergedFilters.minFileSize && stats.size < mergedFilters.minFileSize) {
          return { 
            include: false, 
            reason: `File too small: ${this.formatFileSize(stats.size)} < ${this.formatFileSize(mergedFilters.minFileSize)}` 
          };
        }

        // Check empty files
        if (mergedFilters.excludeEmptyFiles && stats.size === 0) {
          return { include: false, reason: 'Empty file excluded' };
        }
      }

      // 6. Check date filters
      if (mergedFilters.modifiedAfter && stats.mtime < new Date(mergedFilters.modifiedAfter)) {
        return { include: false, reason: `Modified before ${mergedFilters.modifiedAfter}` };
      }

      if (mergedFilters.modifiedBefore && stats.mtime > new Date(mergedFilters.modifiedBefore)) {
        return { include: false, reason: `Modified after ${mergedFilters.modifiedBefore}` };
      }

      // 7. Check pattern matching
      if (mergedFilters.excludePatterns.length > 0) {
        for (const pattern of mergedFilters.excludePatterns) {
          if (this.matchesPattern(relativePath, pattern, mergedFilters.caseSensitive)) {
            return { include: false, reason: `Matches exclude pattern: ${pattern}` };
          }
        }
      }

      if (mergedFilters.includePatterns.length > 0) {
        let matchesInclude = false;
        for (const pattern of mergedFilters.includePatterns) {
          if (this.matchesPattern(relativePath, pattern, mergedFilters.caseSensitive)) {
            matchesInclude = true;
            break;
          }
        }
        if (!matchesInclude) {
          return { include: false, reason: 'Does not match any include pattern' };
        }
      }

      // 8. Apply custom rules
      for (const rule of mergedFilters.customRules) {
        const result = await this.applyCustomRule(rule, filePath, stats);
        if (!result.include) {
          return result;
        }
      }

      return { include: true, reason: 'Passed all filters' };

    } catch (error) {
      console.error('Filter error:', error);
      return { include: true, reason: 'Filter error - defaulting to include' };
    }
  }

  // Pattern matching with glob-like support
  matchesPattern(filePath, pattern, caseSensitive = false) {
    const path = caseSensitive ? filePath : filePath.toLowerCase();
    const pat = caseSensitive ? pattern : pattern.toLowerCase();

    // Convert glob pattern to regex
    const regexPattern = pat
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.')
      .replace(/\[([^\]]+)\]/g, '[$1]');

    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(path);
  }

  // Apply custom filtering rule
  async applyCustomRule(rule, filePath, stats) {
    try {
      switch (rule.type) {
        case 'content_contains':
          if (stats.isFile() && stats.size < 10 * 1024 * 1024) { // Only check files < 10MB
            const content = await fs.readFile(filePath, 'utf8');
            const contains = rule.caseSensitive 
              ? content.includes(rule.value)
              : content.toLowerCase().includes(rule.value.toLowerCase());
            
            return rule.action === 'include' 
              ? { include: contains, reason: `Content ${contains ? 'contains' : 'does not contain'}: ${rule.value}` }
              : { include: !contains, reason: `Content ${contains ? 'contains' : 'does not contain'}: ${rule.value}` };
          }
          break;

        case 'filename_regex':
          const fileName = path.basename(filePath);
          const regex = new RegExp(rule.pattern, rule.caseSensitive ? 'g' : 'gi');
          const matches = regex.test(fileName);
          
          return rule.action === 'include'
            ? { include: matches, reason: `Filename ${matches ? 'matches' : 'does not match'} regex: ${rule.pattern}` }
            : { include: !matches, reason: `Filename ${matches ? 'matches' : 'does not match'} regex: ${rule.pattern}` };

        case 'file_count_limit':
          // This would need to be implemented at the directory level
          return { include: true, reason: 'File count limit not implemented at file level' };

        default:
          return { include: true, reason: `Unknown rule type: ${rule.type}` };
      }
    } catch (error) {
      console.error('Custom rule error:', error);
      return { include: true, reason: 'Custom rule error - defaulting to include' };
    }

    return { include: true, reason: 'Custom rule passed' };
  }

  // Get filter statistics
  async analyzeDirectory(dirPath, filters = {}) {
    const stats = {
      totalFiles: 0,
      includedFiles: 0,
      excludedFiles: 0,
      totalSize: 0,
      includedSize: 0,
      excludedSize: 0,
      exclusionReasons: {},
      largestFile: null,
      oldestFile: null,
      newestFile: null
    };

    try {
      const files = await this.getAllFiles(dirPath);
      
      for (const filePath of files) {
        try {
          const fileStats = await fs.stat(filePath);
          stats.totalFiles++;
          stats.totalSize += fileStats.size;

          const filterResult = await this.shouldIncludeFile(filePath, fileStats, filters);
          
          if (filterResult.include) {
            stats.includedFiles++;
            stats.includedSize += fileStats.size;
          } else {
            stats.excludedFiles++;
            stats.excludedSize += fileStats.size;
            
            // Track exclusion reasons
            const reason = filterResult.reason;
            stats.exclusionReasons[reason] = (stats.exclusionReasons[reason] || 0) + 1;
          }

          // Track file statistics
          if (!stats.largestFile || fileStats.size > stats.largestFile.size) {
            stats.largestFile = { path: filePath, size: fileStats.size };
          }

          if (!stats.oldestFile || fileStats.mtime < stats.oldestFile.mtime) {
            stats.oldestFile = { path: filePath, mtime: fileStats.mtime };
          }

          if (!stats.newestFile || fileStats.mtime > stats.newestFile.mtime) {
            stats.newestFile = { path: filePath, mtime: fileStats.mtime };
          }

        } catch (error) {
          console.error(`Error analyzing file ${filePath}:`, error);
        }
      }

    } catch (error) {
      console.error('Error analyzing directory:', error);
    }

    return stats;
  }

  // Get all files in directory recursively
  async getAllFiles(dirPath, files = []) {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          await this.getAllFiles(fullPath, files);
        } else {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dirPath}:`, error);
    }
    
    return files;
  }

  // Utility: Format file size
  formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  // Create filter preset
  createPreset(name, filters) {
    return {
      name,
      description: `Custom filter preset: ${name}`,
      filters,
      createdAt: new Date().toISOString()
    };
  }

  // Common filter presets
  getPresets() {
    return {
      'development': this.createPreset('Development', {
        excludeDirectories: ['node_modules', '.git', 'build', 'dist', 'target', '__pycache__'],
        excludeExtensions: ['.log', '.tmp', '.cache', '.pyc', '.class'],
        maxFileSize: 50 * 1024 * 1024, // 50MB
        excludePatterns: ['*.min.js', '*.min.css', '*.map']
      }),

      'documents': this.createPreset('Documents Only', {
        includeExtensions: ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf'],
        excludeEmptyFiles: true,
        maxFileSize: 100 * 1024 * 1024 // 100MB
      }),

      'media': this.createPreset('Media Files', {
        includeExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.mp4', '.avi', '.mp3', '.wav'],
        minFileSize: 1024, // 1KB minimum
        maxFileSize: 1024 * 1024 * 1024 // 1GB maximum
      }),

      'recent': this.createPreset('Recent Files', {
        modifiedAfter: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // Last 7 days
        excludeSystemFiles: true,
        excludeEmptyFiles: true
      })
    };
  }
}

module.exports = AdvancedFilters;
