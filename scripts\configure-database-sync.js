#!/usr/bin/env node

/**
 * Configure database sync properly
 */

const axios = require('axios');

console.log('🔧 Configuring Database Sync');
console.log('============================');

async function login() {
  console.log('\n1. 🔐 Logging in...');
  
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Login successful');
      return response.data.token;
    } else {
      console.log('❌ Login failed:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return null;
  }
}

async function checkCurrentStatus(token) {
  console.log('\n2. 📊 Checking current sync status...');
  
  try {
    const response = await axios.get('http://localhost:5001/api/database-sync/status', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Status retrieved');
      console.log('📊 Current Status:', JSON.stringify(response.data, null, 2));
      return response.data;
    } else {
      console.log('❌ Status failed:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Status error:', error.message);
    return null;
  }
}

async function syncFromWebToDesktop(token) {
  console.log('\n3. 🔄 Syncing from Web (PostgreSQL) to Desktop (SQLite)...');
  console.log('   This will bring web client data to desktop database');
  
  try {
    const response = await axios.post('http://localhost:5001/api/database-sync/sync', {
      direction: 'pg-to-sqlite'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Web → Desktop sync completed');
      console.log('📊 Sync Results:', JSON.stringify(response.data, null, 2));
      return response.data;
    } else {
      console.log('❌ Web → Desktop sync failed:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Web → Desktop sync error:', error.message);
    return null;
  }
}

async function syncFromDesktopToWeb(token) {
  console.log('\n4. 🔄 Syncing from Desktop (SQLite) to Web (PostgreSQL)...');
  console.log('   This will bring desktop data to web database');
  
  try {
    const response = await axios.post('http://localhost:5001/api/database-sync/sync', {
      direction: 'sqlite-to-pg'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Desktop → Web sync completed');
      console.log('📊 Sync Results:', JSON.stringify(response.data, null, 2));
      return response.data;
    } else {
      console.log('❌ Desktop → Web sync failed:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Desktop → Web sync error:', error.message);
    return null;
  }
}

async function verifyDataSync() {
  console.log('\n5. 🔍 Verifying data synchronization...');
  
  const Database = require('better-sqlite3');
  const { Pool } = require('pg');
  const path = require('path');
  
  // Connect to SQLite (Desktop)
  const sqlitePath = path.join(__dirname, '../data/syncmasterpro.db');
  const sqlite = new Database(sqlitePath);
  
  // Connect to PostgreSQL (Web)
  const pgPool = new Pool({
    host: '*************',
    port: 5432,
    database: 'syncmasterpro',
    user: 'pi',
    password: 'ubuntu'
  });
  
  try {
    // Check Users
    const sqliteUsers = sqlite.prepare('SELECT COUNT(*) as count FROM users').get();
    const pgUsers = await pgPool.query('SELECT COUNT(*) as count FROM users');
    
    console.log(`👥 Users: SQLite(${sqliteUsers.count}) vs PostgreSQL(${pgUsers.rows[0].count})`);
    
    // Check Sync Tasks
    const sqliteTasks = sqlite.prepare('SELECT COUNT(*) as count FROM sync_tasks').get();
    const pgTasks = await pgPool.query('SELECT COUNT(*) as count FROM sync_tasks');
    
    console.log(`🔄 Tasks: SQLite(${sqliteTasks.count}) vs PostgreSQL(${pgTasks.rows[0].count})`);
    
    // Check Clients
    const sqliteClients = sqlite.prepare('SELECT COUNT(*) as count FROM clients').get();
    const pgClients = await pgPool.query('SELECT COUNT(*) as count FROM clients');
    
    console.log(`🖥️ Clients: SQLite(${sqliteClients.count}) vs PostgreSQL(${pgClients.rows[0].count})`);
    
    // Check History
    const sqliteHistory = sqlite.prepare('SELECT COUNT(*) as count FROM sync_history').get();
    const pgHistory = await pgPool.query('SELECT COUNT(*) as count FROM sync_history');
    
    console.log(`📈 History: SQLite(${sqliteHistory.count}) vs PostgreSQL(${pgHistory.rows[0].count})`);
    
    // Check if data is in sync
    const isInSync = (
      sqliteUsers.count === parseInt(pgUsers.rows[0].count) &&
      sqliteTasks.count === parseInt(pgTasks.rows[0].count) &&
      sqliteClients.count === parseInt(pgClients.rows[0].count) &&
      sqliteHistory.count === parseInt(pgHistory.rows[0].count)
    );
    
    if (isInSync) {
      console.log('✅ Databases are in sync!');
    } else {
      console.log('⚠️ Databases are NOT in sync!');
    }
    
    return {
      isInSync,
      sqlite: {
        users: sqliteUsers.count,
        tasks: sqliteTasks.count,
        clients: sqliteClients.count,
        history: sqliteHistory.count
      },
      postgresql: {
        users: parseInt(pgUsers.rows[0].count),
        tasks: parseInt(pgTasks.rows[0].count),
        clients: parseInt(pgClients.rows[0].count),
        history: parseInt(pgHistory.rows[0].count)
      }
    };
    
  } finally {
    sqlite.close();
    await pgPool.end();
  }
}

async function main() {
  try {
    console.log('🚀 Starting database sync configuration...\n');
    
    // Login
    const token = await login();
    if (!token) {
      console.log('❌ Cannot proceed without authentication');
      return;
    }
    
    // Check current status
    const initialStatus = await checkCurrentStatus(token);
    
    // Sync from web to desktop first (to get client data)
    const webToDesktopSync = await syncFromWebToDesktop(token);
    
    // Then sync from desktop to web (to ensure desktop is primary)
    const desktopToWebSync = await syncFromDesktopToWeb(token);
    
    // Verify final sync status
    const syncStatus = await verifyDataSync();
    
    console.log('\n📊 Final Summary:');
    console.log('=================');
    console.log('- Authentication:', token ? '✅' : '❌');
    console.log('- Initial Status:', initialStatus ? '✅' : '❌');
    console.log('- Web → Desktop Sync:', webToDesktopSync ? '✅' : '❌');
    console.log('- Desktop → Web Sync:', desktopToWebSync ? '✅' : '❌');
    console.log('- Data In Sync:', syncStatus.isInSync ? '✅' : '❌');
    
    if (syncStatus.isInSync) {
      console.log('\n🎉 SUCCESS! Database synchronization is working perfectly!');
      console.log('');
      console.log('📊 Final Data Counts:');
      console.log(`   - Users: ${syncStatus.sqlite.users} (both databases)`);
      console.log(`   - Tasks: ${syncStatus.sqlite.tasks} (both databases)`);
      console.log(`   - Clients: ${syncStatus.sqlite.clients} (both databases)`);
      console.log(`   - History: ${syncStatus.sqlite.history} (both databases)`);
      console.log('');
      console.log('🎯 Result:');
      console.log('   ✅ Desktop app is primary data source');
      console.log('   ✅ Web manager shows same data as desktop');
      console.log('   ✅ Client data is synchronized');
      console.log('   ✅ Both databases have consistent data');
    } else {
      console.log('\n❌ Database synchronization still has issues');
      console.log('');
      console.log('📊 Data Comparison:');
      console.log('   SQLite (Desktop):');
      console.log(`     - Users: ${syncStatus.sqlite.users}`);
      console.log(`     - Tasks: ${syncStatus.sqlite.tasks}`);
      console.log(`     - Clients: ${syncStatus.sqlite.clients}`);
      console.log(`     - History: ${syncStatus.sqlite.history}`);
      console.log('   PostgreSQL (Web):');
      console.log(`     - Users: ${syncStatus.postgresql.users}`);
      console.log(`     - Tasks: ${syncStatus.postgresql.tasks}`);
      console.log(`     - Clients: ${syncStatus.postgresql.clients}`);
      console.log(`     - History: ${syncStatus.postgresql.history}`);
    }
    
  } catch (error) {
    console.error('\n❌ Configuration failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
