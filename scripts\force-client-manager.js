#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to force initialize ClientManager
 * This bypasses the normal login flow and directly initializes ClientManager
 */

const axios = require('axios');

console.log('🔧 Force Initializing ClientManager');
console.log('===================================');

async function forceInitializeClientManager() {
  console.log('\n1. Attempting to force initialize ClientManager...');
  
  try {
    // Call a special endpoint to force initialize ClientManager
    const response = await axios.post('http://localhost:5002/api/debug/force-client-manager', {
      force: true,
      userId: 1,
      userEmail: '<EMAIL>'
    }, {
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log('📝 Force Init Response Status:', response.status);
    console.log('📝 Force Init Response:', response.data);
    
    if (response.status === 200) {
      console.log('✅ ClientManager force initialization successful!');
      return true;
    } else {
      console.log('⚠️ Force initialization returned non-success status');
      return false;
    }
  } catch (error) {
    console.log('❌ Force initialization error:', error.message);
    return false;
  }
}

async function checkClientRegistration() {
  console.log('\n2. Checking if client is now registered...');
  
  // Wait a moment for registration to complete
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  try {
    const response = await axios.get('http://localhost:5001/api/clients', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📊 Clients API Status:', response.status);
    
    if (response.status === 200) {
      console.log('📊 Registered Clients:', response.data);
      console.log('📊 Total Clients:', response.data.length);
      
      if (response.data.length > 0) {
        console.log('✅ Client successfully registered with web server!');
        
        // Show client details
        response.data.forEach((client, index) => {
          console.log(`\n📱 Client ${index + 1}:`);
          console.log(`   - ID: ${client.clientId}`);
          console.log(`   - Hostname: ${client.hostname}`);
          console.log(`   - Platform: ${client.platform}`);
          console.log(`   - Status: ${client.status}`);
          console.log(`   - Last Seen: ${client.lastSeen}`);
        });
        
        return true;
      } else {
        console.log('⚠️ No clients registered yet.');
        return false;
      }
    } else {
      console.log('📊 Clients API Response:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Client registration check error:', error.message);
    return false;
  }
}

async function testWebManagementData() {
  console.log('\n3. Testing if web management now shows real data...');
  
  try {
    // Test dashboard stats endpoint
    const statsResponse = await axios.get('http://localhost:5001/api/dashboard/stats', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log('📈 Dashboard Stats Status:', statsResponse.status);
    
    if (statsResponse.status === 200) {
      console.log('📈 Dashboard Stats:', statsResponse.data);
      
      // Check if data looks real (not mock)
      const stats = statsResponse.data;
      if (stats.totalClients > 0 || stats.activeTasks > 0) {
        console.log('✅ Web management is showing real data!');
        return true;
      } else {
        console.log('⚠️ Data might still be mock (all zeros)');
        return false;
      }
    } else {
      console.log('📈 Dashboard Stats Response:', statsResponse.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Web management data test error:', error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting ClientManager force initialization...\n');
    
    // Step 1: Force initialize ClientManager
    const clientManagerInitialized = await forceInitializeClientManager();
    
    if (!clientManagerInitialized) {
      console.log('\n❌ Failed to force initialize ClientManager');
      console.log('💡 This might be because the endpoint doesn\'t exist yet.');
      console.log('💡 Let\'s try alternative approach...');
      
      // Alternative: Try to trigger via login simulation
      console.log('\n🔄 Trying alternative: Login simulation...');
      
      try {
        const loginResponse = await axios.post('http://localhost:5002/api/auth/login', {
          email: '<EMAIL>',
          password: 'admin'
        }, {
          timeout: 10000,
          validateStatus: () => true
        });
        
        console.log('🔐 Login Response Status:', loginResponse.status);
        
        if (loginResponse.status === 200) {
          console.log('✅ Login successful! This should trigger ClientManager.');
          
          // Wait for ClientManager to initialize
          console.log('⏳ Waiting 5 seconds for ClientManager to initialize...');
          await new Promise(resolve => setTimeout(resolve, 5000));
        }
      } catch (loginError) {
        console.log('❌ Login simulation failed:', loginError.message);
      }
    }
    
    // Step 2: Check client registration
    const clientRegistered = await checkClientRegistration();
    
    // Step 3: Test web management data
    const realDataShowing = await testWebManagementData();
    
    console.log('\n📊 Test Summary:');
    console.log('================');
    console.log('- ClientManager Init:', clientManagerInitialized ? '✅ Success' : '❌ Failed');
    console.log('- Client Registration:', clientRegistered ? '✅ Success' : '❌ Failed');
    console.log('- Real Data in Web:', realDataShowing ? '✅ Success' : '⚠️ Still mock');
    
    if (clientRegistered && realDataShowing) {
      console.log('\n🎉 SUCCESS! Client-server connection is working!');
      console.log('🌐 Web management should now show real data from desktop client.');
      console.log('🔄 Refresh http://localhost:3001 to see the changes.');
    } else if (clientRegistered) {
      console.log('\n🔄 Client is registered but web data might need refresh.');
      console.log('🌐 Try refreshing http://localhost:3001');
    } else {
      console.log('\n⚠️ Client-server connection not established yet.');
      console.log('💡 Possible reasons:');
      console.log('   1. Desktop UI not loaded yet (React dev server still compiling)');
      console.log('   2. User not logged in to desktop app');
      console.log('   3. ClientManager initialization logic needs debugging');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { forceInitializeClientManager, checkClientRegistration };
