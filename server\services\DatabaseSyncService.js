const Database = require('better-sqlite3');
const { Pool } = require('pg');
const path = require('path');
const EventEmitter = require('events');

class DatabaseSyncService extends EventEmitter {
  constructor() {
    super();
    this.sqlite = null;
    this.pgPool = null;
    this.isRunning = false;
    this.syncInterval = null;
    this.consistencyInterval = null;
    this.lastSyncTime = null;
    this.lastConsistencyCheck = null;
    this.dataHashes = new Map(); // Track data hashes for smart sync
    this.config = {
      syncIntervalMinutes: 15, // Sync every 15 minutes
      enableAutoSync: true, // Enable auto sync by default
      syncDirection: 'bidirectional', // 'sqlite-to-pg', 'pg-to-sqlite', 'bidirectional'
      conflictResolution: 'latest_wins', // 'latest_wins', 'sqlite_wins', 'pg_wins'
      batchSize: 100,
      enableDeletionSync: true, // Enable deletion sync by default
      enableSmartSync: true, // Only sync changed records
      enableConsistencyCheck: true, // Verify data consistency after sync
      enableDataComparison: true, // Compare data before sync
      consistencyCheckInterval: 60 // Check consistency every 60 minutes
    };
  }

  // Initialize database connections
  async initialize(config = {}) {
    try {
      this.config = { ...this.config, ...config };
      
      console.log('🔄 Initializing Database Sync Service...');
      
      // Initialize SQLite
      const sqlitePath = path.join(__dirname, '../../data/syncmasterpro.db');
      this.sqlite = new Database(sqlitePath);
      console.log('✅ SQLite connected');
      
      // Initialize PostgreSQL
      this.pgPool = new Pool({
        user: process.env.PG_USER || 'pi',
        host: process.env.PG_HOST || '*************',
        database: process.env.PG_DATABASE || 'syncmasterpro',
        password: process.env.PG_PASSWORD || 'ubuntu',
        port: process.env.PG_PORT || 5432,
      });
      
      // Test PostgreSQL connection
      await this.pgPool.query('SELECT NOW()');
      console.log('✅ PostgreSQL connected');
      
      this.emit('initialized');
      console.log('✅ Database Sync Service initialized');
      
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Database Sync Service:', error);
      this.emit('error', error);
      throw error;
    }
  }

  // Start automatic sync
  startAutoSync() {
    if (this.isRunning) {
      console.log('⚠️ Auto sync already running');
      return;
    }

    if (!this.config.enableAutoSync) {
      console.log('⚠️ Auto sync is disabled in config');
      return;
    }

    console.log(`🚀 Starting auto sync (every ${this.config.syncIntervalMinutes} minutes)`);
    console.log(`🔍 Starting consistency checks (every ${this.config.consistencyCheckInterval} minutes)`);

    this.isRunning = true;

    // Initial sync
    this.performSync().catch(error => {
      console.error('❌ Initial sync failed:', error);
    });

    // Initial consistency check
    setTimeout(() => {
      this.performConsistencyCheck().catch(error => {
        console.error('❌ Initial consistency check failed:', error);
      });
    }, 30000); // Wait 30 seconds after initial sync

    // Schedule periodic sync
    this.syncInterval = setInterval(() => {
      this.performSync().catch(error => {
        console.error('❌ Scheduled sync failed:', error);
      });
    }, this.config.syncIntervalMinutes * 60 * 1000);

    // Schedule periodic consistency checks
    this.consistencyInterval = setInterval(() => {
      this.performConsistencyCheck().catch(error => {
        console.error('❌ Scheduled consistency check failed:', error);
      });
    }, this.config.consistencyCheckInterval * 60 * 1000);

    this.emit('started');
    console.log('✅ Auto sync and consistency monitoring started');
  }

  // Stop automatic sync
  stopAutoSync() {
    if (!this.isRunning) {
      console.log('⚠️ Auto sync not running');
      return;
    }

    console.log('🛑 Stopping auto sync and consistency monitoring...');

    this.isRunning = false;

    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    if (this.consistencyInterval) {
      clearInterval(this.consistencyInterval);
      this.consistencyInterval = null;
    }

    this.emit('stopped');
    console.log('✅ Auto sync and consistency monitoring stopped');
  }

  // Perform sync based on configuration
  async performSync() {
    try {
      console.log('🔄 Starting database sync...');
      const startTime = Date.now();

      this.emit('syncStarted');

      // Check if sync is needed (smart sync)
      if (this.config.enableSmartSync) {
        const syncNeeded = await this.needsSync();
        if (!syncNeeded) {
          console.log('✅ Databases are already in sync - skipping sync');
          const quickResult = {
            direction: this.config.syncDirection,
            startTime: new Date().toISOString(),
            endTime: new Date().toISOString(),
            duration: Date.now() - startTime,
            skipped: true,
            reason: 'Data already consistent'
          };
          this.emit('syncSkipped', quickResult);
          return quickResult;
        }
      }

      let syncResults = {
        direction: this.config.syncDirection,
        startTime: new Date().toISOString(),
        tables: {}
      };

      switch (this.config.syncDirection) {
        case 'sqlite-to-pg':
          syncResults = await this.syncSQLiteToPostgreSQL();
          break;
        case 'pg-to-sqlite':
          syncResults = await this.syncPostgreSQLToSQLite();
          break;
        case 'bidirectional':
          syncResults = await this.syncBidirectional();
          break;
        default:
          throw new Error(`Unknown sync direction: ${this.config.syncDirection}`);
      }

      const duration = Date.now() - startTime;
      syncResults.duration = duration;
      syncResults.endTime = new Date().toISOString();

      this.lastSyncTime = new Date();

      // Perform consistency check after sync if enabled
      if (this.config.enableConsistencyCheck) {
        console.log('🔍 Performing post-sync consistency check...');
        const consistencyResult = await this.compareData();
        syncResults.consistencyCheck = consistencyResult;

        if (!consistencyResult.isConsistent) {
          console.log('⚠️ Post-sync consistency check failed - data may still be inconsistent');
        }
      }

      this.emit('syncCompleted', syncResults);
      console.log(`✅ Database sync completed in ${duration}ms`);

      return syncResults;

    } catch (error) {
      console.error('❌ Database sync failed:', error);
      this.emit('syncFailed', error);
      throw error;
    }
  }

  // Sync from SQLite to PostgreSQL
  async syncSQLiteToPostgreSQL() {
    console.log('📤 Syncing SQLite → PostgreSQL...');

    const results = { direction: 'sqlite-to-pg', tables: {} };
    const tables = ['users', 'sync_tasks', 'sync_history', 'desktop_clients', 'sessions'];

    for (const table of tables) {
      try {
        console.log(`📋 Syncing table: ${table}`);

        // Get data from SQLite
        const sqliteData = this.sqlite.prepare(`SELECT * FROM ${table}`).all();
        console.log(`📊 Found ${sqliteData.length} records in SQLite ${table}`);

        let synced = 0, skipped = 0, errors = 0, deleted = 0;

        // Sync each record
        for (const record of sqliteData) {
          try {
            await this.syncRecordToPostgreSQL(table, record);
            synced++;
          } catch (error) {
            if (error.code === '23505') { // Unique constraint violation
              skipped++;
              console.log(`⏭️ Skipped ${table} record (duplicate): ${error.detail || error.message}`);
            } else {
              errors++;
              console.error(`❌ Error syncing ${table} record:`, {
                message: error.message,
                code: error.code,
                detail: error.detail,
                constraint: error.constraint,
                record: Object.keys(record).reduce((obj, key) => {
                  obj[key] = typeof record[key] === 'string' && record[key].length > 50
                    ? record[key].substring(0, 50) + '...'
                    : record[key];
                  return obj;
                }, {})
              });
            }
          }
        }

        // Handle deletions: remove records from PostgreSQL that don't exist in SQLite
        if (this.config.enableDeletionSync !== false) {
          deleted = await this.handleDeletionsInPostgreSQL(table, sqliteData);
        }

        results.tables[table] = { synced, skipped, errors, deleted };
        console.log(`✅ ${table}: ${synced} synced, ${skipped} skipped, ${errors} errors, ${deleted} deleted`);

      } catch (error) {
        console.error(`❌ Failed to sync table ${table}:`, error);
        results.tables[table] = { synced: 0, skipped: 0, errors: 1, deleted: 0 };
      }
    }

    return results;
  }

  // Sync from PostgreSQL to SQLite
  async syncPostgreSQLToSQLite() {
    console.log('📥 Syncing PostgreSQL → SQLite...');

    const results = { direction: 'pg-to-sqlite', tables: {} };
    const tables = ['users', 'sync_tasks', 'sync_history', 'desktop_clients', 'sessions'];

    for (const table of tables) {
      try {
        console.log(`📋 Syncing table: ${table}`);

        // Get data from PostgreSQL
        const pgResult = await this.pgPool.query(`SELECT * FROM ${table}`);
        const pgData = pgResult.rows;
        console.log(`📊 Found ${pgData.length} records in PostgreSQL ${table}`);

        let synced = 0, skipped = 0, errors = 0, deleted = 0;

        // Sync each record
        for (const record of pgData) {
          try {
            await this.syncRecordToSQLite(table, record);
            synced++;
          } catch (error) {
            if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
              skipped++;
              console.log(`⏭️ Skipped ${table} record (duplicate): ${error.message}`);
            } else {
              errors++;
              console.error(`❌ Error syncing ${table} record:`, {
                message: error.message,
                code: error.code,
                record: Object.keys(record).reduce((obj, key) => {
                  obj[key] = typeof record[key] === 'string' && record[key].length > 50
                    ? record[key].substring(0, 50) + '...'
                    : record[key];
                  return obj;
                }, {})
              });
            }
          }
        }

        // Handle deletions: remove records from SQLite that don't exist in PostgreSQL
        if (this.config.enableDeletionSync !== false) {
          deleted = await this.handleDeletionsInSQLite(table, pgData);
        }

        results.tables[table] = { synced, skipped, errors, deleted };
        console.log(`✅ ${table}: ${synced} synced, ${skipped} skipped, ${errors} errors, ${deleted} deleted`);

      } catch (error) {
        console.error(`❌ Failed to sync table ${table}:`, error);
        results.tables[table] = { synced: 0, skipped: 0, errors: 1, deleted: 0 };
      }
    }

    return results;
  }

  // Bidirectional sync with conflict resolution
  async syncBidirectional() {
    console.log('🔄 Performing bidirectional sync...');

    const results = { direction: 'bidirectional', tables: {} };

    // First sync SQLite → PostgreSQL
    const sqliteToPg = await this.syncSQLiteToPostgreSQL();

    // Then sync PostgreSQL → SQLite
    const pgToSqlite = await this.syncPostgreSQLToSQLite();

    // Combine results
    Object.keys(sqliteToPg.tables).forEach(table => {
      results.tables[table] = {
        sqliteToPg: sqliteToPg.tables[table],
        pgToSqlite: pgToSqlite.tables[table] || { synced: 0, skipped: 0, errors: 0 }
      };
    });

    return results;
  }

  // Sync specific tables only (for real-time sync)
  async syncSpecificTables(tables) {
    console.log(`🎯 Performing targeted sync for tables: ${tables.join(', ')}`);

    const startTime = Date.now();
    const results = {
      direction: this.config.syncDirection,
      type: 'targeted',
      tables: {},
      targetedTables: tables
    };

    try {
      switch (this.config.syncDirection) {
        case 'sqlite-to-pg':
          results.tables = await this.syncSpecificTablesToPostgreSQL(tables);
          break;
        case 'pg-to-sqlite':
          results.tables = await this.syncSpecificTablesToSQLite(tables);
          break;
        case 'bidirectional':
          const sqliteToPg = await this.syncSpecificTablesToPostgreSQL(tables);
          const pgToSqlite = await this.syncSpecificTablesToSQLite(tables);

          tables.forEach(table => {
            results.tables[table] = {
              sqliteToPg: sqliteToPg[table] || { synced: 0, skipped: 0, errors: 0 },
              pgToSqlite: pgToSqlite[table] || { synced: 0, skipped: 0, errors: 0 }
            };
          });
          break;
        default:
          throw new Error(`Unknown sync direction: ${this.config.syncDirection}`);
      }

      const duration = Date.now() - startTime;
      results.duration = duration;
      results.endTime = new Date().toISOString();

      this.lastSyncTime = new Date();

      // Don't emit events for targeted syncs to prevent infinite loops
      // this.emit('syncCompleted', results);
      console.log(`✅ Targeted database sync completed in ${duration}ms`);

      return results;

    } catch (error) {
      console.error('❌ Targeted database sync failed:', error);
      throw error;
    }
  }

  // Sync specific tables from SQLite to PostgreSQL
  async syncSpecificTablesToPostgreSQL(tables) {
    const results = {};

    for (const table of tables) {
      try {
        console.log(`📋 Syncing table: ${table}`);

        // Get data from SQLite
        const sqliteData = this.sqlite.prepare(`SELECT * FROM ${table}`).all();
        console.log(`📊 Found ${sqliteData.length} records in SQLite ${table}`);

        let synced = 0, skipped = 0, errors = 0, deleted = 0;

        // Sync each record
        for (const record of sqliteData) {
          try {
            await this.syncRecordToPostgreSQL(table, record);
            synced++;
          } catch (error) {
            if (error.code === '23505') { // Unique constraint violation
              skipped++;
            } else {
              errors++;
              console.error(`❌ Error syncing ${table} record:`, error.message);
            }
          }
        }

        // Handle deletions if enabled
        if (this.config.enableDeletionSync !== false) {
          deleted = await this.handleDeletionsInPostgreSQL(table, sqliteData);
        }

        results[table] = { synced, skipped, errors, deleted };
        console.log(`✅ ${table}: ${synced} synced, ${skipped} skipped, ${errors} errors, ${deleted} deleted`);

      } catch (error) {
        console.error(`❌ Failed to sync table ${table}:`, error);
        results[table] = { synced: 0, skipped: 0, errors: 1, deleted: 0 };
      }
    }

    return results;
  }

  // Sync specific tables from PostgreSQL to SQLite
  async syncSpecificTablesToSQLite(tables) {
    const results = {};

    for (const table of tables) {
      try {
        console.log(`📋 Syncing table: ${table}`);

        // Get data from PostgreSQL
        const pgResult = await this.pgPool.query(`SELECT * FROM ${table}`);
        const pgData = pgResult.rows;
        console.log(`📊 Found ${pgData.length} records in PostgreSQL ${table}`);

        let synced = 0, skipped = 0, errors = 0, deleted = 0;

        // Sync each record
        for (const record of pgData) {
          try {
            await this.syncRecordToSQLite(table, record);
            synced++;
          } catch (error) {
            if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
              skipped++;
            } else {
              errors++;
              console.error(`❌ Error syncing ${table} record:`, error.message);
            }
          }
        }

        // Handle deletions if enabled
        if (this.config.enableDeletionSync !== false) {
          deleted = await this.handleDeletionsInSQLite(table, pgData);
        }

        results[table] = { synced, skipped, errors, deleted };
        console.log(`✅ ${table}: ${synced} synced, ${skipped} skipped, ${errors} errors, ${deleted} deleted`);

      } catch (error) {
        console.error(`❌ Failed to sync table ${table}:`, error);
        results[table] = { synced: 0, skipped: 0, errors: 1, deleted: 0 };
      }
    }

    return results;
  }

  // Sync single record to PostgreSQL
  async syncRecordToPostgreSQL(table, record) {
    // Get PostgreSQL table schema to filter compatible columns
    const pgSchema = await this.pgPool.query(`
      SELECT column_name FROM information_schema.columns
      WHERE table_name = $1
    `, [table]);

    const pgColumns = pgSchema.rows.map(row => row.column_name);

    // Filter record to only include columns that exist in PostgreSQL
    const filteredRecord = {};
    Object.keys(record).forEach(key => {
      if (pgColumns.includes(key)) {
        let value = record[key];

        // Handle JSON fields - convert TEXT to JSONB if needed
        if ((table === 'sync_tasks' && ['filters', 'options'].includes(key)) ||
            (table === 'sync_history' && key === 'details') ||
            (table === 'users' && key === 'settings')) {
          if (typeof value === 'string' && value) {
            try {
              // Try to parse as JSON, if fails keep as string
              JSON.parse(value);
              // If successful, keep as string (PostgreSQL will convert to JSONB)
            } catch {
              // If not valid JSON, wrap in quotes or set to null
              value = value === '' ? null : `"${value}"`;
            }
          }
        }

        filteredRecord[key] = value;
      }
    });

    // Validate required fields for specific tables
    if (table === 'users') {
      // Ensure required fields are present
      const requiredFields = ['email', 'password', 'name'];
      for (const field of requiredFields) {
        if (!filteredRecord[field]) {
          throw new Error(`Required field '${field}' is missing or null for users table`);
        }
      }
    }

    // Skip if no compatible columns
    if (Object.keys(filteredRecord).length === 0) {
      throw new Error(`No compatible columns found for table ${table}`);
    }

    const columns = Object.keys(filteredRecord);
    const values = Object.values(filteredRecord);
    const placeholders = values.map((_, i) => `$${i + 1}`).join(', ');

    const conflictColumns = this.getConflictColumns(table);
    const updateSet = columns
      .filter(col => !conflictColumns.includes(col))
      .map(col => `${col} = EXCLUDED.${col}`)
      .join(', ');

    const query = `
      INSERT INTO ${table} (${columns.join(', ')})
      VALUES (${placeholders})
      ON CONFLICT (${conflictColumns.join(', ')}) DO UPDATE SET
        ${updateSet}
    `;

    await this.pgPool.query(query, values);
  }

  // Sync single record to SQLite
  async syncRecordToSQLite(table, record) {
    // Convert JSONB fields back to TEXT for SQLite
    const convertedRecord = {};
    Object.keys(record).forEach(key => {
      let value = record[key];

      // Handle JSONB to TEXT conversion
      if ((table === 'sync_tasks' && ['filters', 'options'].includes(key)) ||
          (table === 'sync_history' && key === 'details') ||
          (table === 'users' && key === 'settings')) {
        if (value && typeof value === 'object') {
          value = JSON.stringify(value);
        }
      }

      // Handle Date objects - convert to ISO string
      if (value instanceof Date) {
        value = value.toISOString();
      }

      // Handle other object types - convert to JSON string
      if (value && typeof value === 'object' && value.constructor === Object) {
        value = JSON.stringify(value);
      }

      // Handle arrays - convert to JSON string
      if (Array.isArray(value)) {
        value = JSON.stringify(value);
      }

      // Ensure only SQLite-compatible types
      if (value !== null &&
          typeof value !== 'string' &&
          typeof value !== 'number' &&
          typeof value !== 'bigint' &&
          !Buffer.isBuffer(value)) {
        // Convert anything else to string
        value = String(value);
      }

      convertedRecord[key] = value;
    });

    const columns = Object.keys(convertedRecord);
    const values = Object.values(convertedRecord);
    const placeholders = columns.map(() => '?').join(', ');

    const query = `
      INSERT OR REPLACE INTO ${table} (${columns.join(', ')})
      VALUES (${placeholders})
    `;

    const stmt = this.sqlite.prepare(query);
    stmt.run(values);
  }

  // Handle deletions in PostgreSQL (remove records that don't exist in SQLite)
  async handleDeletionsInPostgreSQL(table, sqliteData) {
    try {
      console.log(`🗑️ Checking for deletions in PostgreSQL ${table}...`);

      // Get all records from PostgreSQL
      const pgResult = await this.pgPool.query(`SELECT * FROM ${table}`);
      const pgData = pgResult.rows;

      // Create a map of SQLite records by primary key
      const sqliteMap = new Map();
      const primaryKey = this.getPrimaryKey(table);

      sqliteData.forEach(record => {
        sqliteMap.set(record[primaryKey], record);
      });

      let deletedCount = 0;

      // Find PostgreSQL records that don't exist in SQLite
      for (const pgRecord of pgData) {
        const pkValue = pgRecord[primaryKey];

        if (!sqliteMap.has(pkValue)) {
          // This record exists in PostgreSQL but not in SQLite - delete it
          if (this.canDeleteRecord(table, pgRecord)) {
            await this.pgPool.query(`DELETE FROM ${table} WHERE ${primaryKey} = $1`, [pkValue]);
            deletedCount++;
            console.log(`🗑️ Deleted ${table} record with ${primaryKey}=${pkValue} from PostgreSQL`);
          }
        }
      }

      return deletedCount;

    } catch (error) {
      console.error(`❌ Error handling deletions in PostgreSQL ${table}:`, error);
      return 0;
    }
  }

  // Handle deletions in SQLite (remove records that don't exist in PostgreSQL)
  async handleDeletionsInSQLite(table, pgData) {
    try {
      console.log(`🗑️ Checking for deletions in SQLite ${table}...`);

      // Get all records from SQLite
      const sqliteData = this.sqlite.prepare(`SELECT * FROM ${table}`).all();

      // Create a map of PostgreSQL records by primary key
      const pgMap = new Map();
      const primaryKey = this.getPrimaryKey(table);

      pgData.forEach(record => {
        pgMap.set(record[primaryKey], record);
      });

      let deletedCount = 0;

      // Find SQLite records that don't exist in PostgreSQL
      for (const sqliteRecord of sqliteData) {
        const pkValue = sqliteRecord[primaryKey];

        if (!pgMap.has(pkValue)) {
          // This record exists in SQLite but not in PostgreSQL - delete it
          if (this.canDeleteRecord(table, sqliteRecord)) {
            const deleteStmt = this.sqlite.prepare(`DELETE FROM ${table} WHERE ${primaryKey} = ?`);
            deleteStmt.run(pkValue);
            deletedCount++;
            console.log(`🗑️ Deleted ${table} record with ${primaryKey}=${pkValue} from SQLite`);
          }
        }
      }

      return deletedCount;

    } catch (error) {
      console.error(`❌ Error handling deletions in SQLite ${table}:`, error);
      return 0;
    }
  }

  // Get primary key for each table
  getPrimaryKey(table) {
    const primaryKeyMap = {
      users: 'id',
      sync_tasks: 'id',
      sync_history: 'id'
    };

    return primaryKeyMap[table] || 'id';
  }

  // Check if a record can be safely deleted
  canDeleteRecord(table, record) {
    // Add safety checks here if needed
    // For example, don't delete admin users, or records with certain statuses

    if (table === 'users') {
      // Don't delete admin users
      if (record.email === '<EMAIL>' || record.role === 'admin') {
        console.log(`⚠️ Skipping deletion of admin user: ${record.email}`);
        return false;
      }
    }

    if (table === 'sync_tasks') {
      // Don't delete running tasks
      if (record.status === 'running' || record.status === 'syncing') {
        console.log(`⚠️ Skipping deletion of running task: ${record.name}`);
        return false;
      }
    }

    return true;
  }

  // Compare data between databases
  async compareData() {
    console.log('🔍 Comparing data between databases...');

    const comparison = {
      timestamp: new Date().toISOString(),
      tables: {},
      isConsistent: true,
      differences: []
    };

    const tables = ['users', 'sync_tasks', 'sync_history'];

    for (const table of tables) {
      try {
        // Get data from both databases
        const sqliteData = this.sqlite.prepare(`SELECT * FROM ${table} ORDER BY id`).all();
        const pgResult = await this.pgPool.query(`SELECT * FROM ${table} ORDER BY id`);
        const pgData = pgResult.rows;

        // Compare counts
        const sqliteCount = sqliteData.length;
        const pgCount = pgData.length;

        // Use content-based comparison for tables with JSON fields
        let isTableConsistent;
        let sqliteChecksum, pgChecksum;

        if (this.hasJsonFields(table)) {
          // Content-based comparison for JSON tables
          isTableConsistent = (sqliteCount === pgCount && this.compareDataContent(sqliteData, pgData, table));
          sqliteChecksum = 'content-based';
          pgChecksum = 'content-based';
        } else {
          // Traditional checksum comparison for non-JSON tables
          sqliteChecksum = this.generateDataChecksum(sqliteData, table);
          pgChecksum = this.generateDataChecksum(pgData, table);
          isTableConsistent = (sqliteCount === pgCount && sqliteChecksum === pgChecksum);
        }

        comparison.tables[table] = {
          sqliteCount,
          pgCount,
          sqliteChecksum,
          pgChecksum,
          isConsistent: isTableConsistent
        };

        if (!isTableConsistent) {
          comparison.isConsistent = false;
          comparison.differences.push({
            table,
            issue: sqliteCount !== pgCount ? 'count_mismatch' : 'data_mismatch',
            sqliteCount,
            pgCount
          });

          // Debug logging for data mismatch
          if (sqliteCount === pgCount && sqliteChecksum !== pgChecksum) {
            console.log(`🔍 Debug: ${table} data mismatch details:`);
            console.log(`   SQLite checksum: ${sqliteChecksum}`);
            console.log(`   PostgreSQL checksum: ${pgChecksum}`);

            // Sample first record for debugging
            if (sqliteData.length > 0 && pgData.length > 0) {
              console.log(`   SQLite sample:`, this.debugRecord(sqliteData[0], table));
              console.log(`   PostgreSQL sample:`, this.debugRecord(pgData[0], table));
            }
          }
        }

        console.log(`📊 ${table}: SQLite(${sqliteCount}) vs PostgreSQL(${pgCount}) - ${isTableConsistent ? '✅' : '❌'}`);

      } catch (error) {
        console.error(`❌ Error comparing ${table}:`, error);
        comparison.tables[table] = { error: error.message };
        comparison.isConsistent = false;
      }
    }

    return comparison;
  }

  // Generate checksum for data consistency with table context
  generateDataChecksum(data, table = null) {
    const crypto = require('crypto');
    const normalizedData = data.map(record => {
      // Sort keys and normalize values for consistent hashing
      const sortedRecord = {};
      Object.keys(record).sort().forEach(key => {
        let value = record[key];

        // Normalize different data types with table context
        value = this.normalizeValueForChecksum(key, value, table);

        sortedRecord[key] = value;
      });
      return sortedRecord;
    });

    const dataString = JSON.stringify(normalizedData);
    return crypto.createHash('md5').update(dataString).digest('hex');
  }

  // Universal data normalizer for consistent checksum generation
  normalizeValueForChecksum(key, value, table = null) {
    // Handle null/undefined
    if (value === null || value === undefined) {
      return null;
    }

    // Handle datetime fields
    if (this.isDateTimeField(key)) {
      return this.normalizeDateTimeValue(value);
    }

    // Handle JSON/JSONB fields with UNIVERSAL normalization
    if (this.isJsonField(key) || (table && this.getTableJsonFields(table).includes(key))) {
      return this.universalJsonNormalize(value);
    }

    // Handle boolean fields
    if (typeof value === 'boolean') {
      return value ? 1 : 0; // Normalize to integer
    }

    // Handle numeric fields
    if (typeof value === 'number' || typeof value === 'bigint') {
      return Number(value); // Ensure consistent number format
    }

    // Handle string fields with universal parsing
    if (typeof value === 'string') {
      return this.universalStringNormalize(key, value);
    }

    // Handle objects (for PostgreSQL JSONB)
    if (typeof value === 'object') {
      return this.universalJsonNormalize(value);
    }

    return String(value); // Fallback to string
  }

  // Universal JSON normalizer - handles ALL JSON formats
  universalJsonNormalize(value) {
    try {
      let parsedValue;

      if (typeof value === 'object' && value !== null) {
        // Already an object - use directly
        parsedValue = value;
      } else if (typeof value === 'string') {
        // Parse string to object
        parsedValue = this.parseJsonString(value);
      } else {
        // Convert to string representation
        return String(value);
      }

      // Deep normalize and return canonical string
      const normalized = this.deepNormalizeJson(parsedValue);
      return this.canonicalJsonString(normalized);

    } catch (error) {
      // If all parsing fails, return cleaned string
      return this.cleanJsonString(value);
    }
  }

  // Universal string normalizer
  universalStringNormalize(key, value) {
    // Clean whitespace
    let cleaned = value.trim();

    // Check if it's a JSON-like string
    if (this.looksLikeJson(cleaned)) {
      return this.universalJsonNormalize(cleaned);
    }

    return cleaned;
  }

  // Check if field is a datetime field
  isDateTimeField(fieldName) {
    const dateTimeFields = [
      'created_at', 'updated_at', 'last_sync', 'last_seen',
      'last_login', 'last_used', 'start_time', 'end_time',
      'timestamp', 'date', 'time'
    ];
    return dateTimeFields.includes(fieldName) || fieldName.includes('_at') || fieldName.includes('_time');
  }

  // Check if field is a JSON field
  isJsonField(fieldName) {
    const jsonFields = ['settings', 'preferences', 'options', 'filters', 'details', 'metadata'];
    return jsonFields.includes(fieldName);
  }

  // Get table-specific JSON fields
  getTableJsonFields(table) {
    const tableJsonFields = {
      users: ['settings', 'preferences'],
      sync_tasks: ['filters', 'options'],
      sync_history: ['details']
    };
    return tableJsonFields[table] || [];
  }

  // Check if table has JSON fields
  hasJsonFields(table) {
    return this.getTableJsonFields(table).length > 0;
  }

  // Content-based comparison for tables with JSON fields
  compareDataContent(sqliteData, pgData, table) {
    if (sqliteData.length !== pgData.length) {
      return false;
    }

    // Sort both datasets by ID for consistent comparison
    const sortedSqlite = [...sqliteData].sort((a, b) => a.id - b.id);
    const sortedPg = [...pgData].sort((a, b) => a.id - b.id);

    // Compare each record
    for (let i = 0; i < sortedSqlite.length; i++) {
      if (!this.compareRecords(sortedSqlite[i], sortedPg[i], table)) {
        return false;
      }
    }

    return true;
  }

  // Compare two records with JSON field awareness
  compareRecords(record1, record2, table) {
    const jsonFields = this.getTableJsonFields(table);

    // Get all unique keys from both records
    const allKeys = new Set([...Object.keys(record1), ...Object.keys(record2)]);

    for (const key of allKeys) {
      const value1 = record1[key];
      const value2 = record2[key];

      if (jsonFields.includes(key)) {
        // Special comparison for JSON fields
        if (!this.compareJsonValues(value1, value2)) {
          return false;
        }
      } else if (this.isDateTimeField(key)) {
        // Special comparison for datetime fields
        if (!this.compareDateTimeValues(value1, value2)) {
          return false;
        }
      } else {
        // Standard comparison for other fields
        if (!this.compareStandardValues(value1, value2)) {
          return false;
        }
      }
    }

    return true;
  }

  // Normalize datetime values
  normalizeDateTimeValue(value) {
    if (!value) return null;

    try {
      // Convert to ISO string for consistent format
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        return null; // Invalid date
      }
      return date.toISOString();
    } catch (error) {
      console.warn('Failed to normalize datetime value:', value, error.message);
      return String(value); // Fallback to string
    }
  }

  // Normalize JSON values with enhanced logic
  normalizeJsonValue(value) {
    if (!value) return null;

    try {
      let parsedValue;

      // If it's already an object, use it directly
      if (typeof value === 'object') {
        parsedValue = value;
      }
      // If it's a string, try to parse it
      else if (typeof value === 'string') {
        // Handle various JSON string formats
        let cleanValue = value;

        // Handle double-quoted JSON strings like ""[]""
        if (cleanValue.startsWith('""') && cleanValue.endsWith('""')) {
          cleanValue = cleanValue.slice(2, -2).replace(/\\"/g, '"');
        }

        // Handle escaped JSON strings
        if (cleanValue.includes('\\"')) {
          cleanValue = cleanValue.replace(/\\"/g, '"');
        }

        // Try to parse the cleaned value
        parsedValue = JSON.parse(cleanValue);
      }
      else {
        return String(value);
      }

      // Normalize the parsed object by sorting keys recursively
      return this.deepNormalizeJson(parsedValue);

    } catch (error) {
      // If parsing fails, try to clean and return as consistent string
      if (typeof value === 'string') {
        // Remove extra quotes and normalize
        let cleanValue = value.replace(/^"+|"+$/g, '').replace(/\\"/g, '"');

        // Try one more time to parse
        try {
          const parsed = JSON.parse(cleanValue);
          return this.deepNormalizeJson(parsed);
        } catch (e) {
          // Return cleaned string if all parsing fails
          return cleanValue;
        }
      }
      return String(value);
    }
  }

  // Deep normalize JSON objects recursively
  deepNormalizeJson(value) {
    if (Array.isArray(value)) {
      // Sort array elements if they are primitives
      const normalized = value.map(item =>
        typeof item === 'object' ? this.deepNormalizeJson(item) : item
      );

      // Only sort if all elements are primitives (strings, numbers, etc.)
      if (normalized.every(item => typeof item !== 'object')) {
        return normalized.sort();
      }
      return normalized;

    } else if (value && typeof value === 'object') {
      // Sort object keys and recursively normalize values
      const sortedObj = {};
      Object.keys(value).sort().forEach(key => {
        sortedObj[key] = this.deepNormalizeJson(value[key]);
      });
      return sortedObj;
    }

    return value;
  }

  // Debug record for troubleshooting with table context
  debugRecord(record, table = null) {
    const debugRec = {};
    Object.keys(record).forEach(key => {
      let value = record[key];

      // Show type and value for debugging
      if (value === null || value === undefined) {
        debugRec[key] = `null`;
      } else if (this.isDateTimeField(key)) {
        debugRec[key] = `${typeof value}:"${value}" -> "${this.normalizeDateTimeValue(value)}"`;
      } else if (this.isJsonField(key) || (table && this.getTableJsonFields(table).includes(key))) {
        const normalized = this.normalizeValueForChecksum(key, value, table);
        debugRec[key] = `${typeof value}:"${JSON.stringify(value)}" -> "${normalized}"`;
      } else {
        debugRec[key] = `${typeof value}:"${value}" -> "${this.normalizeValueForChecksum(key, value, table)}"`;
      }
    });
    return debugRec;
  }

  // Helper: Parse JSON string with multiple format support
  parseJsonString(value) {
    let cleanValue = value;

    // Handle double-quoted JSON strings like ""[]""
    if (cleanValue.startsWith('""') && cleanValue.endsWith('""')) {
      cleanValue = cleanValue.slice(2, -2);
    }

    // Handle escaped quotes
    cleanValue = cleanValue.replace(/\\"/g, '"');

    // Try to parse
    return JSON.parse(cleanValue);
  }

  // Helper: Check if string looks like JSON
  looksLikeJson(value) {
    if (typeof value !== 'string') return false;
    const trimmed = value.trim();
    return (trimmed.startsWith('{') && trimmed.endsWith('}')) ||
           (trimmed.startsWith('[') && trimmed.endsWith(']')) ||
           (trimmed.startsWith('""') && trimmed.includes('{')) ||
           (trimmed.startsWith('""') && trimmed.includes('['));
  }

  // Helper: Create canonical JSON string
  canonicalJsonString(obj) {
    // Sort all keys recursively and create deterministic string
    const sortedObj = this.sortObjectKeysRecursively(obj);
    return JSON.stringify(sortedObj);
  }

  // Helper: Sort object keys recursively
  sortObjectKeysRecursively(obj) {
    if (Array.isArray(obj)) {
      return obj.map(item => this.sortObjectKeysRecursively(item));
    } else if (obj && typeof obj === 'object') {
      const sorted = {};
      Object.keys(obj).sort().forEach(key => {
        sorted[key] = this.sortObjectKeysRecursively(obj[key]);
      });
      return sorted;
    }
    return obj;
  }

  // Helper: Clean JSON string fallback
  cleanJsonString(value) {
    if (typeof value !== 'string') {
      return String(value);
    }

    // Remove extra quotes and normalize
    let cleaned = value.replace(/^"+|"+$/g, '').replace(/\\"/g, '"').trim();

    // If it still looks like JSON, try one more parse
    if (this.looksLikeJson(cleaned)) {
      try {
        const parsed = JSON.parse(cleaned);
        return this.canonicalJsonString(parsed);
      } catch (e) {
        // Final fallback
        return cleaned;
      }
    }

    return cleaned;
  }

  // Compare JSON values with normalization
  compareJsonValues(value1, value2) {
    try {
      const normalized1 = this.normalizeJsonForComparison(value1);
      const normalized2 = this.normalizeJsonForComparison(value2);
      return JSON.stringify(normalized1) === JSON.stringify(normalized2);
    } catch (error) {
      // Fallback to string comparison
      return String(value1) === String(value2);
    }
  }

  // Compare datetime values with normalization
  compareDateTimeValues(value1, value2) {
    try {
      const normalized1 = this.normalizeDateTimeValue(value1);
      const normalized2 = this.normalizeDateTimeValue(value2);
      return normalized1 === normalized2;
    } catch (error) {
      return String(value1) === String(value2);
    }
  }

  // Compare standard values
  compareStandardValues(value1, value2) {
    // Handle null/undefined
    if (value1 === null || value1 === undefined) {
      return value2 === null || value2 === undefined;
    }
    if (value2 === null || value2 === undefined) {
      return false;
    }

    // Handle numbers (including string numbers)
    if (typeof value1 === 'number' || typeof value2 === 'number') {
      return Number(value1) === Number(value2);
    }

    // Handle booleans
    if (typeof value1 === 'boolean' || typeof value2 === 'boolean') {
      return Boolean(value1) === Boolean(value2);
    }

    // Handle strings
    return String(value1).trim() === String(value2).trim();
  }

  // Normalize JSON for comparison
  normalizeJsonForComparison(value) {
    if (!value) return null;

    let parsed;
    if (typeof value === 'object') {
      parsed = value;
    } else if (typeof value === 'string') {
      // Handle various JSON string formats
      let cleanValue = value;
      if (cleanValue.startsWith('""') && cleanValue.endsWith('""')) {
        cleanValue = cleanValue.slice(2, -2).replace(/\\"/g, '"');
      }
      parsed = JSON.parse(cleanValue);
    } else {
      return value;
    }

    // Deep sort object keys
    return this.sortObjectKeysRecursively(parsed);
  }

  // Check if sync is needed based on data comparison
  async needsSync() {
    if (!this.config.enableDataComparison) {
      return true; // Always sync if comparison is disabled
    }

    try {
      const comparison = await this.compareData();
      return !comparison.isConsistent;
    } catch (error) {
      console.error('❌ Error checking if sync needed:', error);
      return true; // Sync on error to be safe
    }
  }

  // Perform consistency check
  async performConsistencyCheck() {
    console.log('🔍 Performing consistency check...');

    try {
      const comparison = await this.compareData();
      this.lastConsistencyCheck = new Date();

      this.emit('consistencyCheck', comparison);

      if (comparison.isConsistent) {
        console.log('✅ Data consistency verified - databases are in sync');
      } else {
        console.log('⚠️ Data inconsistency detected:');
        comparison.differences.forEach(diff => {
          console.log(`   - ${diff.table}: ${diff.issue} (SQLite: ${diff.sqliteCount}, PostgreSQL: ${diff.pgCount})`);
        });

        // Auto-fix if enabled
        if (this.config.enableAutoSync) {
          console.log('🔄 Auto-fixing inconsistency...');
          await this.performSync();
        }
      }

      return comparison;

    } catch (error) {
      console.error('❌ Consistency check failed:', error);
      this.emit('consistencyCheckFailed', error);
      throw error;
    }
  }

  // Get conflict resolution columns for each table
  getConflictColumns(table) {
    const conflictMap = {
      users: ['email'],
      sync_tasks: ['id'],
      sync_history: ['id']
    };

    return conflictMap[table] || ['id'];
  }

  // Get sync status
  getStatus() {
    return {
      isRunning: this.isRunning,
      lastSyncTime: this.lastSyncTime,
      config: this.config,
      connections: {
        sqlite: !!this.sqlite,
        postgresql: !!this.pgPool
      }
    };
  }

  // Update configuration
  updateConfig(newConfig) {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };
    
    console.log('⚙️ Database sync config updated:', newConfig);
    
    // Restart auto sync if interval changed
    if (this.isRunning && oldConfig.syncIntervalMinutes !== this.config.syncIntervalMinutes) {
      this.stopAutoSync();
      this.startAutoSync();
    }
    
    this.emit('configUpdated', this.config);
  }

  // Close connections
  async close() {
    console.log('🔌 Closing database sync connections...');
    
    this.stopAutoSync();
    
    if (this.sqlite) {
      this.sqlite.close();
      this.sqlite = null;
    }
    
    if (this.pgPool) {
      await this.pgPool.end();
      this.pgPool = null;
    }
    
    this.emit('closed');
    console.log('✅ Database sync connections closed');
  }
}

module.exports = DatabaseSyncService;
